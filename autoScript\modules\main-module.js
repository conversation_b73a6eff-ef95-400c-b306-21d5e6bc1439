/**
 * 主模块
 * 统一管理所有功能模块的初始化和执行
 */

const MainModule = (function () {
    // 私有变量
    let isRunning = false;
    let exitTime = 0; // 记录上次点击返回键的时间

    return {
        /**
         * 初始化应用
         */
        init: function () {
            try {
                console.log("开始初始化脚本助手应用...");

                // 初始化日志模块
                try {
                    if (typeof LogModule !== 'undefined') {
                        LogModule.init();
                    } else {
                        console.log("LogModule未加载，跳过初始化");
                    }
                } catch (e) {
                    console.error("LogModule初始化失败: " + e.message);
                    throw e;
                }

                // 初始化存储模块
                try {
                    if (typeof StorageModule !== 'undefined') {
                        StorageModule.init();
                    } else {
                        console.log("StorageModule未加载，跳过初始化");
                    }
                } catch (e) {
                    console.error("StorageModule初始化失败: " + e.message);
                    throw e;
                }

                // 初始化网络模块
                try {
                    if (typeof NetworkModule !== 'undefined') {
                        NetworkModule.init();
                    } else {
                        console.log("NetworkModule未加载，跳过初始化");
                    }
                } catch (e) {
                    console.error("NetworkModule初始化失败: " + e.message);
                    throw e;
                }

                // 初始化应用更新模块
                try {
                    if (typeof AppUpdateModule !== 'undefined') {
                        AppUpdateModule.init();
                    } else {
                        console.log("AppUpdateModule未加载，跳过初始化");
                    }
                } catch (e) {
                    console.error("AppUpdateModule初始化失败: " + e.message);
                    throw e;
                }

                // 初始化UI模块
                try {
                    if (typeof UIModule !== 'undefined') {
                        UIModule.init();
                    } else {
                        console.log("UIModule未加载，跳过初始化");
                    }
                } catch (e) {
                    console.error("UIModule初始化失败: " + e.message);
                    throw e;
                }

                // 设置应用运行状态
                isRunning = true;

                // 设置返回键处理
                this.setupBackKeyHandler();

                // 启动后自动检查更新
                this.autoCheckUpdate();

                if (typeof LogModule !== 'undefined') {
                    LogModule.log("脚本助手应用初始化完成");
                } else {
                    console.log("脚本助手应用初始化完成");
                }

            } catch (e) {
                console.error("应用初始化失败: " + e.message);
                throw e;
            }
        },

        /**
         * 设置返回键处理
         */
        setupBackKeyHandler: function () {
            try {
                ui.emitter.on("back_pressed", (e) => {
                    try {
                        let currentTime = Date.now();
                        
                        if (currentTime - exitTime > 2000) {
                            // 第一次按返回键
                            exitTime = currentTime;
                            
                            if (typeof UIModule !== 'undefined') {
                                UIModule.showToast("再按一次返回键退出应用");
                            } else {
                                toast("再按一次返回键退出应用");
                            }
                            
                            e.consumed = true; // 阻止默认的返回行为
                        } else {
                            // 两秒内再次按返回键，退出应用
                            this.stop();
                        }
                    } catch (error) {
                        if (typeof LogModule !== 'undefined') {
                            LogModule.log("返回键处理异常: " + error.message, "ERROR");
                        }
                    }
                });

                if (typeof LogModule !== 'undefined') {
                    LogModule.log("返回键处理设置完成");
                }
            } catch (e) {
                if (typeof LogModule !== 'undefined') {
                    LogModule.log("返回键处理设置失败: " + e.message, "ERROR");
                }
            }
        },

        /**
         * 自动检查更新
         */
        autoCheckUpdate: function () {
            try {
                if (typeof AppUpdateModule !== 'undefined') {
                    AppUpdateModule.autoCheckUpdate((error, updateInfo) => {
                        if (error) {
                            if (typeof LogModule !== 'undefined') {
                                LogModule.log("自动检查更新失败: " + error.message, "ERROR");
                            }
                            return;
                        }

                        if (updateInfo && updateInfo.hasUpdate) {
                            this.handleUpdateAvailable(updateInfo);
                        } else {
                            if (typeof LogModule !== 'undefined') {
                                LogModule.log("当前已是最新版本");
                            }
                        }
                    });
                } else {
                    if (typeof LogModule !== 'undefined') {
                        LogModule.log("AppUpdateModule未加载，跳过自动更新检查");
                    }
                }
            } catch (e) {
                if (typeof LogModule !== 'undefined') {
                    LogModule.log("自动检查更新异常: " + e.message, "ERROR");
                }
            }
        },

        /**
         * 处理发现更新
         * @param {Object} updateInfo - 更新信息
         */
        handleUpdateAvailable: function (updateInfo) {
            try {
                if (typeof LogModule !== 'undefined') {
                    LogModule.log("发现新版本: " + updateInfo.version);
                }

                // 显示更新对话框
                let updateMessage = "发现新版本 " + updateInfo.version + "\n\n";
                if (updateInfo.description) {
                    updateMessage += "更新内容:\n" + updateInfo.description + "\n\n";
                }
                updateMessage += "是否立即更新？";

                if (typeof UIModule !== 'undefined') {
                    UIModule.showConfirmDialog("发现新版本", updateMessage, (confirmed) => {
                        if (confirmed) {
                            this.startUpdate(updateInfo);
                        }
                    });
                } else {
                    dialogs.confirm("发现新版本", updateMessage).then(confirmed => {
                        if (confirmed) {
                            this.startUpdate(updateInfo);
                        }
                    });
                }

            } catch (e) {
                if (typeof LogModule !== 'undefined') {
                    LogModule.log("处理更新信息异常: " + e.message, "ERROR");
                }
            }
        },

        /**
         * 开始更新
         * @param {Object} updateInfo - 更新信息
         */
        startUpdate: function (updateInfo) {
            try {
                if (typeof LogModule !== 'undefined') {
                    LogModule.log("开始下载更新: " + updateInfo.version);
                }

                if (typeof UIModule !== 'undefined') {
                    UIModule.showToast("开始下载更新，请稍候...");
                } else {
                    toast("开始下载更新，请稍候...");
                }

                if (typeof AppUpdateModule !== 'undefined') {
                    // 获取设备信息
                    const deviceInfo = AppUpdateModule.getDeviceInfo();

                    // 下载更新
                    AppUpdateModule.downloadUpdate(updateInfo.version, deviceInfo.deviceId, (error, filePath) => {
                        if (error) {
                            if (typeof LogModule !== 'undefined') {
                                LogModule.log("下载更新失败: " + error.message, "ERROR");
                            }

                            if (typeof UIModule !== 'undefined') {
                                UIModule.showDialog("下载失败", "更新下载失败: " + error.message);
                            } else {
                                dialogs.alert("下载失败", "更新下载失败: " + error.message);
                            }
                            return;
                        }

                        // 下载成功，开始安装
                        this.installUpdate(filePath, updateInfo);
                    });
                } else {
                    if (typeof LogModule !== 'undefined') {
                        LogModule.log("AppUpdateModule未加载，无法下载更新", "ERROR");
                    }
                }

            } catch (e) {
                if (typeof LogModule !== 'undefined') {
                    LogModule.log("开始更新异常: " + e.message, "ERROR");
                }
            }
        },

        /**
         * 安装更新
         * @param {string} filePath - APK文件路径
         * @param {Object} updateInfo - 更新信息
         */
        installUpdate: function (filePath, updateInfo) {
            try {
                if (typeof LogModule !== 'undefined') {
                    LogModule.log("开始安装更新: " + filePath);
                }

                // 这里应该实现APK安装逻辑
                // 由于安装逻辑比较复杂，这里只提供基础框架
                
                if (typeof UIModule !== 'undefined') {
                    UIModule.showToast("正在安装更新...");
                } else {
                    toast("正在安装更新...");
                }

                // 尝试使用AutoX.js的app.install方法
                if (typeof app !== 'undefined' && typeof app.install === 'function') {
                    try {
                        app.install(filePath);
                        
                        if (typeof LogModule !== 'undefined') {
                            LogModule.log("安装命令已执行");
                        }
                        
                        if (typeof UIModule !== 'undefined') {
                            UIModule.showToast("请按照系统提示完成安装");
                        } else {
                            toast("请按照系统提示完成安装");
                        }
                    } catch (e) {
                        if (typeof LogModule !== 'undefined') {
                            LogModule.log("自动安装失败: " + e.message, "ERROR");
                        }
                        
                        // 提示用户手动安装
                        let message = "自动安装失败，请手动安装更新文件。\n\n文件位置: " + filePath;
                        
                        if (typeof UIModule !== 'undefined') {
                            UIModule.showDialog("需要手动安装", message);
                        } else {
                            dialogs.alert("需要手动安装", message);
                        }
                    }
                } else {
                    // 提示用户手动安装
                    let message = "请手动安装更新文件。\n\n文件位置: " + filePath;
                    
                    if (typeof UIModule !== 'undefined') {
                        UIModule.showDialog("需要手动安装", message);
                    } else {
                        dialogs.alert("需要手动安装", message);
                    }
                }

            } catch (e) {
                if (typeof LogModule !== 'undefined') {
                    LogModule.log("安装更新异常: " + e.message, "ERROR");
                }
            }
        },

        /**
         * 检查应用是否正在运行
         * @returns {boolean} 是否正在运行
         */
        isRunning: function () {
            return isRunning;
        },

        /**
         * 停止应用
         */
        stop: function () {
            try {
                if (isRunning) {
                    isRunning = false;
                    
                    if (typeof UIModule !== 'undefined') {
                        UIModule.showToast("脚本助手已停止");
                    } else {
                        toast("脚本助手已停止");
                    }
                    
                    if (typeof LogModule !== 'undefined') {
                        LogModule.log("应用已停止");
                    } else {
                        console.log("应用已停止");
                    }

                    // 退出应用
                    exit();
                }
            } catch (e) {
                if (typeof LogModule !== 'undefined') {
                    LogModule.log("停止应用异常: " + e.message, "ERROR");
                }
                console.error("停止应用异常: " + e.message);
            }
        }
    };
})();

// 导出模块（AutoX.js兼容方式）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MainModule;
} else {
    // AutoX.js环境 - 将模块添加到全局作用域
    global.MainModule = MainModule;
    if (typeof window !== 'undefined') {
        window.MainModule = MainModule;
    }
}
