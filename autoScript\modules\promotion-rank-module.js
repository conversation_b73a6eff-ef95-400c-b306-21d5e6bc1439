/**
 * 推广排名模块
 * 负责处理推广排名相关功能
 */

var PromotionRankModule = (function () {
    return {
        /**
         * 创建推广排名界面 - 完全按照原始main.js实现
         */
        createPromotionRankUI: function () {
            try {
                if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                    global.LogModule.log("开始创建推广排名界面", "INFO");
                }
                var that = this;

                // 创建推广排名UI布局
                var promotionRankLayoutXml =
                    '<frame>' +
                    '<vertical padding="10 5" bg="#FAFAFA">' +
                    '<horizontal gravity="center_vertical" padding="10">' +
                    '<img src="@android:drawable/ic_menu_revert" w="24" h="24" tint="#333333" id="promotionRankBackBtn"/>' +
                    '<text text="推广排名" textSize="18sp" textColor="#333333" gravity="center" layout_weight="1" textStyle="bold"/>' +
                    '<frame w="60dp" visibility="invisible"/>' +
                    '</horizontal>' +

                    '<card cardCornerRadius="10dp" cardElevation="2dp" margin="5 10">' +
                    '<vertical padding="15">' +
                    '<text text="我的排名" textSize="16sp" textColor="#333333" textStyle="bold"/>' +
                    '<text id="myRankText" text="加载中..." textSize="14sp" textColor="#666666" marginTop="8"/>' +
                    '</vertical>' +
                    '</card>' +

                    '<card cardCornerRadius="10dp" cardElevation="2dp" margin="5 10">' +
                    '<vertical padding="15">' +
                    '<text id="activityDescText" text="活动说明" textSize="16sp" textColor="#333333" textStyle="bold"/>' +
                    '<text id="activityDesc" text="加载中..." textSize="14sp" textColor="#666666" marginTop="8"/>' +
                    '</vertical>' +
                    '</card>' +

                    '<card cardCornerRadius="10dp" cardElevation="2dp" margin="5 10">' +
                    '<vertical padding="15">' +
                    '<text text="推广排行榜" textSize="16sp" textColor="#333333" textStyle="bold"/>' +
                    '<list id="rankList" h="*">' +
                    '<horizontal padding="10 8" w="*">' +
                    '<text text="{{rank}}" w="40" textSize="14sp" textColor="#333333"/>' +
                    '<text text="{{phoneMasked}}" layout_weight="1" textSize="14sp" textColor="#333333"/>' +
                    '<text text="{{totalReferrals}}人" w="60" textSize="14sp" textColor="#FF5722" gravity="right"/>' +
                    '</horizontal>' +
                    '</list>' +
                    '</vertical>' +
                    '</card>' +
                    '</vertical>' +
                    '</frame>';

                // 使用安全的UI创建方法
                this.safeCreateUI(promotionRankLayoutXml, function () {
                    try {
                        // 设置当前页面状态
                        if (typeof global !== 'undefined' && global.UIModule && global.UIModule.setCurrentPage) {
                            global.UIModule.setCurrentPage("promotionRank");
                        }

                        // 绑定事件
                        PromotionRankModule.bindEvents();

                        // 获取并显示排名数据
                        PromotionRankModule.fetchAndShowRankData();
                    } catch (e) {
                        console.error("推广排名界面事件绑定失败: " + e.message);
                        if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                            global.LogModule.log("推广排名界面事件绑定失败: " + e.message, "ERROR");
                        }
                    }
                });
            } catch (e) {
                console.error("创建推广排名界面失败: " + e.message);
                if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                    global.LogModule.log("创建推广排名界面失败: " + e.message, "ERROR");
                }
            }
        },

        /**
         * 安全创建UI的方法
         */
        safeCreateUI: function (layoutXml, callback) {
            try {
                if (typeof ui !== 'undefined' && ui.layout) {
                    ui.layout(layoutXml);
                    if (callback && typeof callback === 'function') {
                        // 延迟执行回调，确保UI已完全加载
                        setTimeout(callback, 100);
                    }
                } else {
                    console.error("UI对象不可用");
                    if (typeof toast !== 'undefined') {
                        toast("UI对象不可用");
                    }
                }
            } catch (e) {
                console.error("安全创建UI失败: " + e.message);
                if (typeof toast !== 'undefined') {
                    toast("创建界面失败: " + e.message);
                }
            }
        },

        /**
         * 绑定事件处理
         */
        bindEvents: function () {
            try {
                // 返回按钮点击事件
                if (ui.promotionRankBackBtn) {
                    ui.promotionRankBackBtn.on("click", function () {
                        // 直接显示"我的"页面，不经过脚本中心
                        if (typeof global !== 'undefined' && global.UIModule && global.UIModule.showMyPageDirectly) {
                            global.UIModule.showMyPageDirectly();
                        } else if (typeof global !== 'undefined' && global.UIModule && global.UIModule.returnToScriptCenterMyTab) {
                            // 降级方案：使用原来的方法
                            global.UIModule.returnToScriptCenterMyTab();
                        } else {
                            console.error("UIModule 导航方法不可用");
                            if (typeof toast !== 'undefined') {
                                toast("返回失败，请重试");
                            }
                        }
                        return true; // 阻止事件继续传播
                    });
                }

                // 添加物理返回键监听器
                PromotionRankModule.setupBackKeyHandler();

            } catch (e) {
                console.error("绑定推广排名事件失败: " + e.message);
            }
        },

        /**
         * 获取用户Token - 与其他模块保持一致的获取逻辑
         */
        getUserToken: function() {
            console.log("推广排名模块：开始获取用户Token...");

            // 优先从StorageModule获取Token（登录时保存的位置）
            if (typeof global !== 'undefined' && global.StorageModule) {
                console.log("推广排名模块：尝试从StorageModule获取Token...");
                var token = global.StorageModule.get("userToken");
                if (token) {
                    console.log("推广排名模块：从StorageModule获取到Token: " + token.substring(0, 10) + "...");
                    return token;
                } else {
                    console.log("推广排名模块：StorageModule中没有找到Token");
                }
            } else {
                console.log("推广排名模块：StorageModule不可用");
            }

            // 降级到ConfigModule
            if (typeof global !== 'undefined' && global.ConfigModule) {
                console.log("推广排名模块：尝试从ConfigModule获取Token...");
                var token = global.ConfigModule.get("userToken");
                if (token) {
                    console.log("推广排名模块：从ConfigModule获取到Token: " + token.substring(0, 10) + "...");
                    return token;
                } else {
                    console.log("推广排名模块：ConfigModule中没有找到Token");
                }
            } else {
                console.log("推广排名模块：ConfigModule不可用");
            }

            // 最后尝试从currentUser全局变量获取
            if (typeof currentUser !== 'undefined' && currentUser && currentUser.token) {
                console.log("推广排名模块：从currentUser获取到Token: " + currentUser.token.substring(0, 10) + "...");
                return currentUser.token;
            } else {
                console.log("推广排名模块：currentUser中没有Token");
            }

            console.warn("推广排名模块：未能获取到用户Token");
            return null;
        },

        /**
         * 设置物理返回键处理
         */
        setupBackKeyHandler: function () {
            try {
                // 监听返回键事件
                if (typeof ui !== 'undefined' && ui.emitter) {
                    ui.emitter.on("back_pressed", function (e) {
                        try {
                            // 检查当前是否在推广排名页面
                            if (typeof global !== 'undefined' && global.UIModule && global.UIModule.getCurrentPage) {
                                var currentPage = global.UIModule.getCurrentPage();
                                if (currentPage === "promotionRank") {
                                    // 阻止默认返回行为
                                    e.consumed = true;

                                    // 执行返回操作 - 直接显示"我的"页面
                                    if (global.UIModule.showMyPageDirectly) {
                                        global.UIModule.showMyPageDirectly();
                                    } else if (global.UIModule.returnToScriptCenterMyTab) {
                                        // 降级方案
                                        global.UIModule.returnToScriptCenterMyTab();
                                    } else {
                                        console.error("UIModule 导航方法不可用");
                                        if (typeof toast !== 'undefined') {
                                            toast("返回失败，请重试");
                                        }
                                    }
                                }
                            }
                        } catch (error) {
                            console.error("处理返回键事件失败: " + error.message);
                        }
                    });
                }

                // 监听按键事件（备用方案）
                if (typeof events !== 'undefined') {
                    events.on("key_down", function (keyCode, event) {
                        try {
                            if (keyCode === keys.back) {
                                // 检查当前是否在推广排名页面
                                if (typeof global !== 'undefined' && global.UIModule && global.UIModule.getCurrentPage) {
                                    var currentPage = global.UIModule.getCurrentPage();
                                    if (currentPage === "promotionRank") {
                                        // 执行返回操作 - 直接显示"我的"页面
                                        if (global.UIModule.showMyPageDirectly) {
                                            global.UIModule.showMyPageDirectly();
                                        } else if (global.UIModule.returnToScriptCenterMyTab) {
                                            global.UIModule.returnToScriptCenterMyTab();
                                        }
                                        return true; // 阻止默认行为
                                    }
                                }
                            }
                        } catch (error) {
                            console.error("处理按键事件失败: " + error.message);
                        }
                        return false;
                    });
                }

                console.log("推广排名页面返回键处理设置完成");
            } catch (e) {
                console.error("设置返回键处理失败: " + e.message);
            }
        },

        /**
         * 获取并显示排名数据
         */
        fetchAndShowRankData: function () {
            try {
                // 初始化排行榜数据
                if (ui.rankList) {
                    ui.rankList.setDataSource([]);
                }

                // 获取推广排名数据
                if (typeof global !== 'undefined' && global.NetworkModule && global.NetworkModule.getPromotionRank) {
                    global.NetworkModule.getPromotionRank(function (error, result) {
                        if (error) {
                            console.error("获取推广排名数据失败: " + error.message);
                            if (typeof toast !== 'undefined') {
                                toast("获取推广排名数据失败，请检查网络连接");
                            }
                            return;
                        }

                        if (result && result.code === 200 && result.data) {
                            var rankData = result.data;
                            PromotionRankModule.updateRankUI(rankData);
                        } else {
                            console.error("获取推广排名数据失败: " + (result ? result.message : "未知错误"));
                            if (typeof toast !== 'undefined') {
                                toast("获取推广排名数据失败: " + (result ? result.message : "未知错误"));
                            }
                        }
                    });
                } else {
                    console.error("NetworkModule 不可用");
                    // 使用默认数据
                    var defaultData = {
                        description: "推广排名活动说明",
                        startTime: "2024-01-01",
                        endTime: "2024-12-31",
                        userRank: null,
                        userInfo: {
                            totalReferrals: 0,
                            level1Count: 0,
                            level2Count: 0
                        },
                        rankList: []
                    };
                    PromotionRankModule.updateRankUI(defaultData);
                }
            } catch (e) {
                console.error("获取排名数据失败: " + e.message);
                if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                    global.LogModule.log("获取排名数据失败: " + e.message, "ERROR");
                }
            }
        },

        /**
         * 更新排名UI内容
         */
        updateRankUI: function (rankData) {
            try {
                // 更新UI内容
                if (typeof ui !== 'undefined' && ui.run) {
                    ui.run(function () {
                        try {
                            // 设置活动说明
                            if (ui.activityDesc) {
                                var activityInfo = "";
                                if (rankData.description) {
                                    activityInfo += rankData.description + "\n";
                                }
                                if (rankData.startTime && rankData.endTime) {
                                    activityInfo += "活动时间: " + rankData.startTime + " 至 " + rankData.endTime;
                                }

                                if (activityInfo) {
                                    ui.activityDesc.setText(activityInfo);
                                } else {
                                    ui.activityDesc.setText("暂无活动说明");
                                }
                            }

                            // 设置我的排名信息
                            if (ui.myRankText) {
                                var myRankInfo = "";
                                if (rankData.userRank && rankData.userRank <= 30) {
                                    myRankInfo = "当前排名: 第" + rankData.userRank + "名\n";
                                } else {
                                    myRankInfo = "当前排名: 未上榜\n";
                                }

                                if (rankData.userInfo) {
                                    myRankInfo += "推广总人数: " + rankData.userInfo.totalReferrals + "人\n";
                                    myRankInfo += "一级推广: " + rankData.userInfo.level1Count + "人\n";
                                    myRankInfo += "二级推广: " + rankData.userInfo.level2Count + "人";
                                } else {
                                    myRankInfo += "推广总人数: 0人\n";
                                    myRankInfo += "一级推广: 0人\n";
                                    myRankInfo += "二级推广: 0人";
                                }

                                ui.myRankText.setText(myRankInfo);
                            }

                            // 设置排行榜数据
                            if (rankData.rankList && rankData.rankList.length > 0) {
                                // 转换数据格式
                                var listData = [];
                                for (var i = 0; i < rankData.rankList.length; i++) {
                                    var item = rankData.rankList[i];
                                    listData.push({
                                        rank: (i + 1),
                                        phoneMasked: item.phoneMasked || "未知用户",
                                        totalReferrals: item.totalReferrals || 0
                                    });
                                }

                                // 更新列表数据
                                if (ui.rankList) {
                                    ui.rankList.setDataSource(listData);
                                }
                            } else {
                                // 没有排名数据时显示空列表
                                if (ui.rankList) {
                                    ui.rankList.setDataSource([]);
                                }
                            }
                        } catch (uiError) {
                            console.error("更新推广排名UI失败: " + uiError.message);
                        }
                    });
                }
            } catch (e) {
                console.error("更新排名UI失败: " + e.message);
            }
        }
    };
})();

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PromotionRankModule;
} else if (typeof global !== 'undefined') {
    global.PromotionRankModule = PromotionRankModule;
}
