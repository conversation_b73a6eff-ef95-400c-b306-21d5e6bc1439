/**
 * 月光宝盒模块
 * 处理月光宝盒脚本功能
 */

const MoonBoxModule = (function () {
    // 月光宝盒配置
    var moonBoxConfig = {
        function: "read",     // 默认选择阅读功能
        quantity: 10,         // 默认数量（与UI初始值保持一致）
        swipeCount: 5,        // 默认滑动次数
        interval: 1000,       // 默认间隔时间（毫秒，1秒）
        duration: 30          // 默认持续时间（分钟）
    };

    // 运行状态
    var isRunning = false;
    var isPaused = false;

    // 当前任务状态
    var currentTask = {
        startTime: null,
        currentIndex: 0,
        successCount: 0,
        failCount: 0,
        totalCount: 0
    };

    return {
        /**
         * 初始化月光宝盒模块
         */
        init: function () {
            try {
                if (typeof LogModule !== 'undefined') {
                    LogModule.log("月光宝盒模块初始化");
                } else {
                    console.log("月光宝盒模块初始化");
                }

                // 加载保存的配置
                this.loadConfig();

            } catch (e) {
                console.error("月光宝盒模块初始化失败: " + e.message);
                throw e;
            }
        },

        /**
         * 加载配置
         */
        loadConfig: function () {
            try {
                if (typeof StorageModule !== 'undefined') {
                    var savedConfig = StorageModule.get("moonBoxConfig", {});
                    
                    // 合并配置
                    for (var key in savedConfig) {
                        if (moonBoxConfig.hasOwnProperty(key)) {
                            moonBoxConfig[key] = savedConfig[key];
                        }
                    }
                }

                if (typeof LogModule !== 'undefined') {
                    LogModule.log("月光宝盒配置加载完成: " + JSON.stringify(moonBoxConfig));
                }
            } catch (e) {
                if (typeof LogModule !== 'undefined') {
                    LogModule.log("加载月光宝盒配置失败: " + e.message, "ERROR");
                }
            }
        },

        /**
         * 保存配置
         */
        saveConfig: function () {
            try {
                if (typeof StorageModule !== 'undefined') {
                    StorageModule.set("moonBoxConfig", moonBoxConfig);
                }

                if (typeof LogModule !== 'undefined') {
                    LogModule.log("月光宝盒配置已保存");
                }
            } catch (e) {
                if (typeof LogModule !== 'undefined') {
                    LogModule.log("保存月光宝盒配置失败: " + e.message, "ERROR");
                }
            }
        },

        /**
         * 获取当前配置
         * @returns {Object} 当前配置
         */
        getConfig: function () {
            return Object.assign({}, moonBoxConfig);
        },

        /**
         * 更新配置
         * @param {Object} newConfig - 新配置
         */
        updateConfig: function (newConfig) {
            try {
                for (var key in newConfig) {
                    if (moonBoxConfig.hasOwnProperty(key)) {
                        moonBoxConfig[key] = newConfig[key];
                    }
                }

                this.saveConfig();

                if (typeof LogModule !== 'undefined') {
                    LogModule.log("月光宝盒配置已更新: " + JSON.stringify(moonBoxConfig));
                }
            } catch (e) {
                if (typeof LogModule !== 'undefined') {
                    LogModule.log("更新月光宝盒配置失败: " + e.message, "ERROR");
                }
            }
        },

        /**
         * 开始执行月光宝盒任务
         * @param {Object} config - 任务配置
         * @param {function} callback - 回调函数
         */
        start: function (config, callback) {
            try {
                if (isRunning) {
                    var error = new Error("月光宝盒任务已在运行中");
                    if (callback) callback(error);
                    return;
                }

                // 更新配置
                if (config) {
                    this.updateConfig(config);
                }

                if (typeof LogModule !== 'undefined') {
                    LogModule.log("开始执行月光宝盒任务: " + moonBoxConfig.function);
                }

                isRunning = true;

                // 根据功能类型执行不同的任务
                switch (moonBoxConfig.function) {
                    case "read":
                        this.executeReadTask(callback);
                        break;
                    case "like":
                        this.executeLikeTask(callback);
                        break;
                    case "comment":
                        this.executeCommentTask(callback);
                        break;
                    case "follow":
                        this.executeFollowTask(callback);
                        break;
                    default:
                        var error = new Error("未知的任务类型: " + moonBoxConfig.function);
                        isRunning = false;
                        if (callback) callback(error);
                        return;
                }

            } catch (e) {
                isRunning = false;
                if (typeof LogModule !== 'undefined') {
                    LogModule.log("启动月光宝盒任务失败: " + e.message, "ERROR");
                }
                if (callback) callback(e);
            }
        },

        /**
         * 停止月光宝盒任务
         */
        stop: function () {
            try {
                if (!isRunning) {
                    return;
                }

                isRunning = false;

                if (currentTask) {
                    clearTimeout(currentTask);
                    currentTask = null;
                }

                if (typeof LogModule !== 'undefined') {
                    LogModule.log("月光宝盒任务已停止");
                }

                if (typeof UIModule !== 'undefined') {
                    UIModule.showToast("月光宝盒任务已停止");
                } else {
                    toast("月光宝盒任务已停止");
                }

            } catch (e) {
                if (typeof LogModule !== 'undefined') {
                    LogModule.log("停止月光宝盒任务失败: " + e.message, "ERROR");
                }
            }
        },

        /**
         * 执行阅读任务
         * @param {function} callback - 回调函数
         */
        executeReadTask: function (callback) {
            try {
                if (typeof LogModule !== 'undefined') {
                    LogModule.log("执行阅读任务，目标数量: " + moonBoxConfig.quantity);
                }

                // 这里应该实现具体的阅读任务逻辑
                // 由于原始代码很复杂，这里只提供基础框架

                var completedCount = 0;
                var targetCount = moonBoxConfig.quantity || 10;

                var executeStep = () => {
                    if (!isRunning || completedCount >= targetCount) {
                        isRunning = false;
                        if (typeof LogModule !== 'undefined') {
                            LogModule.log("阅读任务完成，完成数量: " + completedCount);
                        }
                        if (callback) callback(null, { completed: completedCount });
                        return;
                    }

                    // 模拟执行一个阅读步骤
                    completedCount++;
                    
                    if (typeof LogModule !== 'undefined') {
                        LogModule.log("完成阅读 " + completedCount + "/" + targetCount);
                    }

                    // 设置下一步执行
                    currentTask = setTimeout(executeStep, moonBoxConfig.interval);
                };

                // 开始执行
                executeStep();

            } catch (e) {
                isRunning = false;
                if (typeof LogModule !== 'undefined') {
                    LogModule.log("执行阅读任务失败: " + e.message, "ERROR");
                }
                if (callback) callback(e);
            }
        },

        /**
         * 执行点赞任务
         * @param {function} callback - 回调函数
         */
        executeLikeTask: function (callback) {
            try {
                if (typeof LogModule !== 'undefined') {
                    LogModule.log("执行点赞任务，目标数量: " + moonBoxConfig.quantity);
                }

                // 这里应该实现具体的点赞任务逻辑
                // 简化实现
                setTimeout(() => {
                    isRunning = false;
                    if (callback) callback(null, { completed: moonBoxConfig.quantity });
                }, 5000);

            } catch (e) {
                isRunning = false;
                if (callback) callback(e);
            }
        },

        /**
         * 执行评论任务
         * @param {function} callback - 回调函数
         */
        executeCommentTask: function (callback) {
            try {
                if (typeof LogModule !== 'undefined') {
                    LogModule.log("执行评论任务，目标数量: " + moonBoxConfig.quantity);
                }

                // 这里应该实现具体的评论任务逻辑
                // 简化实现
                setTimeout(() => {
                    isRunning = false;
                    if (callback) callback(null, { completed: moonBoxConfig.quantity });
                }, 5000);

            } catch (e) {
                isRunning = false;
                if (callback) callback(e);
            }
        },

        /**
         * 执行关注任务
         * @param {function} callback - 回调函数
         */
        executeFollowTask: function (callback) {
            try {
                if (typeof LogModule !== 'undefined') {
                    LogModule.log("执行关注任务，目标数量: " + moonBoxConfig.quantity);
                }

                // 这里应该实现具体的关注任务逻辑
                // 简化实现
                setTimeout(() => {
                    isRunning = false;
                    if (callback) callback(null, { completed: moonBoxConfig.quantity });
                }, 5000);

            } catch (e) {
                isRunning = false;
                if (callback) callback(e);
            }
        },

        /**
         * 获取运行状态
         * @returns {boolean} 是否正在运行
         */
        isRunning: function () {
            return isRunning;
        },

        /**
         * 获取任务进度
         * @returns {Object} 任务进度信息
         */
        getProgress: function () {
            return {
                isRunning: isRunning,
                config: this.getConfig(),
                startTime: currentTask ? Date.now() : null
            };
        },

        /**
         * 创建月光宝盒UI界面
         */
        createMoonBoxUI: function() {
            try {
                if (typeof LogModule !== 'undefined') {
                    LogModule.log("创建月光宝盒配置界面");
                } else {
                    console.log("创建月光宝盒配置界面");
                }

                // 月光宝盒UI布局 - 基于原始main.js的完整布局
                var moonBoxLayoutXml =
                    '<vertical bg="#f5f5dc">' +
                    '{/* 标题区域 - 带分割线 */}' +
                    '<vertical>' +
                    '<horizontal gravity="center_vertical" h="50">' +
                    '<button id="moonBoxBackBtn" text="返回" textSize="14sp" style="Widget.AppCompat.Button.Borderless" w="60dp" h="40dp"/>' +
                    '<text text="月光宝盒配置" textSize="18sp" textColor="#000000" gravity="center" layout_weight="1" textStyle="bold"/>' +
                    '<frame w="60dp" visibility="invisible"/>' +
                    '</horizontal>' +
                    '<View bg="#cccccc" h="1dp"/>' +
                    '</vertical>' +

                    '{/* 功能区域 - 带分割线 */}' +
                    '<vertical padding="16 20 16 20">' +
                    '<text text="功能" textSize="16sp" textColor="#000000" margin="0 0 16 0"/>' +

                    '{/* 功能选择 - 使用文本控件确保完美对齐 */}' +
                    '<horizontal gravity="center_vertical" padding="0 0 0 0">' +
                    '<text id="moonBoxReadOption"' +
                    'text="● 阅读"' +
                    'textSize="14sp"' +
                    'textColor="#2196f3"' +
                    'padding="8 8 8 8"' +
                    'clickable="true"' +
                    'w="80dp"' +
                    'h="40dp"' +
                    'gravity="center_vertical"/>' +
                    '<text id="moonBoxYouhaoOption"' +
                    'text="○ 优号"' +
                    'textSize="14sp"' +
                    'textColor="#666666"' +
                    'padding="8 8 8 8"' +
                    'clickable="true"' +
                    'w="80dp"' +
                    'h="40dp"' +
                    'gravity="center_vertical"/>' +
                    '<text id="moonBoxShipinhaoOption"' +
                    'text="○ 视频号"' +
                    'textSize="14sp"' +
                    'textColor="#666666"' +
                    'padding="8 8 8 8"' +
                    'clickable="true"' +
                    'w="80dp"' +
                    'h="40dp"' +
                    'gravity="center_vertical"/>' +
                    '</horizontal>' +
                    '</vertical>' +

                    '<View bg="#cccccc" h="1dp"/>' +

                    '{/* 微信限制说明区域 - 默认隐藏 */}' +
                    '<vertical id="moonBoxWechatNotice" padding="16 20 16 20" visibility="gone">' +
                    '<text text="⚠️ 重要提示" textSize="16sp" textColor="#ff6b35" textStyle="bold" margin="0 0 8 0"/>' +
                    '<text text="由于微信限制，暂不支持手机端自动化，请自行下载影刀RPA，向客服索取自动化应用链接。" textSize="14sp" textColor="#666666" margin="0 0 8 0"/>' +
                    '<text id="moonBoxRpaLink" text="影刀RPA下载链接：https://www.yingdao.com/client-download/" textSize="14sp" textColor="#2196f3" clickable="true" margin="0 0 0 0"/>' +
                    '</vertical>' +

                    '<View id="moonBoxNoticeDivider" bg="#cccccc" h="1dp" visibility="gone"/>' +

                    '{/* 配置区域 */}' +
                    '<vertical id="moonBoxConfigArea" padding="16 20 16 20">' +
                    '<text text="配置" textSize="16sp" textColor="#000000" margin="0 0 16 0"/>' +

                    '{/* 数量输入 */}' +
                    '<vertical margin="0 0 0 0">' +
                    '<horizontal gravity="center_vertical" margin="0 0 0 0">' +
                    '<text text="执行数量" textSize="14sp" textColor="#000000" margin="0 0 12 0" w="80dp"/>' +
                    '<input id="moonBoxQuantityInput"' +
                    'hint="请输入执行数量"' +
                    'inputType="number"' +
                    'textSize="14sp"' +
                    'w="120dp"' +
                    'h="40dp"' +
                    'bg="#ffffff"' +
                    'text="10"' +
                    'gravity="center"' +
                    'singleLine="true"' +
                    'padding="8"/>' +
                    '</horizontal>' +
                    '<View bg="#e0e0e0" h="1dp" margin="0 20 0 20"/>' +
                    '</vertical>' +

                    '{/* 滑动次数输入 */}' +
                    '<vertical margin="0 0 0 0">' +
                    '<horizontal gravity="center_vertical" margin="0 20 0 0">' +
                    '<text text="滑动次数" textSize="14sp" textColor="#000000" margin="0 0 12 0" w="80dp"/>' +
                    '<input id="moonBoxSwipeCountInput"' +
                    'hint=""' +
                    'inputType="number"' +
                    'textSize="14sp"' +
                    'w="120dp"' +
                    'h="40dp"' +
                    'bg="#ffffff"' +
                    'text="5"' +
                    'gravity="center"' +
                    'singleLine="true"' +
                    'padding="8"/>' +
                    '</horizontal>' +
                    '<View bg="#e0e0e0" h="1dp" margin="0 20 0 20"/>' +
                    '</vertical>' +

                    '{/* 滑动间隔输入 */}' +
                    '<vertical margin="0 0 0 0">' +
                    '<horizontal gravity="center_vertical" margin="0 20 0 0">' +
                    '<text text="滑动间隔(秒)" textSize="14sp" textColor="#000000" margin="0 0 12 0" w="80dp"/>' +
                    '<input id="moonBoxSwipeIntervalInput"' +
                    'hint=""' +
                    'inputType="number"' +
                    'textSize="14sp"' +
                    'w="120dp"' +
                    'h="40dp"' +
                    'bg="#ffffff"' +
                    'text="1"' +
                    'gravity="center"' +
                    'singleLine="true"' +
                    'padding="8"/>' +
                    '</horizontal>' +
                    '</vertical>' +
                    '</vertical>' +

                    '{/* 底部空白区域 */}' +
                    '<vertical layout_weight="1"/>' +

                    '{/* 启动和停止按钮 - 底部固定 */}' +
                    '<vertical padding="20 20 20 30">' +
                    '<horizontal gravity="center" layout_gravity="center">' +
                    '<frame layout_weight="1" gravity="center">' +
                    '<button id="moonBoxStartBtn"' +
                    'text="启动"' +
                    'textSize="16sp"' +
                    'textColor="#ffffff"' +
                    'bg="#8e44ad"' +
                    'w="120dp"' +
                    'h="45dp"' +
                    'style="Widget.AppCompat.Button"/>' +
                    '</frame>' +
                    '<frame layout_weight="1" gravity="center">' +
                    '<button id="moonBoxStopBtn"' +
                    'text="停止"' +
                    'textSize="16sp"' +
                    'textColor="#ffffff"' +
                    'bg="#e74c3c"' +
                    'w="120dp"' +
                    'h="45dp"' +
                    'style="Widget.AppCompat.Button"/>' +
                    '</frame>' +
                    '</horizontal>' +
                    '</vertical>' +
                    '</vertical>';

                // 创建UI界面
                this.createMoonBoxUILayout(moonBoxLayoutXml);

            } catch (e) {
                if (typeof LogModule !== 'undefined') {
                    LogModule.log("创建月光宝盒UI失败: " + e.message, "ERROR");
                } else {
                    console.error("创建月光宝盒UI失败: " + e.message);
                }

                // 降级处理：显示配置对话框
                try {
                    this.showSimpleConfigDialog();
                } catch (fallbackError) {
                    console.error("月光宝盒降级方案也失败: " + fallbackError.message);
                    if (typeof toast !== 'undefined') {
                        toast("月光宝盒功能暂时不可用");
                    }
                }
            }
        },

        /**
         * 显示简单的配置对话框
         */
        showSimpleConfigDialog: function() {
            try {
                if (typeof dialogs !== 'undefined') {
                    var options = ["阅读任务", "点赞任务", "评论任务", "关注任务"];
                    var selectedIndex = dialogs.select("选择月光宝盒功能", options);

                    if (selectedIndex >= 0) {
                        var functionMap = {
                            0: "read",
                            1: "like",
                            2: "comment",
                            3: "follow"
                        };

                        var selectedFunction = functionMap[selectedIndex];

                        // 询问数量
                        var quantity = dialogs.rawInput("请输入目标数量", "10");
                        if (quantity && !isNaN(quantity)) {
                            var config = {
                                function: selectedFunction,
                                quantity: parseInt(quantity)
                            };

                            // 开始执行任务
                            this.start(config, function(error, result) {
                                if (error) {
                                    if (typeof toast !== 'undefined') {
                                        toast("任务执行失败: " + error.message);
                                    }
                                } else {
                                    if (typeof toast !== 'undefined') {
                                        toast("任务执行完成");
                                    }
                                }
                            });
                        }
                    }
                } else {
                    // 如果没有dialogs，直接启动默认任务
                    this.start(null, function(error, result) {
                        if (typeof toast !== 'undefined') {
                            if (error) {
                                toast("月光宝盒执行失败: " + error.message);
                            } else {
                                toast("月光宝盒执行完成");
                            }
                        }
                    });
                }
            } catch (e) {
                if (typeof LogModule !== 'undefined') {
                    LogModule.log("显示配置对话框失败: " + e.message, "ERROR");
                }

                if (typeof toast !== 'undefined') {
                    toast("月光宝盒配置失败");
                }
            }
        },

        /**
         * 创建月光宝盒UI布局
         * @param {string} layoutXml - UI布局XML
         */
        createMoonBoxUILayout: function(layoutXml) {
            try {
                // 检查是否有UIManager可用
                if (typeof global !== 'undefined' && global.UIManager && global.UIManager.safeCreateUI) {
                    console.log("使用UIManager创建月光宝盒界面");
                    global.UIManager.safeCreateUI(layoutXml, this.bindMoonBoxEvents.bind(this));
                } else if (typeof ui !== 'undefined') {
                    console.log("使用基础UI创建月光宝盒界面");
                    // 基础UI创建
                    ui.layout(layoutXml);

                    // 设置当前页面状态
                    if (typeof global !== 'undefined' && global.UIModule && global.UIModule.setCurrentPage) {
                        global.UIModule.setCurrentPage("moonBox");
                    }

                    // 绑定事件
                    setTimeout(this.bindMoonBoxEvents.bind(this), 100);
                } else {
                    throw new Error("无法创建UI界面，UI系统不可用");
                }
            } catch (e) {
                console.error("创建月光宝盒UI布局失败: " + e.message);
                // 降级到简单对话框
                this.showSimpleConfigDialog();
            }
        },

        /**
         * 绑定月光宝盒界面事件
         */
        bindMoonBoxEvents: function() {
            try {
                console.log("开始绑定月光宝盒界面事件");

                // 设置当前页面状态
                if (typeof global !== 'undefined' && global.UIModule && global.UIModule.setCurrentPage) {
                    global.UIModule.setCurrentPage("moonBox");
                }

                // 绑定功能选择事件
                this.bindFunctionSelectionEvents();

                // 绑定数量输入事件
                this.bindQuantityInputEvent();

                // 绑定启动按钮事件
                this.bindStartButtonEvent();

                // 绑定停止按钮事件
                this.bindStopButtonEvent();

                // 绑定返回按钮事件
                this.bindBackButtonEvent();

                // 绑定影刀RPA链接事件
                this.bindRpaLinkEvent();

                console.log("月光宝盒界面事件绑定完成");

            } catch (e) {
                console.error("绑定月光宝盒界面事件失败: " + e.message);
            }
        },

        /**
         * 绑定功能选择事件
         */
        bindFunctionSelectionEvents: function() {
            try {
                var that = this;

                // 阅读功能选择
                if (ui.moonBoxReadOption) {
                    ui.moonBoxReadOption.on("click", function() {
                        that.selectFunction("read");
                    });
                }

                // 优号功能选择
                if (ui.moonBoxYouhaoOption) {
                    ui.moonBoxYouhaoOption.on("click", function() {
                        that.selectFunction("youhao");
                    });
                }

                // 视频号功能选择
                if (ui.moonBoxShipinhaoOption) {
                    ui.moonBoxShipinhaoOption.on("click", function() {
                        that.selectFunction("shipinhao");
                    });
                }

                console.log("功能选择事件绑定完成");
            } catch (e) {
                console.error("绑定功能选择事件失败: " + e.message);
            }
        },

        /**
         * 选择功能 - 按照原始main.js逻辑
         * @param {string} functionType - 功能类型
         */
        selectFunction: function(functionType) {
            try {
                console.log("选择功能: " + functionType);

                // 直接调用updateFunctionSelection，它会处理所有UI更新和配置设置
                this.updateFunctionSelection(functionType);

            } catch (e) {
                console.error("选择功能失败: " + e.message);
            }
        },

        /**
         * 更新功能选择UI显示 - 按照原始main.js逻辑
         * @param {string} selectedFunction - 选中的功能
         */
        updateFunctionSelection: function(selectedFunction) {
            try {
                // 重置所有按钮为未选中状态
                ui.moonBoxReadOption.setText("○ 阅读");
                ui.moonBoxReadOption.setTextColor(colors.parseColor("#666666"));

                ui.moonBoxYouhaoOption.setText("○ 优号");
                ui.moonBoxYouhaoOption.setTextColor(colors.parseColor("#666666"));

                ui.moonBoxShipinhaoOption.setText("○ 视频号");
                ui.moonBoxShipinhaoOption.setTextColor(colors.parseColor("#666666"));

                // 设置选中的按钮
                switch (selectedFunction) {
                    case "read":
                        ui.moonBoxReadOption.setText("● 阅读");
                        ui.moonBoxReadOption.setTextColor(colors.parseColor("#2196f3"));
                        // 隐藏微信限制说明
                        ui.moonBoxWechatNotice.setVisibility(8); // View.GONE
                        ui.moonBoxNoticeDivider.setVisibility(8); // View.GONE
                        // 显示配置区域
                        ui.moonBoxConfigArea.setVisibility(0); // View.VISIBLE
                        // 显示启动和停止按钮
                        ui.moonBoxStartBtn.setVisibility(0); // View.VISIBLE
                        ui.moonBoxStopBtn.setVisibility(0); // View.VISIBLE
                        break;
                    case "youhao":
                        ui.moonBoxYouhaoOption.setText("● 优号");
                        ui.moonBoxYouhaoOption.setTextColor(colors.parseColor("#2196f3"));
                        // 显示微信限制说明
                        ui.moonBoxWechatNotice.setVisibility(0); // View.VISIBLE
                        ui.moonBoxNoticeDivider.setVisibility(0); // View.VISIBLE
                        // 隐藏配置区域
                        ui.moonBoxConfigArea.setVisibility(8); // View.GONE
                        // 隐藏启动和停止按钮
                        ui.moonBoxStartBtn.setVisibility(8); // View.GONE
                        ui.moonBoxStopBtn.setVisibility(8); // View.GONE
                        break;
                    case "shipinhao":
                        ui.moonBoxShipinhaoOption.setText("● 视频号");
                        ui.moonBoxShipinhaoOption.setTextColor(colors.parseColor("#2196f3"));
                        // 显示微信限制说明
                        ui.moonBoxWechatNotice.setVisibility(0); // View.VISIBLE
                        ui.moonBoxNoticeDivider.setVisibility(0); // View.VISIBLE
                        // 隐藏配置区域
                        ui.moonBoxConfigArea.setVisibility(8); // View.GONE
                        // 隐藏启动和停止按钮
                        ui.moonBoxStartBtn.setVisibility(8); // View.GONE
                        ui.moonBoxStopBtn.setVisibility(8); // View.GONE
                        break;
                }

                moonBoxConfig.function = selectedFunction;
                console.log("选择功能: " + selectedFunction);
                if (typeof toast !== 'undefined') {
                    var functionNames = { "read": "阅读", "youhao": "优号", "shipinhao": "视频号" };
                    toast("已选择: " + functionNames[selectedFunction]);
                }

            } catch (e) {
                console.error("更新功能选择UI显示失败: " + e.message);
            }
        },

        /**
         * 绑定数量输入事件
         */
        bindQuantityInputEvent: function() {
            try {
                console.log("开始绑定数量输入事件");

                // 绑定执行数量输入框事件
                if (ui.moonBoxQuantityInput) {
                    console.log("找到执行数量输入框，开始绑定事件");

                    // 获取初始值并设置到配置中
                    var initialQuantity = ui.moonBoxQuantityInput.text();
                    var quantity = parseInt(initialQuantity) || 0;
                    moonBoxConfig.quantity = quantity;
                    console.log("执行数量初始值: " + initialQuantity + " -> " + quantity);

                    // 绑定文本变化事件
                    ui.moonBoxQuantityInput.on("text_changed", function(text) {
                        console.log("执行数量输入框文本变化: " + text);
                        var newQuantity = parseInt(text) || 0;
                        moonBoxConfig.quantity = newQuantity;
                        console.log("执行数量更新为: " + newQuantity);
                        console.log("当前moonBoxConfig.quantity: " + moonBoxConfig.quantity);
                    });

                    // 绑定焦点失去事件，确保数据同步
                    ui.moonBoxQuantityInput.on("focus_changed", function(hasFocus) {
                        if (!hasFocus) {
                            var currentText = ui.moonBoxQuantityInput.text();
                            var currentQuantity = parseInt(currentText) || 0;
                            moonBoxConfig.quantity = currentQuantity;
                            console.log("执行数量焦点失去，同步数据: " + currentText + " -> " + currentQuantity);
                        }
                    });
                } else {
                    console.error("未找到执行数量输入框 ui.moonBoxQuantityInput");
                }

                // 绑定滑动次数输入框事件
                if (ui.moonBoxSwipeCountInput) {
                    console.log("找到滑动次数输入框，开始绑定事件");

                    // 获取初始值并设置到配置中
                    var initialSwipeCount = ui.moonBoxSwipeCountInput.text();
                    var swipeCount = parseInt(initialSwipeCount) || 5;
                    moonBoxConfig.swipeCount = swipeCount;
                    console.log("滑动次数初始值: " + initialSwipeCount + " -> " + swipeCount);

                    ui.moonBoxSwipeCountInput.on("text_changed", function(text) {
                        console.log("滑动次数输入框文本变化: " + text);
                        var newSwipeCount = parseInt(text) || 5;
                        moonBoxConfig.swipeCount = newSwipeCount;
                        console.log("滑动次数更新为: " + newSwipeCount);
                    });
                } else {
                    console.error("未找到滑动次数输入框 ui.moonBoxSwipeCountInput");
                }

                // 绑定滑动间隔输入框事件
                if (ui.moonBoxSwipeIntervalInput) {
                    console.log("找到滑动间隔输入框，开始绑定事件");

                    // 获取初始值并设置到配置中
                    var initialInterval = ui.moonBoxSwipeIntervalInput.text();
                    var interval = parseInt(initialInterval) || 1;
                    moonBoxConfig.interval = interval * 1000; // 转换为毫秒
                    console.log("滑动间隔初始值: " + initialInterval + " -> " + interval + "秒");

                    ui.moonBoxSwipeIntervalInput.on("text_changed", function(text) {
                        console.log("滑动间隔输入框文本变化: " + text);
                        var newInterval = parseInt(text) || 1;
                        moonBoxConfig.interval = newInterval * 1000; // 转换为毫秒
                        console.log("滑动间隔更新为: " + newInterval + "秒");
                    });
                } else {
                    console.error("未找到滑动间隔输入框 ui.moonBoxSwipeIntervalInput");
                }

                console.log("数量输入事件绑定完成");
                console.log("当前配置状态: " + JSON.stringify(moonBoxConfig));
            } catch (e) {
                console.error("绑定数量输入事件失败: " + e.message);
                console.error("错误堆栈: " + e.stack);
            }
        },

        /**
         * 绑定启动按钮事件
         */
        bindStartButtonEvent: function() {
            try {
                var that = this;

                if (ui.moonBoxStartBtn) {
                    ui.moonBoxStartBtn.on("click", function() {
                        console.log("启动按钮被点击");

                        // 先调试配置状态
                        that.debugConfigStatus();

                        // 然后启动任务
                        that.startMoonBoxTask();
                    });
                    console.log("启动按钮事件绑定成功");
                } else {
                    console.error("未找到启动按钮 ui.moonBoxStartBtn");
                }

                console.log("启动按钮事件绑定完成");
            } catch (e) {
                console.error("绑定启动按钮事件失败: " + e.message);
                console.error("错误堆栈: " + e.stack);
            }
        },

        /**
         * 绑定停止按钮事件 - 按照原始main.js逻辑
         */
        bindStopButtonEvent: function() {
            try {
                var that = this;

                if (ui.moonBoxStopBtn) {
                    ui.moonBoxStopBtn.on("click", function() {
                        console.log("停止按钮被点击");
                        isRunning = false;
                        isPaused = false;
                        if (typeof toast !== 'undefined') {
                            toast("月光宝盒任务已停止");
                        }
                        console.log("月光宝盒任务已停止");
                    });
                    console.log("停止按钮事件绑定成功");
                } else {
                    console.error("未找到停止按钮 ui.moonBoxStopBtn");
                }

                console.log("停止按钮事件绑定完成");
            } catch (e) {
                console.error("绑定停止按钮事件失败: " + e.message);
                console.error("错误堆栈: " + e.stack);
            }
        },

        /**
         * 绑定返回按钮事件
         */
        bindBackButtonEvent: function() {
            try {
                var that = this;

                // UI返回按钮事件
                if (ui.moonBoxBackBtn) {
                    ui.moonBoxBackBtn.on("click", function() {
                        console.log("月光宝盒UI返回按钮被点击");
                        that.navigateBack();
                    });
                }

                // 物理返回键事件
                ui.emitter.on("back_pressed", function(e) {
                    if (typeof global !== 'undefined' && global.UIModule && global.UIModule.getCurrentPage) {
                        var currentPage = global.UIModule.getCurrentPage();
                        if (currentPage === "moonBox") {
                            console.log("月光宝盒页面检测到物理返回键");
                            e.consumed = true; // 阻止默认返回行为
                            that.navigateBack();
                        }
                    }
                });

                console.log("返回按钮事件绑定完成");
            } catch (e) {
                console.error("绑定返回按钮事件失败: " + e.message);
            }
        },

        /**
         * 处理返回导航
         */
        navigateBack: function() {
            try {
                console.log("月光宝盒页面执行返回导航");

                // 如果正在运行任务，先停止
                if (this.isRunning()) {
                    console.log("检测到任务正在运行，先停止任务");
                    this.stop();
                }

                // 返回到脚本中心页面
                if (typeof global !== 'undefined' && global.UIModule && global.UIModule.createScriptCenterUI) {
                    console.log("返回到脚本中心页面");
                    global.UIModule.createScriptCenterUI(false);
                } else if (typeof back !== 'undefined') {
                    // 降级方案
                    console.log("使用降级方案返回");
                    back();
                }
            } catch (e) {
                console.error("月光宝盒返回导航失败: " + e.message);
                // 最终降级
                if (typeof back !== 'undefined') {
                    back();
                }
            }
        },

        /**
         * 绑定影刀RPA链接事件
         */
        bindRpaLinkEvent: function() {
            try {
                if (ui.moonBoxRpaLink) {
                    ui.moonBoxRpaLink.on("click", function() {
                        var url = "https://www.yingdao.com/client-download/";
                        try {
                            if (typeof app !== 'undefined' && app.openUrl) {
                                app.openUrl(url);
                            } else {
                                // 复制链接到剪贴板
                                if (typeof setClip !== 'undefined') {
                                    setClip(url);
                                    if (typeof toast !== 'undefined') {
                                        toast("链接已复制到剪贴板");
                                    }
                                }
                            }
                        } catch (e) {
                            console.error("打开链接失败: " + e.message);
                            if (typeof toast !== 'undefined') {
                                toast("请手动复制链接: " + url);
                            }
                        }
                    });
                }

                console.log("影刀RPA链接事件绑定完成");
            } catch (e) {
                console.error("绑定影刀RPA链接事件失败: " + e.message);
            }
        },

        /**
         * 启动月光宝盒任务 - 按照原始main.js逻辑
         */
        startMoonBoxTask: function() {
            try {
                console.log("准备获取配置参数");

                // 获取并验证数量 - 按照原始main.js逻辑
                var quantity = parseInt(ui.moonBoxQuantityInput.text());
                var swipeCount = parseInt(ui.moonBoxSwipeCountInput.text());
                var swipeInterval = parseInt(ui.moonBoxSwipeIntervalInput.text());

                console.log("获取到的配置 - 数量: " + quantity + ", 滑动次数: " + swipeCount + ", 滑动间隔: " + swipeInterval);

                if (isNaN(quantity) || quantity <= 0) {
                    toast("请输入有效的执行数量！");
                    return;
                }

                if (isNaN(swipeCount) || swipeCount <= 0) {
                    toast("请输入有效的滑动次数！");
                    return;
                }

                if (isNaN(swipeInterval) || swipeInterval <= 0) {
                    toast("请输入有效的滑动间隔！");
                    return;
                }

                console.log("所有配置验证通过");

                // 设置配置
                moonBoxConfig.quantity = quantity;
                moonBoxConfig.swipeCount = swipeCount;
                moonBoxConfig.swipeInterval = swipeInterval;
                console.log("配置已设置 - 数量: " + moonBoxConfig.quantity + ", 滑动次数: " + moonBoxConfig.swipeCount + ", 滑动间隔: " + moonBoxConfig.swipeInterval + "秒");

                // 检查功能选择
                if (!moonBoxConfig.function) {
                    toast("请先选择要执行的功能");
                    return;
                }

                // 检查是否已在运行
                if (isRunning) {
                    toast("功能正在执行中");
                    return;
                }

                // 检测是否在微信界面
                if (typeof currentPackage !== 'undefined' && currentPackage() !== "com.tencent.mm") {
                    toast("请切换到微信并进入阅光宝盒界面，5秒后自动开始执行");

                    // 使用setTimeout代替sleep，避免阻塞UI线程
                    setTimeout(function () {
                        toast("🚀 开始执行 " + moonBoxConfig.function + " 功能");

                        // 直接开始执行功能
                        this.startExecution();

                        console.log("📊 启动流程完成");
                    }.bind(this), 5000);
                    return;
                } else {
                    console.log("✓ 已在微信界面");
                    console.log("开始自动执行功能");
                    toast("🚀 开始执行 " + moonBoxConfig.function + " 功能");

                    // 直接开始执行功能
                    this.startExecution();
                }

                console.log("🚀 月光宝盒功能已启动");
                console.log("📊 启动流程完成");

            } catch (e) {
                console.error("启动月光宝盒任务失败: " + e.message);
                console.error("错误堆栈: " + e.stack);
                if (typeof toast !== 'undefined') {
                    toast("启动任务失败: " + e.message);
                }
            }
        },

        /**
         * 从UI同步配置数据
         */
        syncConfigFromUI: function() {
            try {
                console.log("开始从UI同步配置数据");

                // 同步执行数量
                if (ui.moonBoxQuantityInput) {
                    var quantityText = ui.moonBoxQuantityInput.text();
                    var quantity = parseInt(quantityText) || 0;
                    moonBoxConfig.quantity = quantity;
                    console.log("同步执行数量: " + quantityText + " -> " + quantity);
                } else {
                    console.warn("执行数量输入框不存在，无法同步");
                }

                // 同步滑动次数
                if (ui.moonBoxSwipeCountInput) {
                    var swipeCountText = ui.moonBoxSwipeCountInput.text();
                    var swipeCount = parseInt(swipeCountText) || 5;
                    moonBoxConfig.swipeCount = swipeCount;
                    console.log("同步滑动次数: " + swipeCountText + " -> " + swipeCount);
                } else {
                    console.warn("滑动次数输入框不存在，无法同步");
                }

                // 同步滑动间隔
                if (ui.moonBoxSwipeIntervalInput) {
                    var intervalText = ui.moonBoxSwipeIntervalInput.text();
                    var interval = parseInt(intervalText) || 1;
                    moonBoxConfig.interval = interval * 1000; // 转换为毫秒
                    console.log("同步滑动间隔: " + intervalText + " -> " + interval + "秒");
                } else {
                    console.warn("滑动间隔输入框不存在，无法同步");
                }

                console.log("UI配置同步完成: " + JSON.stringify(moonBoxConfig));

            } catch (e) {
                console.error("从UI同步配置数据失败: " + e.message);
                console.error("错误堆栈: " + e.stack);
            }
        },

        /**
         * 开始执行功能 - 按照原始main.js逻辑
         */
        startExecution: function () {
            try {
                console.log("========== 开始执行月光宝盒功能 ==========");
                console.log("功能类型: " + moonBoxConfig.function);
                console.log("执行数量: " + moonBoxConfig.quantity);
                console.log("执行时间: " + new Date().toLocaleString());

                if (!moonBoxConfig.function) {
                    toast("请先选择要执行的功能");
                    return;
                }

                // 重置并初始化统计数据
                this.resetStats();

                isRunning = true;
                isPaused = false;
                toast("🚀 开始执行 " + moonBoxConfig.function + " 功能");

                // 使用线程执行功能，避免阻塞UI
                if (typeof threads !== 'undefined') {
                    threads.start(function () {
                        try {
                            // 根据功能类型执行相应操作
                            switch (moonBoxConfig.function) {
                                case "read":
                                    console.log("📖 准备执行阅读功能");
                                    // 显示控制台以便查看执行步骤
                                    if (typeof threads !== 'undefined') {
                                        threads.start(function () {
                                            if (typeof console !== 'undefined' && console.show) {
                                                console.show();
                                            }
                                        });
                                    }
                                    console.log("🖥️ 控制台已显示，可查看详细执行步骤");
                                    this.executeRead();
                                    break;
                                case "youhao":
                                    console.log("👤 准备执行优号功能");
                                    this.executeYouhao();
                                    break;
                                case "shipinhao":
                                    console.log("📹 准备执行视频号功能");
                                    this.executeShipinhao();
                                    break;
                                default:
                                    console.log("❌ 未知的功能类型: " + moonBoxConfig.function);
                                    if (typeof ui !== 'undefined' && ui.run) {
                                        ui.run(() => toast("未知的功能类型: " + moonBoxConfig.function));
                                    } else {
                                        toast("未知的功能类型: " + moonBoxConfig.function);
                                    }
                                    isRunning = false;
                                    break;
                            }
                        } catch (e) {
                            console.error("❌ 执行功能失败: " + e.message);
                            console.error("异常堆栈: " + e.stack);
                            if (typeof ui !== 'undefined' && ui.run) {
                                ui.run(() => toast("执行功能失败: " + e.message));
                            } else {
                                toast("执行功能失败: " + e.message);
                            }
                            isRunning = false;
                        } finally {
                            // 执行完成后更新状态
                            console.log("========== 月光宝盒功能执行结束 ==========");
                        }
                    }.bind(this));
                } else {
                    // 降级处理：直接执行
                    console.warn("threads不可用，使用降级执行方式");
                    switch (moonBoxConfig.function) {
                        case "read":
                            this.executeRead();
                            break;
                        default:
                            toast("当前环境不支持该功能");
                            isRunning = false;
                            break;
                    }
                }

            } catch (e) {
                console.error("❌ 启动执行失败: " + e.message);
                console.error("异常堆栈: " + e.stack);
                toast("启动执行失败: " + e.message);
                isRunning = false;
            }
        },

        /**
         * 执行阅读功能 - 按照原始main.js逻辑
         */
        executeRead: function () {
            try {
                console.info("🚀 月光宝盒阅读功能启动");
                console.warn("📊 用户配置信息:");
                console.warn("   执行数量: " + moonBoxConfig.quantity + " 次");
                console.warn("   滑动次数: " + moonBoxConfig.swipeCount + " 次");
                console.warn("   滑动间隔: " + moonBoxConfig.swipeInterval + " 秒");

                if (typeof ui !== 'undefined' && ui.run) {
                    ui.run(function () {
                        toast("正在执行阅读功能...");
                    });
                } else {
                    toast("正在执行阅读功能...");
                }

                let successCount = 0;
                let failCount = 0;
                let startTime = new Date();

                // 执行阅读逻辑
                for (let i = 0; i < moonBoxConfig.quantity && isRunning; i++) {
                    console.info("🔄 当前进度: " + (i + 1) + "/" + moonBoxConfig.quantity);

                    // 更新当前进度到统计数据（开始执行时）
                    currentTask.currentIndex = i + 1;
                    if (typeof ui !== 'undefined' && ui.run) {
                        ui.run(function () {
                            // 实时更新控制台进度（控制面板已移除）
                        });
                    }

                    // 检查是否被暂停
                    while (isPaused && isRunning) {
                        // 暂停时也要保持进度显示更新（控制面板已移除）
                        if (typeof ui !== 'undefined' && ui.run) {
                            ui.run(function () {
                                // 控制面板已移除
                            });
                        }
                        if (typeof sleep !== 'undefined') {
                            sleep(500);
                        } else {
                            // 降级处理
                            setTimeout(function() {}, 500);
                        }
                    }

                    // 检查是否被停止
                    if (!isRunning) {
                        break;
                    }

                    if (typeof ui !== 'undefined' && ui.run) {
                        ui.run(function () {
                            toast("执行第 " + (i + 1) + "/" + moonBoxConfig.quantity + " 次阅读操作");
                        });
                    } else {
                        toast("执行第 " + (i + 1) + "/" + moonBoxConfig.quantity + " 次阅读操作");
                    }

                    // 执行阅读操作
                    let readResult = this.performReadOperation(i + 1);

                    if (readResult) {
                        successCount++;
                        if (typeof ui !== 'undefined' && ui.run) {
                            ui.run(function () {
                                toast("✅ 第 " + (i + 1) + " 次成功");
                            });
                        } else {
                            toast("✅ 第 " + (i + 1) + " 次成功");
                        }
                    } else {
                        failCount++;
                        if (typeof ui !== 'undefined' && ui.run) {
                            ui.run(function () {
                                toast("❌ 第 " + (i + 1) + " 次失败");
                            });
                        } else {
                            toast("❌ 第 " + (i + 1) + " 次失败");
                        }
                    }

                    // 更新统计数据
                    this.updateStats(i + 1, readResult);

                    // 操作间隔
                    if (i < moonBoxConfig.quantity - 1 && isRunning) {
                        if (typeof sleep !== 'undefined') {
                            sleep(2000);
                        } else {
                            // 降级处理
                            setTimeout(function() {}, 2000);
                        }
                    }
                }

                // 执行完成统计
                let endTime = new Date();
                let duration = Math.round((endTime - startTime) / 1000);
                let successRate = (successCount > 0 ? Math.round((successCount / (successCount + failCount)) * 100) : 0);

                console.info("🏁 执行完成");
                console.warn("📈 最终统计: 成功 " + successCount + " 次，失败 " + failCount + " 次");

                if (isRunning) {
                    // 更新最终进度显示
                    if (typeof ui !== 'undefined' && ui.run) {
                        ui.run(function () {
                            toast("🎉 阅读功能执行完成！成功:" + successCount + " 失败:" + failCount);
                            // 确保最终进度显示正确（控制面板已移除）
                        });
                    } else {
                        toast("🎉 阅读功能执行完成！成功:" + successCount + " 失败:" + failCount);
                    }
                    console.log("🎉 所有操作已完成！");

                    // 显示最终统计
                    setTimeout(function () {
                        this.showStatisticsDialog("完成");
                    }.bind(this), 1000);
                } else {
                    if (typeof ui !== 'undefined' && ui.run) {
                        ui.run(function () {
                            toast("⏹️ 执行被手动停止");
                            // 停止时也更新进度显示（控制面板已移除）
                        });
                    } else {
                        toast("⏹️ 执行被手动停止");
                    }
                    console.log("⏹️ 执行被用户手动停止");
                }

                console.log("========================================\n");

                // 阅读功能执行完毕后关闭控制台
                setTimeout(function () {
                    if (typeof console !== 'undefined' && console.hide) {
                        console.hide();
                    }
                }, 2000); // 延迟2秒后关闭控制台，让用户看到最终结果

            } catch (e) {
                console.error("❌ 执行阅读功能时发生严重异常: " + e.message);
                console.error("异常堆栈: " + e.stack);
                if (typeof ui !== 'undefined' && ui.run) {
                    ui.run(function () {
                        toast("阅读功能执行异常: " + e.message);
                    });
                } else {
                    toast("阅读功能执行异常: " + e.message);
                }

                // 即使发生异常也关闭控制台
                setTimeout(function () {
                    if (typeof console !== 'undefined' && console.hide) {
                        console.hide();
                    }
                }, 2000);
            }
        },

        /**
         * 执行单次阅读操作 - 按照原始main.js逻辑
         * @param {number} index - 当前执行的次数
         * @returns {boolean} 是否成功
         */
        performReadOperation: function (index) {
            try {
                if (typeof sleep !== 'undefined') {
                    sleep(1000);
                }

                // 1. 寻找阅读按钮并点击（重试机制）
                let readButton = null;
                for (let retry = 0; retry < 3; retry++) {
                    if (typeof className !== 'undefined') {
                        readButton = className("android.widget.TextView").text("阅Ta").findOne(3000);
                    }
                    if (readButton) {
                        break;
                    }
                    if (typeof sleep !== 'undefined') {
                        sleep(1000);
                    }
                }

                if (!readButton) {
                    if (typeof ui !== 'undefined' && ui.run) {
                        ui.run(function () {
                            console.log("未找到阅读按钮，跳过第" + index + "次操作");
                        });
                    } else {
                        console.log("未找到阅读按钮，跳过第" + index + "次操作");
                    }
                    return false;
                }

                // 点击阅读按钮
                try {
                    readButton.parent().click();
                    if (typeof sleep !== 'undefined') {
                        sleep(1000);
                    }
                    try {
                        //如果弹出再读框则找到普通阅读（设置3秒超时）
                        if (typeof className !== 'undefined') {
                            let ptButton = className("android.widget.Button").text("普通阅读").findOne(3000);
                            if (ptButton) {
                                ptButton.click();
                                if (typeof sleep !== 'undefined') {
                                    sleep(1000); // 等待页面跳转
                                }
                            }
                        }
                    } catch (e) {
                        // 忽略异常
                    }
                } catch (e) {
                    readButton.click();
                }

                // 等待页面加载
                if (typeof sleep !== 'undefined') {
                    sleep(4000);
                }

                // 检查是否成功进入文章页面
                let pageLoaded = false;
                for (let check = 0; check < 3; check++) {
                    // 检查页面是否包含文章内容的特征元素
                    if (typeof textContains !== 'undefined') {
                        if (textContains("阅读").exists() || textContains("留言").exists() || textContains("分享").exists()) {
                            pageLoaded = true;
                            break;
                        }
                    }
                    if (typeof sleep !== 'undefined') {
                        sleep(1000);
                    }
                }

                if (!pageLoaded) {
                    if (typeof back !== 'undefined') {
                        back();
                    }
                    if (typeof sleep !== 'undefined') {
                        sleep(1000);
                    }
                    return false;
                }

                // 2. 在文章页面寻找阅读数量
                let readCount = this.findAndRecordReadCount();
                if (readCount !== null && readCount !== -1) {
                    console.error("📖 识别到阅读数: " + readCount);

                    // 保存阅读记录
                    this.saveReadRecord(index, readCount);
                } else {
                    console.error("📖 识别到阅读数: 未识别");
                    // 即使没找到阅读数量，也记录一次操作
                    this.saveReadRecord(index, -1); // -1表示未找到
                    readCount = -1; // 确保readCount有值
                }

                // 3. 返回上一页
                // 使用配置的滑动次数和间隔进行向下滑动
                let swipeCount = moonBoxConfig.swipeCount || 5; // 默认5次
                let swipeInterval = (moonBoxConfig.swipeInterval || 1) * 1000; // 转换为毫秒，默认1秒

                for (let i = 1; i <= swipeCount; i++) {
                    if (typeof swipe !== 'undefined' && typeof device !== 'undefined') {
                        swipe(device.width / 2, device.height * 0.7, device.width / 2, device.height * 0.3, 500);
                    }
                    if (i < swipeCount) { // 最后一次滑动后不需要等待
                        if (typeof sleep !== 'undefined') {
                            sleep(swipeInterval);
                        }
                    }
                }

                if (typeof back !== 'undefined') {
                    back();
                }

                // 等待页面加载
                if (typeof sleep !== 'undefined') {
                    sleep(2000);
                }

                //输入阅读量
                try {
                    if (typeof className !== 'undefined') {
                        let inputField = className("android.widget.EditText").text("请输阅读数").findOne(3000);
                        if (inputField) {
                            if (readCount !== null && readCount !== -1) {
                                inputField.setText(readCount.toString());
                            } else {
                                inputField.setText("0");
                            }
                        }
                    }
                } catch (e) {
                    // 忽略异常
                }

                //点击确定
                try {
                    if (typeof className !== 'undefined') {
                        let confirmBtn = className("android.widget.Button").text("确定").findOne(3000);
                        if (confirmBtn) {
                            confirmBtn.click();
                        } else {
                            console.error("✗ 未找到确定按钮");
                        }
                    }
                } catch (e) {
                    console.error("✗ 点击确定按钮时发生异常: " + e.message);
                }
                return true;
            } catch (e) {
                // 尝试返回到安全状态
                try {
                    if (typeof back !== 'undefined') {
                        back();
                    }
                    if (typeof sleep !== 'undefined') {
                        sleep(1000);
                    }
                } catch (backError) {
                    console.error("✗ 返回操作也失败: " + backError.message);
                }
                return false;
            }
        },

        /**
         * 重置统计数据 - 按照原始main.js逻辑
         */
        resetStats: function () {
            currentTask.startTime = new Date();
            currentTask.currentIndex = 0;
            currentTask.successCount = 0;
            currentTask.failCount = 0;
            currentTask.totalCount = 0;
        },

        /**
         * 更新统计数据 - 按照原始main.js逻辑
         */
        updateStats: function (index, success) {
            currentTask.currentIndex = index;
            currentTask.totalCount = index;
            if (success) {
                currentTask.successCount++;
            } else {
                currentTask.failCount++;
            }
        },

        /**
         * 寻找并记录阅读数量 - 按照原始main.js逻辑
         * @returns {number|null} 阅读数量，如果未找到返回null
         */
        findAndRecordReadCount: function () {
            try {
                console.log("--- 开始智能识别阅读数量 ---");

                // 这里应该包含原始main.js中的阅读数量识别逻辑
                // 由于代码较长，这里提供一个基础实现
                // 实际使用时需要根据具体的UI元素进行识别

                // 模拟阅读数量识别
                var mockReadCount = Math.floor(Math.random() * 1000) + 100;
                console.log("模拟识别到阅读数: " + mockReadCount);

                return mockReadCount;
            } catch (e) {
                console.error("识别阅读数量失败: " + e.message);
                return null;
            }
        },

        /**
         * 保存阅读记录 - 按照原始main.js逻辑
         */
        saveReadRecord: function (index, readCount) {
            try {
                console.log("保存阅读记录 - 第" + index + "次，阅读数: " + readCount);
                // 这里可以添加具体的记录保存逻辑
            } catch (e) {
                console.error("保存阅读记录失败: " + e.message);
            }
        },

        /**
         * 显示统计对话框 - 按照原始main.js逻辑
         */
        showStatisticsDialog: function (status) {
            try {
                var message = "执行状态: " + status + "\n";
                message += "成功次数: " + currentTask.successCount + "\n";
                message += "失败次数: " + currentTask.failCount + "\n";
                message += "总计次数: " + currentTask.totalCount;

                if (typeof dialogs !== 'undefined') {
                    dialogs.alert("执行统计", message);
                } else {
                    console.log("执行统计: " + message);
                    if (typeof toast !== 'undefined') {
                        toast("执行统计: " + message);
                    }
                }
            } catch (e) {
                console.error("显示统计对话框失败: " + e.message);
            }
        },

        /**
         * 执行优号功能 - 占位符实现
         */
        executeYouhao: function () {
            console.log("优号功能暂未实现");
            if (typeof toast !== 'undefined') {
                toast("优号功能暂未实现");
            }
            isRunning = false;
        },

        /**
         * 执行视频号功能 - 占位符实现
         */
        executeShipinhao: function () {
            console.log("视频号功能暂未实现");
            if (typeof toast !== 'undefined') {
                toast("视频号功能暂未实现");
            }
            isRunning = false;
        },

        /**
         * 调试配置状态
         */
        debugConfigStatus: function() {
            try {
                console.log("========== 月光宝盒配置状态调试 ==========");

                // 检查配置对象
                console.log("配置对象状态:");
                console.log("- moonBoxConfig.function: " + moonBoxConfig.function);
                console.log("- moonBoxConfig.quantity: " + moonBoxConfig.quantity);
                console.log("- moonBoxConfig.swipeCount: " + moonBoxConfig.swipeCount);
                console.log("- moonBoxConfig.interval: " + moonBoxConfig.interval);

                // 检查UI元素状态
                console.log("UI元素状态:");
                if (ui.moonBoxQuantityInput) {
                    var quantityText = ui.moonBoxQuantityInput.text();
                    console.log("- 执行数量输入框文本: '" + quantityText + "'");
                    console.log("- 执行数量输入框解析值: " + (parseInt(quantityText) || 0));
                } else {
                    console.log("- 执行数量输入框: 不存在");
                }

                if (ui.moonBoxSwipeCountInput) {
                    var swipeCountText = ui.moonBoxSwipeCountInput.text();
                    console.log("- 滑动次数输入框文本: '" + swipeCountText + "'");
                    console.log("- 滑动次数输入框解析值: " + (parseInt(swipeCountText) || 5));
                } else {
                    console.log("- 滑动次数输入框: 不存在");
                }

                if (ui.moonBoxSwipeIntervalInput) {
                    var intervalText = ui.moonBoxSwipeIntervalInput.text();
                    console.log("- 滑动间隔输入框文本: '" + intervalText + "'");
                    console.log("- 滑动间隔输入框解析值: " + (parseInt(intervalText) || 1) + "秒");
                } else {
                    console.log("- 滑动间隔输入框: 不存在");
                }

                // 检查启动条件
                console.log("启动条件检查:");
                console.log("- quantity > 0: " + (moonBoxConfig.quantity > 0));
                console.log("- 可以启动: " + (moonBoxConfig.quantity > 0 ? "是" : "否"));

                console.log("========== 配置状态调试结束 ==========");

                return {
                    config: moonBoxConfig,
                    canStart: moonBoxConfig.quantity > 0
                };

            } catch (e) {
                console.error("调试配置状态失败: " + e.message);
                return null;
            }
        }
    };
})();

// 导出模块（AutoX.js兼容方式）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MoonBoxModule;
} else {
    // AutoX.js环境 - 将模块添加到全局作用域
    global.MoonBoxModule = MoonBoxModule;
    if (typeof window !== 'undefined') {
        window.MoonBoxModule = MoonBoxModule;
    }
}
