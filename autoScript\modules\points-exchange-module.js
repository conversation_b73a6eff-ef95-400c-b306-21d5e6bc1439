/**
 * 积分兑换模块
 * 负责处理积分兑换相关功能
 */

var PointsExchangeModule = (function () {
    return {
        /**
         * 显示积分兑换页面 - 完全按照原始main.js实现
         */
        showPointsExchangePage: function () {
            try {
                console.log("开始显示积分兑换页面");

                // 创建积分兑换页面UI
                var pointsExchangeLayoutXml =
                    '<frame bg="#f5f5f5">' +
                    '<vertical padding="0" h="*">' +
                    '<horizontal gravity="center_vertical" h="50" bg="#ffffff">' +
                    '<button id="backFromExchangeBtn" text="返回" textSize="14sp" style="Widget.AppCompat.Button.Borderless" w="60dp" h="40dp"/>' +
                    '<text text="积分兑换" textSize="18sp" textColor="#333333" gravity="center" layout_weight="1" textStyle="bold"/>' +
                    '<frame w="60dp" visibility="invisible"/>' +
                    '</horizontal>' +

                    '<card cardCornerRadius="0dp" cardElevation="2dp" margin="0 10 0 0">' +
                    '<vertical padding="15 10">' +
                    '<horizontal>' +
                    '<text text="当前积分：" textSize="16sp" textColor="#333333"/>' +
                    '<text id="exchangeCurrentPointsText" text="0" textSize="16sp" textColor="#FF5722" textStyle="bold"/>' +
                    '</horizontal>' +
                    '</vertical>' +
                    '</card>' +

                    '<scroll layout_weight="1">' +
                    '<vertical id="cardListContainer" padding="0">' +
                    '<text text="加载中..." textSize="14sp" textColor="#999999" gravity="center" margin="0 50"/>' +
                    '</vertical>' +
                    '</scroll>' +
                    '</vertical>' +
                    '</frame>';

                // 使用安全的UI创建方法
                this.safeCreateUI(pointsExchangeLayoutXml, function () {
                    try {
                        // 设置当前页面状态
                        if (typeof global !== 'undefined' && global.UIModule && global.UIModule.setCurrentPage) {
                            global.UIModule.setCurrentPage("pointsExchange");
                        }

                        // 绑定事件
                        PointsExchangeModule.bindEvents();

                        // 加载用户积分数据
                        PointsExchangeModule.loadUserPointsData();

                        // 加载积分兑换配置
                        PointsExchangeModule.loadExchangeConfigs();

                    } catch (e) {
                        console.error("绑定积分兑换界面事件失败: " + e.message);
                        console.error(e.stack);
                    }
                });

                if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                    global.LogModule.log("积分兑换页面已显示");
                }
            } catch (e) {
                console.error("显示积分兑换页面失败: " + e.message);
                console.error(e.stack);
                if (typeof toast !== 'undefined') {
                    toast("显示积分兑换页面失败: " + e.message);
                }
            }
        },

        /**
         * 安全创建UI的方法
         */
        safeCreateUI: function (layoutXml, callback) {
            try {
                if (typeof ui !== 'undefined' && ui.layout) {
                    ui.layout(layoutXml);
                    if (callback && typeof callback === 'function') {
                        // 延迟执行回调，确保UI已完全加载
                        setTimeout(callback, 100);
                    }
                } else {
                    console.error("UI对象不可用");
                    if (typeof toast !== 'undefined') {
                        toast("UI对象不可用");
                    }
                }
            } catch (e) {
                console.error("安全创建UI失败: " + e.message);
                if (typeof toast !== 'undefined') {
                    toast("创建界面失败: " + e.message);
                }
            }
        },

        /**
         * 绑定事件处理
         */
        bindEvents: function () {
            try {
                // 绑定返回按钮事件
                if (ui.backFromExchangeBtn) {
                    ui.backFromExchangeBtn.on("click", function () {
                        console.log("积分兑换页面返回按钮被点击");
                        // 直接显示"我的"页面，不经过脚本中心
                        if (typeof global !== 'undefined' && global.UIModule && global.UIModule.showMyPageDirectly) {
                            global.UIModule.showMyPageDirectly();
                        } else if (typeof global !== 'undefined' && global.UIModule && global.UIModule.returnToScriptCenterMyTab) {
                            // 降级方案：使用原来的方法
                            global.UIModule.returnToScriptCenterMyTab();
                        } else {
                            console.error("UIModule 导航方法不可用");
                            if (typeof toast !== 'undefined') {
                                toast("返回失败，请重试");
                            }
                        }
                    });
                }

                // 添加物理返回键监听器
                PointsExchangeModule.setupBackKeyHandler();

            } catch (e) {
                console.error("绑定积分兑换事件失败: " + e.message);
            }
        },

        /**
         * 获取用户Token - 与其他模块保持一致的获取逻辑
         */
        getUserToken: function() {
            console.log("积分兑换模块：开始获取用户Token...");

            // 优先从StorageModule获取Token（登录时保存的位置）
            if (typeof global !== 'undefined' && global.StorageModule) {
                console.log("积分兑换模块：尝试从StorageModule获取Token...");
                var token = global.StorageModule.get("userToken");
                if (token) {
                    console.log("积分兑换模块：从StorageModule获取到Token: " + token.substring(0, 10) + "...");
                    return token;
                } else {
                    console.log("积分兑换模块：StorageModule中没有找到Token");
                }
            } else {
                console.log("积分兑换模块：StorageModule不可用");
            }

            // 降级到ConfigModule
            if (typeof global !== 'undefined' && global.ConfigModule) {
                console.log("积分兑换模块：尝试从ConfigModule获取Token...");
                var token = global.ConfigModule.get("userToken");
                if (token) {
                    console.log("积分兑换模块：从ConfigModule获取到Token: " + token.substring(0, 10) + "...");
                    return token;
                } else {
                    console.log("积分兑换模块：ConfigModule中没有找到Token");
                }
            } else {
                console.log("积分兑换模块：ConfigModule不可用");
            }

            // 最后尝试从currentUser全局变量获取
            if (typeof currentUser !== 'undefined' && currentUser && currentUser.token) {
                console.log("积分兑换模块：从currentUser获取到Token: " + currentUser.token.substring(0, 10) + "...");
                return currentUser.token;
            } else {
                console.log("积分兑换模块：currentUser中没有Token");
            }

            console.warn("积分兑换模块：未能获取到用户Token");
            return null;
        },

        /**
         * 获取当前用户积分余额
         */
        getCurrentUserPoints: function() {
            try {
                // 从UI中获取当前显示的积分
                var currentPointsText = ui.findById("exchangeCurrentPointsText");
                if (currentPointsText) {
                    var pointsStr = currentPointsText.getText().toString();
                    var points = parseInt(pointsStr) || 0;
                    console.log("从UI获取当前积分: " + points);
                    return points;
                }

                // 如果UI中没有，尝试从存储的用户数据中获取
                if (typeof global !== 'undefined' && global.StorageModule) {
                    var currentUser = global.StorageModule.get("currentUser");
                    if (currentUser && currentUser.points !== undefined) {
                        console.log("从存储用户数据获取积分: " + currentUser.points);
                        return parseInt(currentUser.points) || 0;
                    }
                }

                console.warn("无法获取当前用户积分，返回0");
                return 0;
            } catch (e) {
                console.error("获取当前用户积分失败: " + e.message);
                return 0;
            }
        },

        /**
         * 更新本地积分余额（兑换成功后）
         */
        updateLocalPointsBalance: function(consumedPoints) {
            try {
                console.log("更新本地积分余额，消耗积分: " + consumedPoints);

                // 1. 更新UI显示的积分
                var currentPointsText = ui.findById("exchangeCurrentPointsText");
                if (currentPointsText) {
                    var currentPoints = parseInt(currentPointsText.getText().toString()) || 0;
                    var newPoints = Math.max(0, currentPoints - consumedPoints);

                    ui.run(function() {
                        currentPointsText.setText(newPoints.toString());
                    });

                    console.log("UI积分已更新: " + currentPoints + " -> " + newPoints);
                }

                // 2. 更新存储的用户数据
                if (typeof global !== 'undefined' && global.StorageModule) {
                    var currentUser = global.StorageModule.get("currentUser");
                    if (currentUser) {
                        if (currentUser.points !== undefined) {
                            currentUser.points = Math.max(0, (currentUser.points || 0) - consumedPoints);
                        }
                        global.StorageModule.set("currentUser", currentUser);
                        console.log("存储的用户积分已更新");
                    }
                }

            } catch (e) {
                console.error("更新本地积分余额失败: " + e.message);
            }
        },

        /**
         * 设置物理返回键处理
         */
        setupBackKeyHandler: function () {
            try {
                // 监听返回键事件
                if (typeof ui !== 'undefined' && ui.emitter) {
                    ui.emitter.on("back_pressed", function (e) {
                        try {
                            // 检查当前是否在积分兑换页面
                            if (typeof global !== 'undefined' && global.UIModule && global.UIModule.getCurrentPage) {
                                var currentPage = global.UIModule.getCurrentPage();
                                if (currentPage === "pointsExchange") {
                                    // 阻止默认返回行为
                                    e.consumed = true;

                                    // 执行返回操作 - 直接显示"我的"页面
                                    if (global.UIModule.showMyPageDirectly) {
                                        global.UIModule.showMyPageDirectly();
                                    } else if (global.UIModule.returnToScriptCenterMyTab) {
                                        // 降级方案
                                        global.UIModule.returnToScriptCenterMyTab();
                                    } else {
                                        console.error("UIModule 导航方法不可用");
                                        if (typeof toast !== 'undefined') {
                                            toast("返回失败，请重试");
                                        }
                                    }
                                }
                            }
                        } catch (error) {
                            console.error("处理返回键事件失败: " + error.message);
                        }
                    });
                }

                // 监听按键事件（备用方案）
                if (typeof events !== 'undefined') {
                    events.on("key_down", function (keyCode, event) {
                        try {
                            if (keyCode === keys.back) {
                                // 检查当前是否在积分兑换页面
                                if (typeof global !== 'undefined' && global.UIModule && global.UIModule.getCurrentPage) {
                                    var currentPage = global.UIModule.getCurrentPage();
                                    if (currentPage === "pointsExchange") {
                                        // 执行返回操作 - 直接显示"我的"页面
                                        if (global.UIModule.showMyPageDirectly) {
                                            global.UIModule.showMyPageDirectly();
                                        } else if (global.UIModule.returnToScriptCenterMyTab) {
                                            global.UIModule.returnToScriptCenterMyTab();
                                        }
                                        return true; // 阻止默认行为
                                    }
                                }
                            }
                        } catch (error) {
                            console.error("处理按键事件失败: " + error.message);
                        }
                        return false;
                    });
                }

                console.log("积分兑换页面返回键处理设置完成");
            } catch (e) {
                console.error("设置返回键处理失败: " + e.message);
            }
        },

        /**
         * 加载用户积分数据
         */
        loadUserPointsData: function () {
            try {
                console.log("开始加载用户积分数据...");
                var userPhone = null;
                var token = this.getUserToken();

                console.log("积分兑换模块：获取到Token: " + (token ? token.substring(0, 10) + "..." : "null"));

                // 获取用户手机号 - 添加详细调试信息
                console.log("积分兑换模块：开始获取用户手机号...");

                if (typeof global !== 'undefined' && global.StorageModule) {
                    console.log("积分兑换模块：尝试从StorageModule获取手机号...");
                    userPhone = global.StorageModule.get("userPhone");
                    console.log("积分兑换模块：从StorageModule获取到手机号: " + userPhone);
                }

                if (!userPhone && typeof global !== 'undefined' && global.ConfigModule) {
                    console.log("积分兑换模块：尝试从ConfigModule获取手机号...");
                    userPhone = global.ConfigModule.get("userPhone");
                    console.log("积分兑换模块：从ConfigModule获取到手机号: " + userPhone);
                }

                if (!userPhone && typeof currentUser !== 'undefined' && currentUser && currentUser.phone) {
                    console.log("积分兑换模块：尝试从currentUser获取手机号...");
                    userPhone = currentUser.phone;
                    console.log("积分兑换模块：从currentUser获取到手机号: " + userPhone);
                }

                // 最终检查
                console.log("积分兑换模块：最终获取结果 - userPhone: " + userPhone + ", token: " + (token ? "存在" : "不存在"));

                if (!userPhone || !token) {
                    console.log("用户信息不完整，无法加载积分数据");
                    if (typeof toast !== 'undefined') {
                        toast("用户信息不完整，无法加载积分数据");
                    }
                    if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                        global.LogModule.log("加载积分数据失败：用户信息不完整", "ERROR");
                    }
                    return;
                }

                // 调用API获取积分数据
                if (typeof global !== 'undefined' && global.NetworkModule && global.NetworkModule.post) {
                    global.NetworkModule.post("/points/external/user", { phone: userPhone }, function (error, result) {
                        if (error) {
                            console.error("获取积分数据失败: " + error.message);
                            if (typeof toast !== 'undefined') {
                                toast("获取积分数据失败，请检查网络连接");
                            }
                            if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                                global.LogModule.log("获取积分数据失败: " + error.message, "ERROR");
                            }
                            return;
                        }

                        if (result && result.code === 200 && result.data) {
                            console.log("积分数据获取成功，开始更新UI");
                            PointsExchangeModule.updateUserPointsUI(result.data);
                        } else {
                            console.error("获取积分数据失败: " + (result ? result.message : "未知错误"));
                            if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                                global.LogModule.log("获取积分数据失败: " + (result ? result.message : "未知错误"), "ERROR");
                            }
                        }
                    }, token);
                } else {
                    console.error("NetworkModule 不可用");
                    // 使用默认数据
                    var defaultData = {
                        userPoints: {
                            points: 0
                        }
                    };
                    PointsExchangeModule.updateUserPointsUI(defaultData);
                }

            } catch (e) {
                console.error("加载用户积分数据失败: " + e.message);
                console.error(e.stack);
                if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                    global.LogModule.log("加载用户积分数据失败: " + e.message, "ERROR");
                }
            }
        },

        /**
         * 更新用户积分UI
         */
        updateUserPointsUI: function (pointsData) {
            try {
                // 更新UI - 使用ui.run确保在UI线程中执行
                if (typeof ui !== 'undefined' && ui.run) {
                    ui.run(function () {
                        try {
                            var userPoints = pointsData.userPoints || {};

                            // 更新当前积分
                            var currentPointsText = ui.findById("exchangeCurrentPointsText");
                            if (currentPointsText) {
                                currentPointsText.setText(userPoints.points ? userPoints.points.toString() : "0");
                            }

                            console.log("用户积分数据UI更新完成");
                        } catch (uiError) {
                            console.error("更新积分UI失败: " + uiError.message);
                            console.error(uiError.stack);
                            if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                                global.LogModule.log("更新积分UI失败: " + uiError.message, "ERROR");
                            }
                        }
                    });
                }
            } catch (e) {
                console.error("更新用户积分UI失败: " + e.message);
            }
        },

        /**
         * 加载积分兑换配置
         */
        loadExchangeConfigs: function () {
            try {
                console.log("开始加载积分兑换配置...");
                var token = null;

                // 获取用户token
                if (typeof global !== 'undefined' && global.ConfigModule && global.ConfigModule.get) {
                    token = global.StorageModule.get("userToken");
                }

                if (!token) {
                    console.log("用户未登录，无法加载兑换配置");
                    if (typeof toast !== 'undefined') {
                        toast("用户未登录，无法加载兑换配置");
                    }
                    if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                        global.LogModule.log("加载兑换配置失败：用户未登录", "ERROR");
                    }
                    return;
                }

                // 调用API获取积分配置
                if (typeof global !== 'undefined' && global.NetworkModule && global.NetworkModule.get) {
                    global.NetworkModule.get("/points/config/list", null, function (error, result) {
                        if (error) {
                            console.error("获取积分配置失败: " + error.message);
                            if (typeof toast !== 'undefined') {
                                toast("获取积分配置失败，请检查网络连接");
                            }
                            if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                                global.LogModule.log("获取积分配置失败: " + error.message, "ERROR");
                            }
                            return;
                        }

                        if (result && result.code === 200 && result.data) {
                            console.log("积分配置获取成功，开始更新UI");

                            // 过滤出兑换卡密相关的配置
                            var exchangeConfigs = result.data.filter(function (config) {
                                return config.configKey && config.configKey.startsWith("EXCHANGE_POINTS_TYPE_");
                            });

                            console.log("找到" + exchangeConfigs.length + "个兑换配置项");
                            PointsExchangeModule.updateExchangeConfigsUI(exchangeConfigs);
                        } else {
                            console.error("获取积分配置失败: " + (result ? result.message : "未知错误"));
                            if (typeof toast !== 'undefined') {
                                toast("获取积分配置失败: " + (result ? result.message : "未知错误"));
                            }
                            if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                                global.LogModule.log("获取积分配置失败: " + (result ? result.message : "未知错误"), "ERROR");
                            }
                        }
                    }, token);
                } else {
                    console.error("NetworkModule 不可用");
                    // 使用默认配置
                    var defaultConfigs = [];
                    PointsExchangeModule.updateExchangeConfigsUI(defaultConfigs);
                }

            } catch (e) {
                console.error("加载积分兑换配置失败: " + e.message);
                console.error(e.stack);
                if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                    global.LogModule.log("加载积分兑换配置失败: " + e.message, "ERROR");
                }
            }
        },

        /**
         * 更新积分兑换配置UI
         */
        updateExchangeConfigsUI: function (exchangeConfigs) {
            try {
                // 更新UI - 使用ui.run确保在UI线程中执行
                if (typeof ui !== 'undefined' && ui.run) {
                    ui.run(function () {
                        try {
                            var cardListContainer = ui.findById("cardListContainer");
                            if (!cardListContainer) {
                                console.error("找不到卡片列表容器");
                                return;
                            }

                            // 清空容器
                            cardListContainer.removeAllViews();

                            if (exchangeConfigs.length === 0) {
                                // 没有配置时显示提示
                                var emptyView = ui.inflate(
                                    '<text text="暂无可兑换的卡密" textSize="14sp" textColor="#999999" gravity="center" margin="0 50"/>',
                                    cardListContainer
                                );
                                cardListContainer.addView(emptyView);
                                return;
                            }

                            // 设置容器内边距
                            cardListContainer.setPadding(0, 0, 0, 0);

                            // 创建卡片列表
                            for (var i = 0; i < exchangeConfigs.length; i++) {
                                try {
                                    var config = exchangeConfigs[i];
                                    var keyType = config.configKey.replace("EXCHANGE_POINTS_TYPE_", "");
                                    var pointsRequired = config.configValue;
                                    var cardName = config.configName;
                                    var cardRemark = config.remark || "积分兑换卡密";

                                    // 尝试从remark字段中提取图片URL
                                    var imageUrl = null;
                                    try {
                                        // 检查remark是否包含http或https链接
                                        if (cardRemark && (cardRemark.includes("http://") || cardRemark.includes("https://"))) {
                                            // 简单提取URL，假设remark中包含完整URL
                                            var urlMatch = cardRemark.match(/(https?:\/\/[^\s]+)/g);
                                            if (urlMatch && urlMatch.length > 0) {
                                                imageUrl = urlMatch[0];
                                                console.log("从remark中提取到图片URL: " + imageUrl);
                                            } else {
                                                // 如果没有匹配到URL但包含http或https，尝试将整个remark作为URL
                                                imageUrl = cardRemark.trim();
                                                console.log("使用整个remark作为图片URL: " + imageUrl);
                                            }
                                        }
                                    } catch (e) {
                                        console.error("提取图片URL失败: " + e.message);
                                    }

                                    // 创建卡片视图
                                    var cardXml =
                                        '<card cardCornerRadius="8dp" cardElevation="2dp" margin="8 5" foreground="?selectableItemBackground">' +
                                        '<vertical padding="0">' +
                                        '<frame layout_gravity="center" margin="0" padding="0">' +
                                        '<img id="cardImg_' + keyType + '" src="@android:drawable/ic_menu_report_image" w="*" h="180dp" scaleType="centerCrop"/>' +
                                        '</frame>' +
                                        '<vertical padding="12 8">' +
                                        '<text text="' + cardName + '" textSize="16sp" textColor="#333333" textStyle="bold" gravity="center" margin="0 2"/>' +
                                        '<text text="所需积分: ' + pointsRequired + '" textSize="14sp" textColor="#FF5722" gravity="center" margin="0 5"/>' +
                                        '<button id="exchangeBtn_' + keyType + '" text="立即兑换" textSize="14sp" style="Widget.AppCompat.Button.Colored" margin="0 5 0 0"/>' +
                                        '</vertical>' +
                                        '</vertical>' +
                                        '</card>';

                                    var cardView = ui.inflate(cardXml, cardListContainer);
                                    cardListContainer.addView(cardView);

                                    // 存储卡片数据，用于后续兑换操作
                                    cardView.tag = {
                                        keyType: parseInt(keyType),
                                        pointsRequired: parseInt(pointsRequired),
                                        cardName: cardName,
                                        remark: cardRemark,
                                        imageUrl: imageUrl
                                    };

                                    // 如果有图片URL，加载图片
                                    if (imageUrl) {
                                        PointsExchangeModule.loadCardImage(keyType, imageUrl);
                                    }

                                    // 绑定兑换按钮点击事件
                                    PointsExchangeModule.bindExchangeButton(keyType, cardView.tag);

                                    console.log("已添加卡片: " + cardName);
                                } catch (cardError) {
                                    console.error("添加卡片失败: " + cardError.message);
                                }
                            }

                            console.log("积分兑换配置UI更新完成");
                        } catch (uiError) {
                            console.error("更新积分兑换UI失败: " + uiError.message);
                            console.error(uiError.stack);
                            if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                                global.LogModule.log("更新积分兑换UI失败: " + uiError.message, "ERROR");
                            }

                            // 显示错误提示
                            var cardListContainer = ui.findById("cardListContainer");
                            if (cardListContainer) {
                                var errorView = ui.inflate(
                                    '<text text="加载兑换配置失败，请重试" textSize="14sp" textColor="#F44336" gravity="center" margin="0 50"/>',
                                    cardListContainer
                                );
                                cardListContainer.removeAllViews();
                                cardListContainer.addView(errorView);
                            }
                        }
                    });
                }
            } catch (e) {
                console.error("更新积分兑换配置UI失败: " + e.message);
            }
        },

        /**
         * 加载卡片图片
         */
        loadCardImage: function (keyType, imageUrl) {
            try {
                var imgId = "cardImg_" + keyType;
                var imgView = ui.findById(imgId);
                if (imgView) {
                    // 设置加载中的占位图
                    try {
                        // 设置加载中的占位图标
                        imgView.setImageResource(android.R.drawable.ic_menu_gallery);
                    } catch (e) {
                        console.error("设置占位图失败: " + e.message);
                    }

                    // 使用IIFE解决闭包问题，确保每个线程使用正确的URL和控件
                    (function (currentImgView, currentImgUrl) {
                        console.log("准备加载图片: " + currentImgUrl + " 到控件: " + imgId);

                        // 在子线程中加载图片，避免NetworkOnMainThreadException
                        if (typeof threads !== 'undefined') {
                            threads.start(function () {
                                try {
                                    console.log("开始加载图片: " + currentImgUrl);

                                    // 设置加载超时
                                    let loadingTimeout = setTimeout(function () {
                                        console.log("图片加载超时: " + currentImgUrl);
                                        ui.run(function () {
                                            try {
                                                currentImgView.setImageResource(android.R.drawable.ic_menu_gallery);
                                            } catch (e) {
                                                console.error("设置默认图片失败: " + e.message);
                                            }
                                        });
                                    }, 10000); // 10秒超时

                                    // 使用原生方法加载图片
                                    if (typeof images !== 'undefined' && images.load) {
                                        let imgWrapper = images.load(currentImgUrl);

                                        // 清除超时
                                        clearTimeout(loadingTimeout);

                                        if (imgWrapper) {
                                            // 将ImageWrapper转换为Bitmap
                                            let bitmap = imgWrapper.getBitmap();

                                            // 在UI线程中设置图片
                                            ui.run(function () {
                                                try {
                                                    currentImgView.setImageBitmap(bitmap);
                                                    console.log("图片加载成功: " + currentImgUrl);
                                                } catch (e) {
                                                    console.error("设置图片到UI失败: " + e.message);

                                                    // 加载失败时设置默认图标
                                                    try {
                                                        currentImgView.setImageResource(android.R.drawable.ic_menu_gallery);
                                                    } catch (defaultError) {
                                                        console.error("设置默认图标也失败: " + defaultError.message);
                                                    }
                                                }
                                            });
                                        } else {
                                            console.error("加载图片失败: 无法获取位图");

                                            // 在UI线程中设置默认图片
                                            ui.run(function () {
                                                try {
                                                    currentImgView.setImageResource(android.R.drawable.ic_menu_gallery);
                                                } catch (e) {
                                                    console.error("设置默认图片失败: " + e.message);
                                                }
                                            });
                                        }
                                    }
                                } catch (threadError) {
                                    console.error("子线程加载图片失败: " + threadError.message);
                                }
                            });
                        }
                    })(imgView, imageUrl);
                }
            } catch (e) {
                console.error("加载卡片图片失败: " + e.message);
            }
        },

        /**
         * 绑定兑换按钮事件
         */
        bindExchangeButton: function (keyType, cardData) {
            try {
                var btnId = "exchangeBtn_" + keyType;
                var exchangeBtn = ui.findById(btnId);
                if (exchangeBtn) {
                    console.log("找到兑换按钮: " + btnId);

                    // 使用直接绑定方式，避免闭包问题
                    (function (currentCardData) {
                        exchangeBtn.on("click", function () {
                            console.log("兑换按钮被点击: " + currentCardData.cardName);
                            console.log("兑换数据: " + JSON.stringify(currentCardData));
                            PointsExchangeModule.showExchangeConfirmDialog(currentCardData);
                        });
                    })(cardData);
                } else {
                    console.error("未找到兑换按钮: " + btnId);
                }
            } catch (e) {
                console.error("绑定兑换按钮事件失败: " + e.message);
            }
        },

        /**
         * 显示兑换确认对话框
         */
        showExchangeConfirmDialog: function (cardData) {
            try {
                console.log("显示兑换确认对话框: " + cardData.cardName);

                // 获取当前积分余额
                var currentPoints = PointsExchangeModule.getCurrentUserPoints();
                console.log("确认对话框 - 当前积分: " + currentPoints + ", 需要积分: " + cardData.pointsRequired);

                if (typeof dialogs !== 'undefined') {
                    var message = "🛒 确定要兑换 " + cardData.cardName + " 吗？\n\n" +
                        "💰 需要消耗积分: " + cardData.pointsRequired + "\n" +
                        "💎 您的当前积分: " + currentPoints + "\n";

                    if (currentPoints >= cardData.pointsRequired) {
                        var remainingPoints = currentPoints - cardData.pointsRequired;
                        message += "✅ 兑换后剩余: " + remainingPoints + " 积分";
                    } else {
                        var shortfall = cardData.pointsRequired - currentPoints;
                        message += "❌ 积分不足，还需要: " + shortfall + " 积分";
                    }

                    dialogs.confirm("积分兑换确认", message, function (confirmed) {
                        if (confirmed) {
                            PointsExchangeModule.performExchange(cardData);
                        }
                    });
                } else {
                    // 如果没有dialogs，直接执行兑换
                    if (typeof toast !== 'undefined') {
                        toast("正在兑换 " + cardData.cardName + "...");
                    }
                    PointsExchangeModule.performExchange(cardData);
                }
            } catch (e) {
                console.error("显示兑换确认对话框失败: " + e.message);
                if (typeof toast !== 'undefined') {
                    toast("兑换失败: " + e.message);
                }
            }
        },

        /**
         * 执行兑换操作
         */
        performExchange: function (cardData) {
            try {
                console.log("开始执行兑换操作: " + cardData.cardName);

                // 1. 预兑换验证 - 检查积分余额
                var currentPoints = PointsExchangeModule.getCurrentUserPoints();
                console.log("当前用户积分: " + currentPoints + ", 需要积分: " + cardData.pointsRequired);

                if (currentPoints < cardData.pointsRequired) {
                    var shortfall = cardData.pointsRequired - currentPoints;
                    console.error("积分不足，无法兑换");
                    if (typeof toast !== 'undefined') {
                        toast("积分不足！还需要 " + shortfall + " 积分才能兑换");
                    }
                    if (typeof dialogs !== 'undefined') {
                        dialogs.alert("积分不足",
                            "您当前积分: " + currentPoints + "\n" +
                            "需要积分: " + cardData.pointsRequired + "\n" +
                            "还需要: " + shortfall + " 积分");
                    }
                    return;
                }

                var userPhone = null;
                var token = null;

                // 获取用户信息 - 使用标准化的token获取方式
                token = PointsExchangeModule.getUserToken();
                console.log("积分兑换模块：获取到Token: " + (token ? token.substring(0, 10) + "..." : "null"));

                // 获取用户手机号 - 添加详细调试信息
                console.log("积分兑换模块：开始获取用户手机号...");

                if (typeof global !== 'undefined' && global.StorageModule) {
                    userPhone = global.StorageModule.get("userPhone");
                }

                if (!userPhone && typeof global !== 'undefined' && global.ConfigModule) {
                    userPhone = global.ConfigModule.get("userPhone");
                }

                if (!userPhone && typeof currentUser !== 'undefined' && currentUser && currentUser.phone) {
                    userPhone = currentUser.phone;
                }

                if (!userPhone || !token) {
                    console.error("积分兑换模块：用户信息不完整，无法执行兑换");
                    if (typeof toast !== 'undefined') {
                        toast("用户信息不完整，请先登录");
                    }
                    return;
                }

                // 调用兑换API
                if (typeof global !== 'undefined' && global.NetworkModule && global.NetworkModule.post) {
                    console.log("userId: " + currentUser.id);
                    var exchangeData = {
                        phone: userPhone,
                        keyType: cardData.keyType,
                        userId: currentUser.id
                    };

                    global.NetworkModule.post("/points/exchange-card-key", exchangeData, function (error, result) {
                        if (error) {
                            console.error("兑换失败: " + error.message);
                            if (typeof toast !== 'undefined') {
                                toast("兑换失败: " + error.message);
                            }
                            return;
                        }

                        if (result && result.code === 200) {
                            console.log("兑换成功");

                            // 2. 兑换成功后的处理逻辑

                            // 2.1 更新本地积分余额
                            PointsExchangeModule.updateLocalPointsBalance(cardData.pointsRequired);

                            // 2.2 显示成功提示
                            if (typeof toast !== 'undefined') {
                                toast("兑换成功！卡密已发送到您的账户");
                            }

                            // 2.3 刷新积分数据显示
                            PointsExchangeModule.loadUserPointsData();

                            // 2.4 显示详细兑换结果
                            if (result.data && result.data.keyCode) {
                                PointsExchangeModule.showExchangeResult(cardData, result.data.keyCode);
                            } else {
                                // 即使没有返回卡密，也显示成功信息
                                PointsExchangeModule.showExchangeResult(cardData, "兑换成功，请查看您的账户");
                            }

                            // 2.5 记录兑换日志
                            if (typeof global !== 'undefined' && global.LogModule) {
                                global.LogModule.log("积分兑换成功: " + cardData.cardName + ", 消耗积分: " + cardData.pointsRequired, "INFO");
                            }

                        } else {
                            console.error("兑换失败: " + (result ? result.message : "未知错误"));
                            if (typeof toast !== 'undefined') {
                                toast("兑换失败: " + (result ? result.message : "未知错误"));
                            }

                            // 记录失败日志
                            if (typeof global !== 'undefined' && global.LogModule) {
                                global.LogModule.log("积分兑换失败: " + (result ? result.message : "未知错误"), "ERROR");
                            }
                        }
                    }, token);
                } else {
                    console.error("NetworkModule 不可用");
                    if (typeof toast !== 'undefined') {
                        toast("网络模块不可用，无法执行兑换");
                    }
                }

            } catch (e) {
                console.error("执行兑换操作失败: " + e.message);
                if (typeof toast !== 'undefined') {
                    toast("兑换失败: " + e.message);
                }
            }
        },

        /**
         * 显示兑换结果
         */
        showExchangeResult: function (cardData, cardKey) {
            try {
                console.log("显示兑换结果: " + cardData.cardName + ", 卡密: " + cardKey);

                // 获取当前积分余额
                var currentPoints = PointsExchangeModule.getCurrentUserPoints();

                if (typeof dialogs !== 'undefined' && cardKey && cardKey !== "兑换成功，请查看您的账户") {
                    // 创建自定义对话框，包含卡密和复制按钮
                    PointsExchangeModule.showCardKeyDialog(cardData, cardKey, currentPoints);
                } else if (typeof dialogs !== 'undefined') {
                    // 没有卡密时的简单对话框
                    var message = "🎉 兑换成功！\n\n" +
                        "📦 兑换物品: " + cardData.cardName + "\n" +
                        "💰 消耗积分: " + cardData.pointsRequired + "\n" +
                        "💎 剩余积分: " + currentPoints + "\n\n" +
                        "✅ 兑换成功，请查看您的账户获取详细信息";

                    dialogs.alert("兑换成功", message, function() {
                        console.log("兑换结果对话框已关闭");
                    });
                } else {
                    // 降级方案：使用toast显示
                    var toastMessage = "兑换成功！" + cardData.cardName;
                    if (cardKey && cardKey !== "兑换成功，请查看您的账户") {
                        toastMessage += " 卡密: " + cardKey;
                    }

                    if (typeof toast !== 'undefined') {
                        toast(toastMessage);
                    }
                }

                // 记录兑换结果
                console.log("兑换结果已显示 - 物品: " + cardData.cardName + ", 卡密: " + cardKey);

            } catch (e) {
                console.error("显示兑换结果失败: " + e.message);
                // 降级处理
                if (typeof toast !== 'undefined') {
                    toast("兑换成功！" + cardData.cardName);
                }
            }
        },

        /**
         * 显示包含卡密和复制按钮的对话框
         */
        showCardKeyDialog: function (cardData, cardKey, currentPoints) {
            try {
                console.log("显示卡密对话框: " + cardKey);

                // 使用简化的对话框方案，避免findViewById的兼容性问题
                var message = "🎉 兑换成功！\n\n" +
                    "📦 兑换物品: " + cardData.cardName + "\n" +
                    "💰 消耗积分: " + cardData.pointsRequired + "\n" +
                    "💎 剩余积分: " + currentPoints + "\n\n" +
                    "🔑 您的卡密: " + cardKey + "\n\n" +
                    "⚠️ 请妥善保管您的卡密\n\n" +
                    "是否复制卡密到剪贴板？";

                // 显示确认对话框，询问是否复制卡密
                dialogs.confirm("兑换成功", message, function(copyConfirmed) {
                    if (copyConfirmed) {
                        try {
                            // 复制卡密到剪贴板
                            if (typeof setClip !== 'undefined') {
                                setClip(cardKey);
                                toast("✅ 卡密已复制到剪贴板");
                                console.log("卡密已复制到剪贴板: " + cardKey);
                            } else {
                                // 降级方案：显示卡密供用户手动复制
                                toast("请手动复制卡密: " + cardKey);
                                console.log("剪贴板功能不可用，显示卡密供手动复制");
                            }
                        } catch (e) {
                            console.error("复制卡密失败: " + e.message);
                            toast("复制失败，请手动复制卡密");
                        }
                    }
                    console.log("兑换成功对话框已关闭");
                });

            } catch (e) {
                console.error("显示卡密对话框失败: " + e.message);

                // 降级方案：使用简单的alert对话框
                if (typeof dialogs !== 'undefined') {
                    var fallbackMessage = "🎉 兑换成功！\n\n" +
                        "📦 兑换物品: " + cardData.cardName + "\n" +
                        "💰 消耗积分: " + cardData.pointsRequired + "\n" +
                        "💎 剩余积分: " + currentPoints + "\n\n" +
                        "🔑 卡密: " + cardKey + "\n\n" +
                        "⚠️ 请妥善保管您的卡密";

                    dialogs.alert("兑换成功", fallbackMessage, function() {
                        // 尝试复制卡密
                        if (typeof setClip !== 'undefined') {
                            setClip(cardKey);
                            toast("卡密已复制到剪贴板");
                        }
                    });
                } else {
                    toast("兑换成功！卡密: " + cardKey);
                }
            }
        }
    };
})();

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PointsExchangeModule;
} else if (typeof global !== 'undefined') {
    global.PointsExchangeModule = PointsExchangeModule;
}
