/**
 * 存储模块
 * 负责处理应用的数据持久化功能
 */

var StorageModule = (function () {
    // 存储根目录
    var STORAGE_DIR = "/sdcard/脚本助手/storage";

    // 存储实例缓存
    var storageInstance = null;

    return {
        /**
         * 初始化存储模块
         */
        init: function () {
            try {
                // 确保存储目录存在
                if (!files.exists(STORAGE_DIR)) {
                    files.createWithDirs(STORAGE_DIR + "/temp.txt");
                    files.remove(STORAGE_DIR + "/temp.txt");
                }
                
                // 初始化存储实例
                storageInstance = storages.create("脚本助手");
                
                console.log("存储模块初始化完成");
            } catch (e) {
                console.error("存储模块初始化失败: " + e.message);
            }
        },

        /**
         * 获取存储实例
         */
        getStorage: function () {
            if (!storageInstance) {
                storageInstance = storages.create("脚本助手");
            }
            return storageInstance;
        },

        /**
         * 保存数据
         * @param {string} key - 键
         * @param {any} value - 值
         * @returns {boolean} 是否成功
         */
        set: function (key, value) {
            try {
                var storage = this.getStorage();

                // 如果是对象，转换为JSON字符串
                if (typeof value === 'object' && value !== null) {
                    value = JSON.stringify(value);
                }

                storage.put(key, value);
                return true;
            } catch (e) {
                console.error("保存数据失败: " + e.message);
                return false;
            }
        },

        /**
         * 获取数据
         * @param {string} key - 键
         * @param {any} defaultValue - 默认值
         * @returns {any} 值
         */
        get: function (key, defaultValue) {
            try {
                var storage = this.getStorage();
                var value = storage.get(key, defaultValue);
                
                // 尝试解析JSON
                if (typeof value === 'string' && (value.startsWith('{') || value.startsWith('['))) {
                    try {
                        return JSON.parse(value);
                    } catch (e) {
                        // 如果解析失败，返回原始字符串
                        return value;
                    }
                }
                
                return value;
            } catch (e) {
                console.error("获取数据失败: " + e.message);
                return defaultValue;
            }
        },

        /**
         * 删除数据
         * @param {string} key - 键
         * @returns {boolean} 是否成功
         */
        remove: function (key) {
            try {
                var storage = this.getStorage();
                storage.remove(key);
                return true;
            } catch (e) {
                console.error("删除数据失败: " + e.message);
                return false;
            }
        },

        /**
         * 检查键是否存在
         * @param {string} key - 键
         * @returns {boolean} 是否存在
         */
        contains: function (key) {
            try {
                var storage = this.getStorage();
                return storage.contains(key);
            } catch (e) {
                console.error("检查键存在性失败: " + e.message);
                return false;
            }
        },

        /**
         * 获取所有键
         * @returns {Array} 键数组
         */
        keys: function () {
            try {
                var storage = this.getStorage();
                return storage.keys() || [];
            } catch (e) {
                console.error("获取所有键失败: " + e.message);
                return [];
            }
        },

        /**
         * 清除所有数据
         * @returns {boolean} 是否成功
         */
        clear: function () {
            try {
                var storage = this.getStorage();
                storage.clear();
                return true;
            } catch (e) {
                console.error("清除数据失败: " + e.message);
                return false;
            }
        },

        /**
         * 保存到文件
         * @param {string} filename - 文件名
         * @param {any} data - 数据
         * @returns {boolean} 是否成功
         */
        saveToFile: function (filename, data) {
            try {
                var filePath = STORAGE_DIR + "/" + filename;
                var content = typeof data === 'string' ? data : JSON.stringify(data, null, 2);
                
                files.write(filePath, content);
                return true;
            } catch (e) {
                console.error("保存到文件失败: " + e.message);
                return false;
            }
        },

        /**
         * 从文件读取
         * @param {string} filename - 文件名
         * @param {any} defaultValue - 默认值
         * @returns {any} 数据
         */
        loadFromFile: function (filename, defaultValue) {
            try {
                var filePath = STORAGE_DIR + "/" + filename;

                if (!files.exists(filePath)) {
                    return defaultValue;
                }

                var content = files.read(filePath);
                
                // 尝试解析JSON
                if (content && (content.startsWith('{') || content.startsWith('['))) {
                    try {
                        return JSON.parse(content);
                    } catch (e) {
                        return content;
                    }
                }
                
                return content || defaultValue;
            } catch (e) {
                console.error("从文件读取失败: " + e.message);
                return defaultValue;
            }
        },

        /**
         * 删除文件
         * @param {string} filename - 文件名
         * @returns {boolean} 是否成功
         */
        deleteFile: function (filename) {
            try {
                var filePath = STORAGE_DIR + "/" + filename;
                
                if (files.exists(filePath)) {
                    files.remove(filePath);
                }
                
                return true;
            } catch (e) {
                console.error("删除文件失败: " + e.message);
                return false;
            }
        },

        /**
         * 获取存储统计信息
         * @returns {Object} 统计信息
         */
        getStats: function () {
            try {
                var keys = this.keys();
                var totalSize = 0;

                keys.forEach(function(key) {
                    var value = this.get(key);
                    if (typeof value === 'string') {
                        totalSize += value.length;
                    } else if (value) {
                        totalSize += JSON.stringify(value).length;
                    }
                });
                
                return {
                    keyCount: keys.length,
                    totalSize: totalSize,
                    storageDir: STORAGE_DIR
                };
            } catch (e) {
                console.error("获取存储统计失败: " + e.message);
                return {
                    keyCount: 0,
                    totalSize: 0,
                    storageDir: STORAGE_DIR
                };
            }
        }
    };
})();

// 导出模块（AutoX.js兼容方式）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = StorageModule;
} else {
    // AutoX.js环境 - 将模块添加到全局作用域
    global.StorageModule = StorageModule;
    if (typeof window !== 'undefined') {
        window.StorageModule = StorageModule;
    }
}
