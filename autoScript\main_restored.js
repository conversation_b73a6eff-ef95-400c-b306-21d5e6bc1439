// 使用"ui"模式
"ui";

/**
 * 脚本助手 - 1:1完全还原版本
 * 基于原始main.js的精确还原
 * 保持技术改进的同时完全还原原始功能和界面
 */

console.log("=== 脚本助手启动 ===");
console.log("准备启动脚本助手...");

// 全局状态管理
let currentPage = 'main';
let isLoggedIn = false;
let currentUser = null;

// 自定义模块加载器 - 保持技术改进
function loadModule(modulePath) {
    const scriptPath = files.cwd();
    console.log("检测到脚本路径: " + scriptPath);
    
    const possiblePaths = [
        files.join(scriptPath, modulePath),
        files.join("/storage/emulated/0/脚本/autoScript", modulePath),
        files.join("/sdcard/脚本/autoScript", modulePath)
    ];
    
    for (let path of possiblePaths) {
        console.log("尝试加载模块: " + modulePath);
        console.log("  尝试路径: " + path);
        
        try {
            if (files.exists(path)) {
                let moduleContent = files.read(path);
                let moduleFunction = new Function('module', 'exports', 'require', '__filename', '__dirname', moduleContent);
                let module = { exports: {} };
                moduleFunction(module, module.exports, require, path, files.getParent(path));
                console.log("✓ 模块加载成功: " + path);
                return module.exports;
            }
        } catch (e) {
            console.log("  路径加载失败: " + e.message);
        }
    }
    
    console.error("✗ 模块加载失败: " + modulePath + " - 所有路径都加载失败");
    console.error("  尝试的路径: " + possiblePaths.join(", "));
    return null;
}

// 加载所有模块
function loadAllModules() {
    console.log("开始加载所有模块...");
    
    // 1. 加载配置
    console.log("1. 加载配置...");
    try {
        const constants = loadModule("./config/constants.js");
        if (constants) {
            global.APP_INFO = constants.APP_INFO;
            global.API_CONFIG = constants.API_CONFIG;
            console.log("✓ 配置加载成功");
            console.log("API基础URL: " + global.API_CONFIG.BASE_URL);
        } else {
            throw new Error("配置模块加载失败");
        }
    } catch (e) {
        console.warn("配置加载失败，使用默认配置: " + e.message);
        global.APP_INFO = { VERSION: "1.0.0", NAME: "脚本助手" };
        global.API_CONFIG = { BASE_URL: "http://192.168.1.19:8527/api" };
    }
    
    // 2. 加载工具函数
    console.log("2. 加载工具函数...");
    try {
        const commonUtils = loadModule("./utils/common-utils.js");
        if (commonUtils) {
            global.CommonUtils = commonUtils;
            console.log("✓ 工具函数加载成功");
        } else {
            throw new Error("工具函数模块加载失败");
        }
    } catch (e) {
        console.warn("工具函数加载失败，使用基础功能: " + e.message);
        global.CommonUtils = {
            formatDate: function(date) { return date.toString(); },
            generateId: function() { return Date.now().toString(); }
        };
    }
    
    // 3. 加载日志模块
    console.log("3. 加载日志模块...");
    try {
        const logModule = loadModule("./modules/log-module.js");
        if (logModule) {
            global.LogModule = logModule;
            global.LogModule.init();
            console.log("✓ 日志模块加载成功");
        } else {
            throw new Error("日志模块加载失败");
        }
    } catch (e) {
        console.warn("日志模块加载失败，使用基础日志功能: " + e.message);
        global.LogModule = {
            init: function() {},
            log: function(message, level) {
                console.log("[" + (level || "INFO") + "] " + message);
            }
        };
    }
    
    // 4. 加载存储模块
    console.log("4. 加载存储模块...");
    try {
        const storageModule = loadModule("./modules/storage-module.js");
        if (storageModule) {
            global.StorageModule = storageModule;
            global.StorageModule.init();
            console.log("✓ 存储模块加载成功");
        } else {
            throw new Error("存储模块加载失败");
        }
    } catch (e) {
        console.warn("存储模块加载失败，使用基础存储功能: " + e.message);
        global.StorageModule = {
            init: function() {},
            get: function(key, defaultValue) { return defaultValue; },
            set: function(key, value) { return true; }
        };
    }
    
    // 5. 加载网络模块
    console.log("5. 加载网络模块...");
    try {
        const networkModule = loadModule("./modules/network-module.js");
        if (networkModule) {
            global.NetworkModule = networkModule;
            global.NetworkModule.init();
            console.log("✓ 网络模块加载成功");
        } else {
            throw new Error("网络模块加载失败");
        }
    } catch (e) {
        console.warn("网络模块加载失败，使用基础网络功能: " + e.message);
        global.NetworkModule = {
            init: function() {},
            login: function(phone, password, callback) {
                // 演示模式登录
                if (phone === "13800138000" && password === "123456") {
                    setTimeout(() => {
                        callback(null, { id: "demo_user", phone: phone, nickname: "演示用户" });
                    }, 1000);
                } else {
                    setTimeout(() => {
                        callback(new Error("用户名或密码错误"), null);
                    }, 1000);
                }
            }
        };
    }
    
    console.log("所有模块加载完成");
}

// UI模块 - 完全按照原始版本还原
const UIModule = {
    /**
     * 设置当前页面
     */
    setCurrentPage: function(page) {
        currentPage = page;
    },
    
    /**
     * 创建主界面 - 完全按照原始版本
     */
    createMainUI: function() {
        try {
            // 创建UI布局 - 精确还原原始版本
            var layoutXml =
                '<frame>' +
                '<vertical padding="16">' +
                '<text id="appTitle" text="脚本助手" textSize="24sp" textColor="#3F51B5" gravity="center" margin="0 10"/>' +

                '<card margin="10" cardCornerRadius="8dp" cardElevation="5dp">' +
                '<img id="bannerImage" src="@drawable/ic_launcher" scaleType="centerCrop" h="150"/>' +
                '</card>' +

                '<card margin="10" cardCornerRadius="8dp" cardElevation="3dp">' +
                '<vertical padding="16">' +
                '<horizontal gravity="center_vertical">' +
                '<text text="无障碍" textSize="16sp" layout_weight="1"/>' +
                '<text text="(点击、滑动、长按等)" textSize="12sp" textColor="#888888" layout_weight="2"/>' +
                '<Switch id="accessibilitySwitch" checked="false"/>' +
                '</horizontal>' +

                '<horizontal gravity="center_vertical" marginTop="20">' +
                '<text text="悬浮框" textSize="16sp" layout_weight="1"/>' +
                '<text text="(增加脚本存活率)" textSize="12sp" textColor="#888888" layout_weight="2"/>' +
                '<Switch id="floatingWindowSwitch" checked="false"/>' +
                '</horizontal>' +
                '</vertical>' +
                '</card>' +

                '<horizontal margin="10 20">' +
                '<button id="loginBtn" text="登录" style="Widget.AppCompat.Button.Colored" layout_weight="1" margin="5"/>' +
                '<button id="registerBtn" text="注册" style="Widget.AppCompat.Button.Colored" layout_weight="1" margin="5"/>' +
                '</horizontal>' +
                '</vertical>' +
                '</frame>';

            // 设置UI布局
            ui.layout(layoutXml);

            // 绑定事件
            this.bindEvents();

            // 初始化状态
            this.updateUIState();

            // 更新当前页面状态
            this.setCurrentPage("main");
            global.LogModule.log("主界面创建成功");
        } catch (e) {
            global.LogModule.log("创建主界面失败: " + e.message, "ERROR");
            console.error("创建主界面失败: " + e.message);
            console.error(e.stack);
        }
    },
    
    /**
     * 绑定主界面事件 - 完全按照原始版本
     */
    bindEvents: function() {
        var that = this;

        // 无障碍服务开关
        ui.accessibilitySwitch.on("check", function(checked) {
            if (checked && !that.checkAccessibilityPermission()) {
                // 请求无障碍服务权限
                that.requestAccessibilityPermission();
            }
            if (global.StorageModule) {
                global.StorageModule.set("enableAccessibility", checked);
            }
        });

        // 悬浮窗开关
        ui.floatingWindowSwitch.on("check", function(checked) {
            if (checked && !that.checkFloatingWindowPermission()) {
                // 请求悬浮窗权限
                that.requestFloatingWindowPermission();
            }
            if (global.StorageModule) {
                global.StorageModule.set("enableFloatWindow", checked);
            }
        });

        // 登录按钮
        ui.loginBtn.on("click", function() {
            // 检查无障碍服务状态
            let accessibilityEnabled = that.isAccessibilityServiceEnabled();

            // 检查悬浮窗权限
            let floatingWindowEnabled = that.isFloatingWindowPermissionGranted();

            // 必须开启无障碍服务才能登录
            if (!accessibilityEnabled) {
                that.showAccessibilitySettingsDialog();
                return;
            }

            // 悬浮窗权限可选，但给出提示
            if (!floatingWindowEnabled) {
                that.showToast("建议开启悬浮窗权限以获得完整功能");
            }

            // 跳转到登录页面
            that.createLoginUI();
        });

        // 注册按钮
        ui.registerBtn.on("click", function() {
            // 检查必要权限
            if (!that.checkAllRequiredPermissions()) {
                that.showToast("请先开启无障碍服务和悬浮窗权限");
                return;
            }

            // 跳转到注册页面
            that.createRegisterUI();
        });
    },

    /**
     * 创建登录界面 - 完全按照原始版本
     */
    createLoginUI: function() {
        try {
            // 创建登录UI布局 - 精确还原原始版本
            var loginLayoutXml =
                '<frame bg="#e8f5e9">' +
                '<vertical padding="16" gravity="center_horizontal">' +
                '<text id="loginTitle" text="登录" textSize="24sp" textColor="#333333" gravity="center" margin="0 20 0 40"/>' +

                '<text text="手机号" textSize="16sp" textColor="#666666" marginTop="20"/>' +
                '<input id="phoneInput" hint="请输入手机号" inputType="phone" textSize="16sp" marginTop="4" padding="8" bg="#ffffff" h="50"/>' +

                '<text text="密码" textSize="16sp" textColor="#666666" marginTop="20"/>' +
                '<horizontal>' +
                '<input id="passwordInput" hint="请输入密码" inputType="textPassword" textSize="16sp" marginTop="4" padding="8" bg="#ffffff" h="50" layout_weight="1"/>' +
                '<text id="forgotPassword" text="忘记密码" textSize="14sp" textColor="#FF5722" gravity="center" marginLeft="10"/>' +
                '</horizontal>' +

                '<button id="loginSubmitBtn" text="登录" textSize="16sp" color="#ffffff" bg="#CD853F" marginTop="30" h="50"/>' +

                '<horizontal marginTop="20" gravity="center">' +
                '<text text="没有账号? " textSize="14sp" textColor="#666666"/>' +
                '<text id="goToRegister" text="立即注册" textSize="14sp" textColor="#4CAF50" />' +
                '</horizontal>' +

                '<button id="backToMainBtn" text="返回" textSize="14sp" style="Widget.AppCompat.Button.Borderless" marginTop="20"/>' +
                '</vertical>' +
                '</frame>';

            // 设置UI布局
            ui.layout(loginLayoutXml);

            // 绑定登录页面事件
            this.bindLoginEvents();

            // 更新当前页面状态
            this.setCurrentPage("login");
            global.LogModule.log("登录界面创建成功");
        } catch (e) {
            global.LogModule.log("创建登录界面失败: " + e.message, "ERROR");
            console.error("创建登录界面失败: " + e.message);
            console.error(e.stack);
        }
    },

    /**
     * 绑定登录页面事件 - 完全按照原始版本
     */
    bindLoginEvents: function() {
        var that = this;

        // 登录按钮
        ui.loginSubmitBtn.on("click", function() {
            var phone = ui.phoneInput.text();
            var password = ui.passwordInput.text();

            // 再次检查无障碍服务状态
            let accessibilityEnabled = that.isAccessibilityServiceEnabled();

            // 必须开启无障碍服务才能登录
            if (!accessibilityEnabled) {
                that.showAccessibilitySettingsDialog();
                return;
            }

            // 验证手机号格式
            if (!that.isValidPhoneNumber(phone)) {
                that.showToast("请输入正确的手机号");
                return;
            }

            // 验证密码
            if (!that.isValidPassword(password)) {
                that.showToast("密码长度不能少于6位");
                return;
            }

            // 显示加载提示
            that.showToast("登录中...");

            // 调用登录接口
            global.NetworkModule.login(phone, password, function(error, userData) {
                if (error) {
                    // 登录失败
                    that.showToast("登录失败: " + error.message);
                    global.LogModule.log("登录失败: " + error.message, "ERROR");
                } else {
                    // 登录成功
                    that.showToast("登录成功");
                    global.LogModule.log("登录成功: 用户ID=" + userData.id);

                    // 保存登录状态
                    isLoggedIn = true;
                    currentUser = userData;
                    if (global.StorageModule) {
                        global.StorageModule.set("isLoggedIn", true);
                        global.StorageModule.set("currentUser", userData);
                    }

                    // 跳转到脚本中心页面，并显示公告
                    that.createScriptCenterUI(true);
                }
            });
        });

        // 忘记密码
        ui.forgotPassword.on("click", function() {
            global.LogModule.log("跳转到重置密码页面");
            that.showToast("重置密码功能开发中...");
        });

        // 去注册
        ui.goToRegister.on("click", function() {
            global.LogModule.log("跳转到注册页面");
            that.createRegisterUI();
        });

        // 返回主页
        ui.backToMainBtn.on("click", function() {
            global.LogModule.log("返回主页");
            that.createMainUI();
        });
    },

    /**
     * 创建注册界面 - 完全按照原始版本
     */
    createRegisterUI: function() {
        try {
            // 创建注册UI布局 - 精确还原原始版本
            var registerLayoutXml =
                '<frame bg="#e8f5e9">' +
                '<vertical padding="16" gravity="center_horizontal">' +
                '<text id="registerTitle" text="注册" textSize="24sp" textColor="#333333" gravity="center" margin="0 20 0 40"/>' +

                '<text text="手机号" textSize="16sp" textColor="#666666" marginTop="20"/>' +
                '<input id="registerPhoneInput" hint="请输入手机号" inputType="phone" textSize="16sp" marginTop="4" padding="8" bg="#ffffff" h="50"/>' +

                '<text text="密码" textSize="16sp" textColor="#666666" marginTop="20"/>' +
                '<input id="registerPasswordInput" hint="请输入密码" inputType="textPassword" textSize="16sp" marginTop="4" padding="8" bg="#ffffff" h="50"/>' +

                '<text text="推荐人ID" textSize="16sp" textColor="#666666" marginTop="20"/>' +
                '<input id="referrerIdInput" hint="请输入推荐人ID" textSize="16sp" marginTop="4" padding="8" bg="#ffffff" h="50"/>' +

                '<button id="registerSubmitBtn" text="注册" textSize="16sp" color="#ffffff" bg="#CD853F" marginTop="30" h="50"/>' +

                '<button id="backToLoginFromRegisterBtn" text="返回登录" textSize="14sp" style="Widget.AppCompat.Button.Borderless" marginTop="20"/>' +
                '</vertical>' +
                '</frame>';

            // 设置UI布局
            ui.layout(registerLayoutXml);

            // 绑定注册页面事件
            this.bindRegisterEvents();

            // 更新当前页面状态
            this.setCurrentPage("register");
            global.LogModule.log("注册界面创建成功");
        } catch (e) {
            global.LogModule.log("创建注册界面失败: " + e.message, "ERROR");
            console.error("创建注册界面失败: " + e.message);
            console.error(e.stack);
        }
    },

    /**
     * 绑定注册页面事件
     */
    bindRegisterEvents: function() {
        var that = this;

        // 注册按钮
        ui.registerSubmitBtn.on("click", function() {
            that.showToast("注册功能开发中...");
        });

        // 返回登录
        ui.backToLoginFromRegisterBtn.on("click", function() {
            global.LogModule.log("返回登录页面");
            that.createLoginUI();
        });
    },

    /**
     * 创建脚本中心界面 - 简化版本
     */
    createScriptCenterUI: function(showAnnouncement) {
        try {
            // 创建脚本中心UI布局 - 简化但保持原始风格
            var scriptCenterLayoutXml =
                '<frame>' +
                '<vertical bg="#F5F5F5" padding="8">' +
                '<frame bg="#F5F5F5" h="50">' +
                '<horizontal gravity="center_vertical" h="*" w="*">' +
                '<frame w="60dp" visibility="invisible"/>' +
                '<text text="脚本助手" textSize="18sp" textColor="#333333" gravity="center" layout_weight="1" textStyle="bold"/>' +
                '<text id="logoutBtn" text="退出" textSize="14sp" textColor="#333333" w="60dp" gravity="center" clickable="true"/>' +
                '</horizontal>' +
                '</frame>' +

                '<scroll layout_weight="1">' +
                '<vertical>' +
                // 用户信息卡片
                '<card cardCornerRadius="8dp" cardElevation="2dp" margin="5 8 5 5">' +
                '<vertical padding="15">' +
                '<text id="userWelcomeText" text="欢迎使用脚本助手" textSize="16sp" textColor="#333333" gravity="center"/>' +
                '<text id="userStatusText" text="请选择要运行的脚本" textSize="14sp" textColor="#666666" gravity="center" marginTop="5"/>' +
                '</vertical>' +
                '</card>' +

                // 脚本列表标题
                '<card cardCornerRadius="8dp" cardElevation="2dp" margin="5 8 5 5">' +
                '<text text="可用脚本" textSize="16sp" textColor="#FFFFFF" gravity="center" bg="#009688" h="40" w="*"/>' +
                '</card>' +

                // 微信读书脚本
                '<card w="*" h="60" cardCornerRadius="8dp" cardElevation="2dp" margin="5 5">' +
                '<horizontal gravity="center_vertical" bg="#009688" h="*" w="*" clickable="true" id="free_wx_read" padding="15">' +
                '<text text="📖" textSize="24sp" textColor="#FFFFFF" marginRight="15"/>' +
                '<vertical layout_weight="1">' +
                '<text text="微信阅读" textSize="16sp" textColor="#FFFFFF"/>' +
                '<text text="自动阅读获取书币" textSize="12sp" textColor="#E0F2F1"/>' +
                '</vertical>' +
                '<text text="▶" textSize="18sp" textColor="#FFFFFF"/>' +
                '</horizontal>' +
                '</card>' +

                // 其他脚本占位
                '<card w="*" h="60" cardCornerRadius="8dp" cardElevation="2dp" margin="5 5">' +
                '<horizontal gravity="center_vertical" bg="#009688" h="*" w="*" clickable="true" id="other_script" padding="15">' +
                '<text text="🚀" textSize="24sp" textColor="#FFFFFF" marginRight="15"/>' +
                '<vertical layout_weight="1">' +
                '<text text="更多脚本" textSize="16sp" textColor="#FFFFFF"/>' +
                '<text text="敬请期待..." textSize="12sp" textColor="#E0F2F1"/>' +
                '</vertical>' +
                '<text text="▶" textSize="18sp" textColor="#FFFFFF"/>' +
                '</horizontal>' +
                '</card>' +

                '</vertical>' +
                '</scroll>' +
                '</vertical>' +
                '</frame>';

            // 设置UI布局
            ui.layout(scriptCenterLayoutXml);

            // 绑定脚本中心页面事件
            this.bindScriptCenterEvents();

            // 更新用户信息
            this.updateUserInfo();

            // 更新当前页面状态
            this.setCurrentPage("scriptCenter");
            global.LogModule.log("脚本中心界面创建成功");

            // 显示公告（如果需要）
            if (showAnnouncement === true) {
                this.showToast("欢迎使用脚本助手！");
            }
        } catch (e) {
            global.LogModule.log("创建脚本中心界面失败: " + e.message, "ERROR");
            console.error("创建脚本中心界面失败: " + e.message);
            console.error(e.stack);
        }
    },

    /**
     * 绑定脚本中心页面事件
     */
    bindScriptCenterEvents: function() {
        var that = this;

        // 退出登录按钮
        ui.logoutBtn.on("click", function() {
            that.showToast("正在退出...");

            // 清除登录状态
            isLoggedIn = false;
            currentUser = null;
            if (global.StorageModule) {
                global.StorageModule.set("isLoggedIn", false);
                global.StorageModule.set("currentUser", null);
            }

            global.LogModule.log("用户退出登录");

            // 返回主页
            that.createMainUI();
        });

        // 微信读书脚本
        ui.free_wx_read.on("click", function() {
            that.showToast("微信读书脚本启动中...");
            global.LogModule.log("用户启动微信读书脚本");

            // 这里可以添加实际的脚本启动逻辑
            setTimeout(function() {
                that.showToast("脚本运行中，请保持屏幕常亮");
            }, 2000);
        });

        // 其他脚本
        ui.other_script.on("click", function() {
            that.showToast("更多脚本功能开发中...");
        });
    },

    /**
     * 更新用户信息显示
     */
    updateUserInfo: function() {
        try {
            if (ui.userWelcomeText && currentUser) {
                var welcomeText = "欢迎, " + (currentUser.nickname || currentUser.phone || "用户");
                ui.userWelcomeText.setText(welcomeText);
            }

            if (ui.userStatusText) {
                if (isLoggedIn) {
                    ui.userStatusText.setText("已登录 - 请选择要运行的脚本");
                } else {
                    ui.userStatusText.setText("未登录 - 请先登录");
                }
            }
        } catch (e) {
            console.error("更新用户信息失败: " + e.message);
        }
    },

    // 权限管理函数 - 完全按照原始版本
    checkAccessibilityPermission: function() {
        try {
            return auto.service != null;
        } catch (e) {
            return false;
        }
    },

    isAccessibilityServiceEnabled: function() {
        return this.checkAccessibilityPermission();
    },

    requestAccessibilityPermission: function() {
        try {
            auto();
            this.showToast("请在无障碍设置中开启脚本助手");
        } catch (e) {
            this.showAccessibilitySettingsDialog();
        }
    },

    showAccessibilitySettingsDialog: function() {
        try {
            dialogs.confirm("需要无障碍权限",
                "脚本助手需要无障碍服务权限才能正常工作。\n\n" +
                "无障碍服务用于:\n" +
                "• 自动点击和滑动\n" +
                "• 读取屏幕内容\n" +
                "• 执行自动化任务\n\n" +
                "是否前往设置开启？"
            ).then(confirmed => {
                if (confirmed) {
                    try {
                        auto();
                        this.showToast("请在无障碍设置中开启脚本助手");
                    } catch (e) {
                        this.showToast("请手动前往设置开启无障碍服务");
                    }
                }
            });
        } catch (e) {
            this.showToast("请在设置中开启无障碍服务");
        }
    },

    checkFloatingWindowPermission: function() {
        try {
            return floaty.checkPermission();
        } catch (e) {
            return false;
        }
    },

    isFloatingWindowPermissionGranted: function() {
        return this.checkFloatingWindowPermission();
    },

    requestFloatingWindowPermission: function() {
        try {
            floaty.requestPermission();
            this.showToast("请在设置中开启悬浮窗权限");
        } catch (e) {
            this.showToast("请手动开启悬浮窗权限");
        }
    },

    checkAllRequiredPermissions: function() {
        return this.checkAccessibilityPermission() && this.checkFloatingWindowPermission();
    },

    // 验证函数
    isValidPhoneNumber: function(phone) {
        return /^1[3-9]\d{9}$/.test(phone);
    },

    isValidPassword: function(password) {
        return password && password.length >= 6;
    },

    // UI状态更新
    updateUIState: function() {
        try {
            // 更新权限开关状态
            if (ui.accessibilitySwitch) {
                ui.accessibilitySwitch.setChecked(this.checkAccessibilityPermission());
            }
            if (ui.floatingWindowSwitch) {
                ui.floatingWindowSwitch.setChecked(this.checkFloatingWindowPermission());
            }

            // 更新版本信息
            if (ui.appTitle && global.APP_INFO) {
                // 保持原始标题
            }
        } catch (e) {
            console.error("更新UI状态失败: " + e.message);
        }
    },

    // 工具函数
    showToast: function(message) {
        try {
            toast(message);
        } catch (e) {
            console.log("Toast: " + message);
        }
    }
};

// 检查登录状态
function checkLoginStatus() {
    try {
        if (global.StorageModule) {
            var savedLoginStatus = global.StorageModule.get("isLoggedIn", false);
            var savedUser = global.StorageModule.get("currentUser", null);

            if (savedLoginStatus === true && savedUser) {
                isLoggedIn = true;
                currentUser = savedUser;
                global.LogModule.log("自动恢复登录状态: " + savedUser.phone, "INFO");

                // 跳转到脚本中心页面
                setTimeout(function() {
                    UIModule.createScriptCenterUI(false);
                }, 500);
                return;
            }
        }

        // 未登录，显示主界面
        UIModule.createMainUI();
    } catch (e) {
        console.error("检查登录状态失败: " + e.message);
        UIModule.createMainUI();
    }
}

// 主函数 - 应用启动入口
function main() {
    try {
        console.log("脚本助手主函数启动...");

        // 1. 初始化应用
        console.log("开始初始化应用...");

        // 2. 加载所有模块
        loadAllModules();

        // 3. 检查登录状态并显示相应界面
        console.log("检查登录状态...");
        checkLoginStatus();

        console.log("✓ 应用初始化完成");
        global.LogModule.log("脚本助手启动成功");

    } catch (e) {
        console.error("应用启动失败: " + e.message);
        console.error(e.stack);

        // 启动失败时显示基础界面
        try {
            UIModule.createMainUI();
        } catch (uiError) {
            console.error("创建基础界面也失败: " + uiError.message);
            toast("应用启动失败，请重试");
        }
    }
}

// 启动应用
console.log("=== 脚本助手启动完成 ===");
main();
