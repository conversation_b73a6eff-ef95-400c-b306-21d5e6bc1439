/**
 * 每日任务模块
 * 负责处理每日任务相关功能
 */

var DailyTaskModule = (function () {
    return {
        /**
         * 创建每日任务界面 - 完全按照原始main.js实现
         */
        createDailyTaskUI: function () {
            try {
                // 创建每日任务UI布局 - 完全按照原版
                var dailyTaskLayoutXml =
                    '<frame bg="#f5ffe0">' +
                    '<vertical padding="8" h="*">' +
                    '<horizontal gravity="center_vertical" h="50">' +
                    '<button id="backFromDailyTaskBtn" text="返回" textSize="14sp" style="Widget.AppCompat.Button.Borderless" w="60dp" h="40dp"/>' +
                    '<text text="每日任务" textSize="18sp" textColor="#333333" gravity="center" layout_weight="1" textStyle="bold"/>' +
                    '<frame w="60dp" visibility="invisible"/>' + // 添加一个不可见的占位符，平衡左侧返回按钮
                    '</horizontal>' +

                    '<scroll layout_weight="1">' +
                    '<vertical>' +
                    '<card cardCornerRadius="10dp" cardElevation="2dp" margin="5 10">' +
                    '<vertical padding="0">' +
                    '<img id="promotionImage" src="@android:drawable/ic_menu_gallery" w="*" h="200" scaleType="centerCrop"/>' +
                    '</vertical>' +
                    '</card>' +

                    '<card cardCornerRadius="10dp" cardElevation="2dp" margin="5 10">' +
                    '<vertical padding="15">' +
                    '<text id="promotionTitle" text="每日任务" textSize="16sp" textColor="#333333" textStyle="bold"/>' +
                    '<text id="promotionContent" text="加载中..." textSize="14sp" textColor="#666666" marginTop="8"/>' +
                    '</vertical>' +
                    '</card>' +
                    '</vertical>' +
                    '</scroll>' +

                    '<vertical padding="5 10" bg="#f5ffe0">' +
                    '<horizontal margin="0 5" gravity="center">' +
                    '<button id="saveImageBtn" text="保存图片" textSize="14sp" style="Widget.AppCompat.Button.Colored" layout_weight="1" margin="5"/>' +
                    '<button id="copyPromotionBtn" text="复制推广信息" textSize="14sp" style="Widget.AppCompat.Button.Colored" layout_weight="1" margin="5"/>' +
                    '</horizontal>' +

                    '<horizontal margin="0 0 0 5" gravity="center">' +
                    '<button id="shareToFriendsBtn" text="发送到朋友圈" textSize="14sp" style="Widget.AppCompat.Button.Colored" layout_weight="1" margin="5"/>' +
                    '<button id="getRewardBtn" text="领取积分奖励" textSize="14sp" style="Widget.AppCompat.Button.Colored" layout_weight="1" margin="5"/>' +
                    '</horizontal>' +
                    '</vertical>' +
                    '</vertical>' +
                    '</frame>';

                // 使用安全的UI创建方法
                this.safeCreateUI(dailyTaskLayoutXml, function () {
                    try {
                        // 设置当前页面状态
                        if (typeof global !== 'undefined' && global.UIModule && global.UIModule.setCurrentPage) {
                            global.UIModule.setCurrentPage("dailyTask");
                        }

                        // 绑定事件
                        DailyTaskModule.bindEvents();

                        // 加载推广内容
                        DailyTaskModule.loadPromotionContent();

                    } catch (e) {
                        console.error("绑定每日任务界面事件失败: " + e.message);
                        console.error(e.stack);
                    }
                });
            } catch (e) {
                console.error("创建每日任务界面失败: " + e.message);
                if (typeof toast !== 'undefined') {
                    toast("创建每日任务界面失败: " + e.message);
                }
            }
        },

        /**
         * 安全创建UI的方法
         */
        safeCreateUI: function (layoutXml, callback) {
            try {
                if (typeof ui !== 'undefined' && ui.layout) {
                    ui.layout(layoutXml);
                    if (callback && typeof callback === 'function') {
                        callback();
                    }
                } else {
                    console.error("UI对象不可用");
                    if (typeof toast !== 'undefined') {
                        toast("UI对象不可用");
                    }
                }
            } catch (e) {
                console.error("安全创建UI失败: " + e.message);
                if (typeof toast !== 'undefined') {
                    toast("创建界面失败: " + e.message);
                }
            }
        },

        /**
         * 绑定事件处理
         */
        bindEvents: function () {
            try {
                // 返回按钮点击事件
                if (ui.backFromDailyTaskBtn) {
                    ui.backFromDailyTaskBtn.on("click", function () {
                        try {
                            // 直接显示"我的"页面，不经过脚本中心
                            if (typeof global !== 'undefined' && global.UIModule && global.UIModule.showMyPageDirectly) {
                                global.UIModule.showMyPageDirectly();
                            } else if (typeof global !== 'undefined' && global.UIModule && global.UIModule.returnToScriptCenterMyTab) {
                                // 降级方案：使用原来的方法
                                global.UIModule.returnToScriptCenterMyTab();
                            } else {
                                console.error("UIModule 导航方法不可用");
                                if (typeof toast !== 'undefined') {
                                    toast("返回失败，请重试");
                                }
                            }
                        } catch (e) {
                            console.error("返回我的页面失败: " + e.message);
                            if (typeof toast !== 'undefined') {
                                toast("返回失败，请重试");
                            }
                        }
                    });
                }

                // 保存图片按钮
                if (ui.saveImageBtn) {
                    ui.saveImageBtn.on("click", function () {
                        DailyTaskModule.savePromotionImage();
                    });
                }

                // 复制推广信息按钮
                if (ui.copyPromotionBtn) {
                    ui.copyPromotionBtn.on("click", function () {
                        DailyTaskModule.copyPromotionContent();
                    });
                }

                // 发送到朋友圈按钮
                if (ui.shareToFriendsBtn) {
                    ui.shareToFriendsBtn.on("click", function () {
                        DailyTaskModule.shareToWechatMoments();
                    });
                }

                // 领取积分奖励按钮
                if (ui.getRewardBtn) {
                    ui.getRewardBtn.on("click", function () {
                        if (typeof toast !== 'undefined') {
                            toast("请联系客服领取积分奖励");
                        }
                    });
                }

                // 添加物理返回键监听器
                DailyTaskModule.setupBackKeyHandler();

            } catch (e) {
                console.error("绑定每日任务事件失败: " + e.message);
            }
        },

        /**
         * 加载推广内容
         */
        loadPromotionContent: function () {
            try {
                // 获取推广内容 - 优先从global对象获取NetworkModule
                var networkModule = null;
                if (typeof global !== 'undefined' && global.NetworkModule) {
                    networkModule = global.NetworkModule;
                } else if (typeof NetworkModule !== 'undefined') {
                    networkModule = NetworkModule;
                }

                if (networkModule && networkModule.getRandomPromotion) {
                    console.log("使用NetworkModule获取推广内容");
                    networkModule.getRandomPromotion(function (error, result) {
                        if (error) {
                            console.error("获取推广内容失败: " + error.message);
                            if (typeof toast !== 'undefined') {
                                toast("获取推广内容失败，请检查网络连接");
                            }
                            return;
                        }

                        if (result && result.code === 200 && result.data) {
                            var promotionData = result.data;
                            DailyTaskModule.updatePromotionUI(promotionData);
                        }
                    });
                } else {
                    console.log("NetworkModule不可用或缺少getRandomPromotion方法，使用默认内容");
                    // 使用默认内容
                    var defaultPromotion = {
                        title: "每日任务",
                        content: "完成每日推广任务，获取积分奖励！\n\n1. 保存推广图片到相册\n2. 复制推广信息\n3. 发送到朋友圈\n4. 联系客服领取积分奖励",
                        imageUrl: null
                    };
                    DailyTaskModule.updatePromotionUI(defaultPromotion);
                }
            } catch (e) {
                console.error("加载推广内容失败: " + e.message);
                // 发生错误时也使用默认内容
                var defaultPromotion = {
                    title: "每日任务",
                    content: "完成每日推广任务，获取积分奖励！",
                    imageUrl: null
                };
                DailyTaskModule.updatePromotionUI(defaultPromotion);
            }
        },

        /**
         * 更新推广UI内容
         */
        updatePromotionUI: function (promotionData) {
            try {
                // 更新UI内容
                if (typeof ui !== 'undefined' && ui.run) {
                    ui.run(function () {
                        try {
                            // 设置标题和内容
                            if (ui.promotionTitle && promotionData.title) {
                                ui.promotionTitle.setText(promotionData.title);
                            }

                            if (ui.promotionContent && promotionData.content) {
                                ui.promotionContent.setText(promotionData.content);
                            }

                            // 设置图片
                            if (ui.promotionImage && promotionData.imageUrl) {
                                DailyTaskModule.loadPromotionImage(promotionData.imageUrl);
                            }

                            // 保存推广数据到全局变量，以便复制按钮使用
                            if (typeof ConfigModule !== 'undefined' && ConfigModule.set) {
                                ConfigModule.set("currentPromotion", JSON.stringify(promotionData));
                            }

                        } catch (uiError) {
                            console.error("更新推广UI失败: " + uiError.message);
                        }
                    });
                }
            } catch (e) {
                console.error("更新推广UI失败: " + e.message);
            }
        },

        /**
         * 加载推广图片
         */
        loadPromotionImage: function (imageUrl) {
            try {
                // 在子线程中加载图片，避免NetworkOnMainThreadException
                if (typeof threads !== 'undefined') {
                    threads.start(function () {
                        try {
                            // 使用原生方法加载图片
                            if (typeof images !== 'undefined' && images.load) {
                                let imgWrapper = images.load(imageUrl);
                                if (imgWrapper) {
                                    // 将ImageWrapper转换为Bitmap
                                    let bitmap = imgWrapper.getBitmap();

                                    // 在UI线程中设置图片
                                    ui.run(function () {
                                        try {
                                            ui.promotionImage.setImageBitmap(bitmap);
                                        } catch (e) {
                                            console.error("设置图片到UI失败: " + e.message);

                                            // 尝试使用另一种方式设置图片
                                            try {
                                                let drawable = new android.graphics.drawable.BitmapDrawable(context.getResources(), bitmap);
                                                ui.promotionImage.setImageDrawable(drawable);
                                            } catch (e2) {
                                                console.error("使用Drawable设置图片也失败: " + e2.message);
                                            }
                                        }
                                    });
                                } else {
                                    console.error("加载图片失败: 无法获取位图");
                                }
                            }
                        } catch (threadError) {
                            console.error("子线程加载图片失败: " + threadError.message);
                        }
                    });
                }
            } catch (imgError) {
                console.error("加载图片失败: " + imgError.message);
            }
        },

        /**
         * 保存推广图片
         */
        savePromotionImage: function () {
            try {
                // 尝试获取当前显示的图片
                let img = ui.promotionImage.getDrawable();
                if (img) {
                    // 将Drawable转换为Bitmap
                    let bitmap = android.graphics.Bitmap.createBitmap(
                        img.getIntrinsicWidth(),
                        img.getIntrinsicHeight(),
                        android.graphics.Bitmap.Config.ARGB_8888
                    );
                    let canvas = new android.graphics.Canvas(bitmap);
                    img.setBounds(0, 0, canvas.getWidth(), canvas.getHeight());
                    img.draw(canvas);

                    // 保存图片到相册
                    let fileName = "promotion_" + new Date().getTime() + ".png";
                    let path = "/sdcard/Pictures/" + fileName;

                    // 确保目录存在
                    if (typeof files !== 'undefined' && files.ensureDir) {
                        files.ensureDir("/sdcard/Pictures/");
                    }

                    // 使用正确的方式保存图片
                    try {
                        // 方法1：使用java的方式保存
                        let fileOutputStream = new java.io.FileOutputStream(path);
                        bitmap.compress(android.graphics.Bitmap.CompressFormat.PNG, 100, fileOutputStream);
                        fileOutputStream.close();
                    } catch (e1) {
                        console.error("Java方式保存失败: " + e1.message);

                        try {
                            // 方法2：使用Auto.js的images.save方法（正确参数）
                            if (typeof images !== 'undefined' && images.fromBitmap && images.save) {
                                let imgWrapper = images.fromBitmap(bitmap);
                                images.save(imgWrapper, path);
                            }
                        } catch (e2) {
                            console.error("Auto.js方式保存失败: " + e2.message);
                            throw e2;
                        }
                    }

                    // 通知媒体库更新
                    try {
                        if (typeof media !== 'undefined' && media.scanFile) {
                            media.scanFile(path);
                        }
                    } catch (e) {
                        console.error("媒体库更新失败: " + e.message);
                    }

                    if (typeof toast !== 'undefined') {
                        toast("图片已保存到相册: " + fileName);
                    }
                } else {
                    if (typeof toast !== 'undefined') {
                        toast("没有可保存的图片");
                    }
                }
            } catch (e) {
                if (typeof toast !== 'undefined') {
                    toast("保存图片失败: " + e.message);
                }
                console.error("保存图片失败: " + e.message);
            }
        },

        /**
         * 复制推广内容
         */
        copyPromotionContent: function () {
            try {
                // 获取保存的推广数据
                let promotionJson = "";
                let promotionData = null;

                if (typeof ConfigModule !== 'undefined' && ConfigModule.get) {
                    promotionJson = ConfigModule.get("currentPromotion");
                    promotionData = promotionJson ? JSON.parse(promotionJson) : null;
                }

                // 获取邀请码
                let invitationCode = "未设置";
                if (typeof ConfigModule !== 'undefined' && ConfigModule.get) {
                    invitationCode = ConfigModule.get("invitationCode") || "未设置";
                }

                // 构建推广内容
                let content = "";

                if (promotionData && promotionData.content) {
                    content = promotionData.content + "\n\n";
                } else {
                    content = "这是推广内容示例，实际内容请联系客服获取。\n\n";
                }

                // 添加邀请码
                content += "邀请码: " + invitationCode;

                // 复制到剪贴板
                if (typeof setClip !== 'undefined') {
                    setClip(content);
                    if (typeof toast !== 'undefined') {
                        toast("推广内容已复制到剪贴板");
                    }
                } else {
                    console.error("setClip 方法不可用");
                    if (typeof toast !== 'undefined') {
                        toast("复制功能不可用");
                    }
                }
            } catch (e) {
                if (typeof toast !== 'undefined') {
                    toast("复制推广内容失败: " + e.message);
                }
                console.error("复制推广内容失败: " + e.message);
            }
        },

        /**
         * 分享到微信朋友圈
         */
        shareToWechatMoments: function () {
            try {
                // 导入必要的类
                importClass(android.content.Intent);
                importClass(android.content.ComponentName);

                // 准备打开微信的意图
                var intent = new Intent();
                intent.setAction("android.intent.action.MAIN");
                intent.addCategory("android.intent.category.LAUNCHER");
                intent.setPackage("com.tencent.mm"); // 微信的包名

                // 获取微信启动页
                var packageManager = context.getPackageManager();
                var resolveInfo = packageManager.queryIntentActivities(intent, 0);

                if (resolveInfo && resolveInfo.size() > 0) {
                    // 存在微信应用，获取启动页面的组件信息
                    var activityInfo = resolveInfo.get(0).activityInfo;
                    var componentName = new ComponentName(activityInfo.packageName, activityInfo.name);

                    // 设置要启动的组件
                    intent.setComponent(componentName);
                    intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);

                    // 启动微信
                    context.startActivity(intent);

                    if (typeof LogModule !== 'undefined' && LogModule.log) {
                        LogModule.log("成功跳转到微信", "INFO");
                    }
                } else {
                    if (typeof toast !== 'undefined') {
                        toast("未安装微信或无法启动微信");
                    }
                    if (typeof LogModule !== 'undefined' && LogModule.log) {
                        LogModule.log("未安装微信或无法启动微信", "WARN");
                    }
                }
            } catch (e) {
                console.error("跳转到微信失败: " + e.message);
                console.error(e.stack);
                if (typeof LogModule !== 'undefined' && LogModule.log) {
                    LogModule.log("跳转到微信失败: " + e.message, "ERROR");
                }
                if (typeof toast !== 'undefined') {
                    toast("跳转到微信失败: " + e.message);
                }
            }
        },

        /**
         * 设置物理返回键处理
         */
        setupBackKeyHandler: function () {
            try {
                // 监听返回键事件
                if (typeof ui !== 'undefined' && ui.emitter) {
                    ui.emitter.on("back_pressed", function (e) {
                        try {
                            // 检查当前是否在每日任务页面
                            if (typeof global !== 'undefined' && global.UIModule && global.UIModule.getCurrentPage) {
                                var currentPage = global.UIModule.getCurrentPage();
                                if (currentPage === "dailyTask") {
                                    // 阻止默认返回行为
                                    e.consumed = true;

                                    // 执行返回操作 - 直接显示"我的"页面
                                    if (global.UIModule.showMyPageDirectly) {
                                        global.UIModule.showMyPageDirectly();
                                    } else if (global.UIModule.returnToScriptCenterMyTab) {
                                        // 降级方案
                                        global.UIModule.returnToScriptCenterMyTab();
                                    } else {
                                        console.error("UIModule 导航方法不可用");
                                        if (typeof toast !== 'undefined') {
                                            toast("返回失败，请重试");
                                        }
                                    }
                                }
                            }
                        } catch (error) {
                            console.error("处理返回键事件失败: " + error.message);
                        }
                    });
                }

                // 监听按键事件（备用方案）
                if (typeof events !== 'undefined') {
                    events.on("key_down", function (keyCode, event) {
                        try {
                            if (keyCode === keys.back) {
                                // 检查当前是否在每日任务页面
                                if (typeof global !== 'undefined' && global.UIModule && global.UIModule.getCurrentPage) {
                                    var currentPage = global.UIModule.getCurrentPage();
                                    if (currentPage === "dailyTask") {
                                        // 执行返回操作 - 直接显示"我的"页面
                                        if (global.UIModule.showMyPageDirectly) {
                                            global.UIModule.showMyPageDirectly();
                                        } else if (global.UIModule.returnToScriptCenterMyTab) {
                                            global.UIModule.returnToScriptCenterMyTab();
                                        }
                                        return true; // 阻止默认行为
                                    }
                                }
                            }
                        } catch (error) {
                            console.error("处理按键事件失败: " + error.message);
                        }
                        return false;
                    });
                }

                console.log("每日任务页面返回键处理设置完成");
            } catch (e) {
                console.error("设置返回键处理失败: " + e.message);
            }
        }
    };
})();

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DailyTaskModule;
} else if (typeof global !== 'undefined') {
    global.DailyTaskModule = DailyTaskModule;
}
