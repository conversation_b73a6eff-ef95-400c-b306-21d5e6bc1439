/**
 * 代理分销模块
 * 负责处理代理分销相关功能
 */

var AgentSalesModule = (function () {
    return {
        /**
         * 创建代理分销界面 - 完全按照原始main.js实现
         */
        createAgentSalesUI: function () {
            try {
                // 创建代理分销UI布局 - 完全按照原版
                var agentSalesLayoutXml =
                    '<frame w="*" h="*" background="#f5f5f5">' +
                    '<vertical w="*" h="*" padding="16">' +
                    '<horizontal w="*" h="50" gravity="center_vertical">' +
                    '<img id="backBtn" src="@android:drawable/ic_menu_revert" w="24" h="24" tint="#333333" margin="0 0 16 0"/>' +
                    '<frame w="0" layout_weight="1" gravity="center">' +
                    '<text text="代理分销" textSize="18sp" textColor="#333333" textStyle="bold"/>' +
                    '</frame>' +
                    '<frame w="24" visibility="invisible"/>' +
                    '</horizontal>' +

                    '<card w="*" h="auto" margin="0 16 0 0" cardCornerRadius="8dp" cardElevation="2dp">' +
                    '<vertical padding="16">' +
                    '<horizontal>' +
                    '<text text="我的佣金：" textSize="16sp" textColor="#333333"/>' +
                    '<text id="commissionText" text="0.00元" textSize="16sp" textColor="#FF5722" textStyle="bold"/>' +
                    '</horizontal>' +
                    '<horizontal margin="0 8 0 0">' +
                    '<text text="推荐总人数：" textSize="16sp" textColor="#333333"/>' +
                    '<text id="totalReferralsText" text="0" textSize="16sp" textColor="#333333" textStyle="bold"/>' +
                    '</horizontal>' +
                    '</vertical>' +
                    '</card>' +

                    '<card id="level1Card" w="*" h="auto" margin="0 16 0 0" cardCornerRadius="8dp" cardElevation="2dp">' +
                    '<vertical>' +
                    '<horizontal id="level1Header" padding="16 16" gravity="center_vertical" bg="#f9f9f9">' +
                    '<text id="level1Title" text="一级会员(0)" textSize="16sp" textColor="#333333" textStyle="bold" layout_weight="1"/>' +
                    '<text id="level1Arrow" text="▶" textSize="16sp" textColor="#666666"/>' +
                    '</horizontal>' +
                    '<vertical id="level1Content" padding="8 0" visibility="gone">' +
                    '<horizontal padding="10 8" bg="#eeeeee">' +
                    '<text text="用户名" w="0" layout_weight="1" textSize="14sp" textColor="#666666" textStyle="bold"/>' +
                    '<text text="收益金额" w="0" layout_weight="1" textSize="14sp" textColor="#666666" textStyle="bold"/>' +
                    '<text text="收益时间" w="0" layout_weight="1" textSize="14sp" textColor="#666666" textStyle="bold"/>' +
                    '</horizontal>' +
                    '<!-- 会员列表内容将动态添加 -->' +
                    '</vertical>' +
                    '</vertical>' +
                    '</card>' +

                    '<card id="level2Card" w="*" h="auto" margin="0 16 0 0" cardCornerRadius="8dp" cardElevation="2dp">' +
                    '<vertical>' +
                    '<horizontal id="level2Header" padding="16 16" gravity="center_vertical" bg="#f9f9f9">' +
                    '<text id="level2Title" text="二级会员(0)" textSize="16sp" textColor="#333333" textStyle="bold" layout_weight="1"/>' +
                    '<text id="level2Arrow" text="▶" textSize="16sp" textColor="#666666"/>' +
                    '</horizontal>' +
                    '<vertical id="level2Content" padding="8 0" visibility="gone">' +
                    '<horizontal padding="10 8" bg="#eeeeee">' +
                    '<text text="用户名" w="0" layout_weight="1" textSize="14sp" textColor="#666666" textStyle="bold"/>' +
                    '<text text="收益金额" w="0" layout_weight="1" textSize="14sp" textColor="#666666" textStyle="bold"/>' +
                    '<text text="收益时间" w="0" layout_weight="1" textSize="14sp" textColor="#666666" textStyle="bold"/>' +
                    '</horizontal>' +
                    '<!-- 会员列表内容将动态添加 -->' +
                    '</vertical>' +
                    '</vertical>' +
                    '</card>' +
                    '</vertical>' +
                    '</frame>';

                // 使用安全的UI创建方法
                this.safeCreateUI(agentSalesLayoutXml, function () {
                    try {
                        // 设置当前页面状态
                        if (typeof global !== 'undefined' && global.UIModule && global.UIModule.setCurrentPage) {
                            global.UIModule.setCurrentPage("agentSales");
                        }

                        // 绑定事件
                        AgentSalesModule.bindEvents();

                        // 加载分销数据
                        AgentSalesModule.loadDistributionData();

                    } catch (e) {
                        console.error("绑定代理分销界面事件失败: " + e.message);
                        console.error(e.stack);
                    }
                });
            } catch (e) {
                console.error("创建代理分销界面失败: " + e.message);
                if (typeof toast !== 'undefined') {
                    toast("创建代理分销界面失败: " + e.message);
                }
            }
        },

        /**
         * 安全创建UI的方法
         */
        safeCreateUI: function (layoutXml, callback) {
            try {
                if (typeof ui !== 'undefined' && ui.layout) {
                    ui.layout(layoutXml);
                    if (callback && typeof callback === 'function') {
                        callback();
                    }
                } else {
                    console.error("UI对象不可用");
                    if (typeof toast !== 'undefined') {
                        toast("UI对象不可用");
                    }
                }
            } catch (e) {
                console.error("安全创建UI失败: " + e.message);
                if (typeof toast !== 'undefined') {
                    toast("创建界面失败: " + e.message);
                }
            }
        },

        /**
         * 绑定事件处理
         */
        bindEvents: function () {
            try {
                // 返回按钮点击事件
                if (ui.backBtn) {
                    ui.backBtn.on("click", function () {
                        try {
                            // 直接显示"我的"页面，不经过脚本中心
                            if (typeof global !== 'undefined' && global.UIModule && global.UIModule.showMyPageDirectly) {
                                global.UIModule.showMyPageDirectly();
                            } else if (typeof global !== 'undefined' && global.UIModule && global.UIModule.returnToScriptCenterMyTab) {
                                // 降级方案：使用原来的方法
                                global.UIModule.returnToScriptCenterMyTab();
                            } else {
                                console.error("UIModule 导航方法不可用");
                                if (typeof toast !== 'undefined') {
                                    toast("返回失败，请重试");
                                }
                            }
                        } catch (e) {
                            console.error("返回我的页面失败: " + e.message);
                            if (typeof toast !== 'undefined') {
                                toast("返回失败，请重试");
                            }
                        }
                    });
                }

                // 绑定一级会员展开/收起事件
                if (ui.level1Header) {
                    ui.level1Header.on("click", function () {
                        AgentSalesModule.toggleMemberList("level1");
                    });
                }

                // 绑定二级会员展开/收起事件
                if (ui.level2Header) {
                    ui.level2Header.on("click", function () {
                        AgentSalesModule.toggleMemberList("level2");
                    });
                }

                // 添加物理返回键监听器
                AgentSalesModule.setupBackKeyHandler();

            } catch (e) {
                console.error("绑定代理分销事件失败: " + e.message);
            }
        },

        /**
         * 获取用户Token - 与其他模块保持一致的获取逻辑
         */
        getUserToken: function() {
            console.log("代理分销模块：开始获取用户Token...");

            // 优先从StorageModule获取Token（登录时保存的位置）
            if (typeof global !== 'undefined' && global.StorageModule) {
                console.log("代理分销模块：尝试从StorageModule获取Token...");
                var token = global.StorageModule.get("userToken");
                if (token) {
                    console.log("代理分销模块：从StorageModule获取到Token: " + token.substring(0, 10) + "...");
                    return token;
                } else {
                    console.log("代理分销模块：StorageModule中没有找到Token");
                }
            } else {
                console.log("代理分销模块：StorageModule不可用");
            }

            // 降级到ConfigModule
            if (typeof global !== 'undefined' && global.ConfigModule) {
                console.log("代理分销模块：尝试从ConfigModule获取Token...");
                var token = global.ConfigModule.get("userToken");
                if (token) {
                    console.log("代理分销模块：从ConfigModule获取到Token: " + token.substring(0, 10) + "...");
                    return token;
                } else {
                    console.log("代理分销模块：ConfigModule中没有找到Token");
                }
            } else {
                console.log("代理分销模块：ConfigModule不可用");
            }

            // 最后尝试从currentUser全局变量获取
            if (typeof currentUser !== 'undefined' && currentUser && currentUser.token) {
                console.log("代理分销模块：从currentUser获取到Token: " + currentUser.token.substring(0, 10) + "...");
                return currentUser.token;
            } else {
                console.log("代理分销模块：currentUser中没有Token");
            }

            console.warn("代理分销模块：未能获取到用户Token");
            return null;
        },

        /**
         * 加载分销数据
         */
        loadDistributionData: function () {
            try {
                // 获取用户token - 使用与其他模块一致的获取方式
                var token = this.getUserToken();
                console.log("代理分销模块：获取到Token: " + (token ? token.substring(0, 10) + "..." : "null"));

                if (!token) {
                    console.error("未找到用户token，无法获取分销数据");
                    if (typeof toast !== 'undefined') {
                        toast("请先登录");
                    }
                    return;
                }

                // 调用API获取分销统计数据
                if (typeof global !== 'undefined' && global.NetworkModule && global.NetworkModule.get) {
                    global.NetworkModule.get("/user/distribution/statistics", null, function (error, result) {
                        if (error) {
                            console.error("获取分销数据失败: " + error.message);
                            if (typeof toast !== 'undefined') {
                                toast("获取分销数据失败，请检查网络连接");
                            }
                            return;
                        }

                        if (result && result.code === 200 && result.data) {
                            var distributionData = result.data;
                            AgentSalesModule.updateDistributionUI(distributionData);
                        }
                    }, token);
                } else {
                    console.error("NetworkModule 不可用");
                    // 使用默认数据
                    var defaultData = {
                        totalAmount: 0.00,
                        totalReferrals: 0,
                        level1Count: 0,
                        level2Count: 0,
                        level1Details: [],
                        level2Details: []
                    };
                    AgentSalesModule.updateDistributionUI(defaultData);
                }
            } catch (e) {
                console.error("加载分销数据失败: " + e.message);
            }
        },

        /**
         * 更新分销UI内容
         */
        updateDistributionUI: function (distributionData) {
            try {
                // 更新UI内容
                if (typeof ui !== 'undefined' && ui.run) {
                    ui.run(function () {
                        try {
                            // 设置佣金信息
                            if (ui.commissionText) {
                                ui.commissionText.setText(distributionData.totalAmount + "元");
                            }

                            // 设置推荐总人数
                            if (ui.totalReferralsText) {
                                ui.totalReferralsText.setText(distributionData.totalReferrals + "");
                            }

                            // 更新一级会员标题和数据
                            if (ui.level1Title) {
                                ui.level1Title.setText("一级会员(" + distributionData.level1Count + ")");
                            }

                            // 更新二级会员标题和数据
                            if (ui.level2Title) {
                                ui.level2Title.setText("二级会员(" + distributionData.level2Count + ")");
                            }

                            // 处理会员数据并填充会员列表
                            if (distributionData.level1Details && distributionData.level1Details.length > 0) {
                                var processedData = AgentSalesModule.processMemberData(distributionData.level1Details);
                                AgentSalesModule.fillMemberList("level1", processedData);
                            }

                            if (distributionData.level2Details && distributionData.level2Details.length > 0) {
                                var processedData = AgentSalesModule.processMemberData(distributionData.level2Details);
                                AgentSalesModule.fillMemberList("level2", processedData);
                            }

                        } catch (uiError) {
                            console.error("更新分销UI失败: " + uiError.message);
                        }
                    });
                }
            } catch (e) {
                console.error("更新分销UI失败: " + e.message);
            }
        },

        /**
         * 处理会员数据，归集相同用户的记录
         * @param {Array} data - 原始会员数据数组
         * @returns {Array} 处理后的会员数据数组
         */
        processMemberData: function (data) {
            try {
                if (!data || !Array.isArray(data) || data.length === 0) {
                    return [];
                }

                // 用于存储归集后的数据
                var processedData = {};

                // 遍历原始数据，按用户ID归集
                data.forEach(function (item) {
                    var userId = item.referralUserId;
                    if (!userId) return;

                    // 如果该用户ID还未记录，则创建新记录
                    if (!processedData[userId]) {
                        processedData[userId] = {
                            referralUserId: userId,
                            referralPhoneMasked: item.referralPhoneMasked || "未知用户",
                            amount: 0,
                            createTime: item.createTime || "",
                            hasIncome: false
                        };
                    }

                    // 累加金额
                    if (item.amount) {
                        processedData[userId].amount += parseFloat(item.amount);

                        // 如果有金额，则标记为有收益
                        if (parseFloat(item.amount) > 0) {
                            processedData[userId].hasIncome = true;
                        }

                        // 更新为最新的时间（如果当前记录的时间更新）
                        if (item.createTime && (!processedData[userId].createTime ||
                            new Date(item.createTime) > new Date(processedData[userId].createTime))) {
                            processedData[userId].createTime = item.createTime;
                        }
                    }
                });

                // 转换为数组
                var result = Object.values(processedData);

                // 按金额从大到小排序
                result.sort(function (a, b) {
                    return b.amount - a.amount;
                });

                return result;
            } catch (e) {
                console.error("处理会员数据出错：" + e);
                return data || [];
            }
        },

        /**
         * 填充会员列表数据
         * @param {string} level - 会员级别：level1, level2
         * @param {Array} data - 会员数据数组
         */
        fillMemberList: function (level, data) {
            try {
                var container;

                // 确保数据有效
                if (!data || !Array.isArray(data) || data.length === 0) {
                    return;
                }

                // 根据级别获取对应容器
                if (level === "level1") {
                    container = ui.level1Content;
                } else if (level === "level2") {
                    container = ui.level2Content;
                }

                // 确保容器存在
                if (!container) {
                    console.error("未找到" + level + "容器UI元素");
                    return;
                }

                // 清除现有视图（除了标题行）
                container.removeAllViews();

                // 添加标题行
                var headerXml =
                    '<horizontal padding="10 8" bg="#eeeeee">' +
                    '<text text="用户名" w="0" layout_weight="1" textSize="14sp" textColor="#666666" textStyle="bold"/>' +
                    '<text text="收益金额" w="0" layout_weight="1" textSize="14sp" textColor="#666666" textStyle="bold"/>' +
                    '<text text="收益时间" w="0" layout_weight="1" textSize="14sp" textColor="#666666" textStyle="bold"/>' +
                    '</horizontal>';

                try {
                    var headerRow = ui.inflate(headerXml, container, true);
                } catch (e) {
                    console.error("添加标题行失败: " + e);
                }

                // 添加会员行
                for (var i = 0; i < data.length; i++) {
                    try {
                        var member = data[i];
                        var rowLayout = new android.widget.LinearLayout(context);
                        rowLayout.setOrientation(android.widget.LinearLayout.HORIZONTAL);
                        rowLayout.setLayoutParams(new android.widget.LinearLayout.LayoutParams(
                            android.widget.LinearLayout.LayoutParams.MATCH_PARENT,
                            android.widget.LinearLayout.LayoutParams.WRAP_CONTENT
                        ));
                        rowLayout.setPadding(10, 5, 10, 5);

                        // 设置背景色
                        if (i % 2 === 0) {
                            rowLayout.setBackgroundColor(android.graphics.Color.parseColor("#ffffff"));
                        } else {
                            rowLayout.setBackgroundColor(android.graphics.Color.parseColor("#f8f8f8"));
                        }

                        // 创建用户名文本
                        var phoneText = new android.widget.TextView(context);
                        var phoneParams = new android.widget.LinearLayout.LayoutParams(0, android.widget.LinearLayout.LayoutParams.WRAP_CONTENT, 1);
                        phoneText.setLayoutParams(phoneParams);
                        phoneText.setText(member.referralPhoneMasked || "未知用户");
                        phoneText.setTextSize(14);
                        phoneText.setTextColor(android.graphics.Color.parseColor("#333333"));
                        rowLayout.addView(phoneText);

                        // 创建金额文本
                        var amountText = new android.widget.TextView(context);
                        var amountParams = new android.widget.LinearLayout.LayoutParams(0, android.widget.LinearLayout.LayoutParams.WRAP_CONTENT, 1);
                        amountText.setLayoutParams(amountParams);
                        amountText.setText(member.amount.toFixed(2) + "元");
                        amountText.setTextSize(14);
                        amountText.setTextColor(android.graphics.Color.parseColor("#FF5722"));
                        rowLayout.addView(amountText);

                        // 创建时间文本
                        var timeText = new android.widget.TextView(context);
                        var timeParams = new android.widget.LinearLayout.LayoutParams(0, android.widget.LinearLayout.LayoutParams.WRAP_CONTENT, 1);
                        timeText.setLayoutParams(timeParams);

                        // 如果金额为0，则不显示时间
                        var timeStr = "";
                        if (member.hasIncome && member.amount > 0) {
                            // 将时间格式化为"2023.06.01"的形式
                            var createTime = member.createTime || "";
                            if (createTime && createTime.length >= 10) {
                                timeStr = createTime.substring(0, 10).replace(/-/g, ".");
                            }
                        }

                        timeText.setText(timeStr);
                        timeText.setTextSize(14);
                        timeText.setTextColor(android.graphics.Color.parseColor("#666666"));
                        rowLayout.addView(timeText);

                        // 添加到容器
                        ui.run(function () {
                            container.addView(rowLayout);
                        });
                    } catch (e) {
                        console.error("添加会员行失败: " + e);
                    }
                }
            } catch (e) {
                console.error("填充会员列表数据出错：" + e);
            }
        },

        /**
         * 切换会员列表的展开/收起状态
         * @param {string} level - 会员级别：level1, level2
         */
        toggleMemberList: function (level) {
            try {
                var content, arrow;

                // 根据级别获取对应的内容和箭头元素
                if (level === "level1") {
                    content = ui.level1Content;
                    arrow = ui.level1Arrow;
                } else if (level === "level2") {
                    content = ui.level2Content;
                    arrow = ui.level2Arrow;
                }

                if (!content || !arrow) {
                    console.error("未找到" + level + "的UI元素");
                    return;
                }

                // 获取当前可见性状态
                var currentVisibility = content.getVisibility();

                if (currentVisibility === android.view.View.GONE) {
                    // 当前隐藏，显示内容
                    content.setVisibility(android.view.View.VISIBLE);
                    arrow.setText("▼");
                } else {
                    // 当前显示，隐藏内容
                    content.setVisibility(android.view.View.GONE);
                    arrow.setText("▶");
                }
            } catch (e) {
                console.error("切换会员列表显示状态出错：" + e);
            }
        },

        /**
         * 设置物理返回键处理
         */
        setupBackKeyHandler: function () {
            try {
                // 监听返回键事件
                if (typeof ui !== 'undefined' && ui.emitter) {
                    ui.emitter.on("back_pressed", function (e) {
                        try {
                            // 检查当前是否在代理分销页面
                            if (typeof global !== 'undefined' && global.UIModule && global.UIModule.getCurrentPage) {
                                var currentPage = global.UIModule.getCurrentPage();
                                if (currentPage === "agentSales") {
                                    // 阻止默认返回行为
                                    e.consumed = true;

                                    // 执行返回操作 - 直接显示"我的"页面
                                    if (global.UIModule.showMyPageDirectly) {
                                        global.UIModule.showMyPageDirectly();
                                    } else if (global.UIModule.returnToScriptCenterMyTab) {
                                        // 降级方案
                                        global.UIModule.returnToScriptCenterMyTab();
                                    } else {
                                        console.error("UIModule 导航方法不可用");
                                        if (typeof toast !== 'undefined') {
                                            toast("返回失败，请重试");
                                        }
                                    }
                                }
                            }
                        } catch (error) {
                            console.error("处理返回键事件失败: " + error.message);
                        }
                    });
                }

                // 监听按键事件（备用方案）
                if (typeof events !== 'undefined') {
                    events.on("key_down", function (keyCode, event) {
                        try {
                            if (keyCode === keys.back) {
                                // 检查当前是否在代理分销页面
                                if (typeof global !== 'undefined' && global.UIModule && global.UIModule.getCurrentPage) {
                                    var currentPage = global.UIModule.getCurrentPage();
                                    if (currentPage === "agentSales") {
                                        // 执行返回操作 - 直接显示"我的"页面
                                        if (global.UIModule.showMyPageDirectly) {
                                            global.UIModule.showMyPageDirectly();
                                        } else if (global.UIModule.returnToScriptCenterMyTab) {
                                            global.UIModule.returnToScriptCenterMyTab();
                                        }
                                        return true; // 阻止默认行为
                                    }
                                }
                            }
                        } catch (error) {
                            console.error("处理按键事件失败: " + error.message);
                        }
                        return false;
                    });
                }

                console.log("代理分销页面返回键处理设置完成");
            } catch (e) {
                console.error("设置返回键处理失败: " + e.message);
            }
        }
    };
})();

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AgentSalesModule;
} else if (typeof global !== 'undefined') {
    global.AgentSalesModule = AgentSalesModule;
}
