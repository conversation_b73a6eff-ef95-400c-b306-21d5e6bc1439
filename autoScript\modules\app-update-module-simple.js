/**
 * 应用更新模块 - 简化版本
 * 避免参数重复声明问题
 */

const AppUpdateModule = (function () {
    // 应用版本信息
    const APP_VERSION = typeof APP_INFO !== 'undefined' ? APP_INFO.VERSION : "1.0.0";
    const VERSION_CODE = typeof APP_INFO !== 'undefined' ? APP_INFO.VERSION_CODE : 100;

    // 更新状态常量
    const UPDATE_STATUS = {
        CHECKING: 0,        // 检查中
        DOWNLOADING: 1,     // 下载中
        INSTALLING: 2,      // 安装中
        SUCCESS: 3,         // 成功
        FAILED: 4,          // 失败
        NO_UPDATE: 5        // 无更新
    };

    return {
        /**
         * 初始化应用更新模块
         */
        init: function () {
            try {
                if (typeof LogModule !== 'undefined') {
                    LogModule.log("应用更新模块初始化");
                    LogModule.log("当前应用版本: " + APP_VERSION);
                } else {
                    console.log("应用更新模块初始化");
                    console.log("当前应用版本: " + APP_VERSION);
                }
            } catch (e) {
                console.error("应用更新模块初始化失败: " + e.message);
            }
        },

        /**
         * 获取当前版本信息
         */
        getCurrentVersion: function () {
            return {
                version: APP_VERSION,
                versionCode: VERSION_CODE
            };
        },

        /**
         * 获取设备信息
         */
        getDeviceInfo: function () {
            try {
                return {
                    deviceId: device.getAndroidId() || "unknown_device",
                    platform: "android",
                    deviceModel: device.model || "Unknown Model",
                    androidVersion: device.release || "Unknown Version"
                };
            } catch (e) {
                if (typeof LogModule !== 'undefined') {
                    LogModule.log("获取设备信息失败: " + e.message, "ERROR");
                }
                return {
                    deviceId: "unknown_device",
                    platform: "android",
                    deviceModel: "Unknown Model",
                    androidVersion: "Unknown Version"
                };
            }
        },

        /**
         * 检查更新
         */
        checkUpdate: function (callback) {
            try {
                if (typeof LogModule !== 'undefined') {
                    LogModule.log("开始检查应用更新");
                }

                // 获取设备信息
                const deviceInfo = this.getDeviceInfo();
                
                // 构建请求参数
                const requestData = {
                    deviceId: deviceInfo.deviceId,
                    currentVersion: APP_VERSION,
                    versionCode: VERSION_CODE,
                    platform: deviceInfo.platform
                };

                // 发送检查更新请求
                if (typeof NetworkModule !== 'undefined') {
                    NetworkModule.get("/v1/update/check", requestData, function(requestErr, response) {
                        if (requestErr) {
                            if (typeof LogModule !== 'undefined') {
                                LogModule.log("检查更新失败: " + requestErr.message, "ERROR");
                            }
                            if (callback) callback(requestErr, null);
                            return;
                        }

                        try {
                            if (response && response.success) {
                                const updateInfo = response.data;
                                
                                if (typeof LogModule !== 'undefined') {
                                    LogModule.log("检查更新成功: " + JSON.stringify(updateInfo));
                                }

                                // 判断是否有更新
                                if (updateInfo && updateInfo.hasUpdate) {
                                    if (typeof LogModule !== 'undefined') {
                                        LogModule.log("发现新版本: " + updateInfo.version);
                                    }
                                } else {
                                    if (typeof LogModule !== 'undefined') {
                                        LogModule.log("当前已是最新版本");
                                    }
                                }

                                if (callback) callback(null, updateInfo);
                            } else {
                                const responseErr = new Error(response ? response.message || "检查更新失败" : "服务器响应异常");
                                if (callback) callback(responseErr, null);
                            }
                        } catch (parseErr) {
                            if (typeof LogModule !== 'undefined') {
                                LogModule.log("解析更新信息失败: " + parseErr.message, "ERROR");
                            }
                            if (callback) callback(parseErr, null);
                        }
                    });
                } else {
                    const moduleErr = new Error("网络模块未初始化");
                    if (callback) callback(moduleErr, null);
                }

            } catch (checkErr) {
                if (typeof LogModule !== 'undefined') {
                    LogModule.log("检查更新异常: " + checkErr.message, "ERROR");
                }
                if (callback) callback(checkErr, null);
            }
        },

        /**
         * 下载更新
         */
        downloadUpdate: function (version, deviceId, callback, progressCallback) {
            try {
                if (typeof LogModule !== 'undefined') {
                    LogModule.log("开始下载更新: " + version);
                }

                // 构建下载URL
                const downloadUrl = "/v1/update/download/" + version + "?deviceId=" + encodeURIComponent(deviceId);
                
                // 构建保存路径
                const savePath = "/sdcard/脚本助手/updates/app_v" + version + ".apk";

                // 确保更新目录存在
                const updateDir = "/sdcard/脚本助手/updates";
                if (!files.exists(updateDir)) {
                    files.createWithDirs(updateDir + "/temp.txt");
                    files.remove(updateDir + "/temp.txt");
                }

                // 报告下载开始状态
                this.reportUpdateStatus(deviceId, APP_VERSION, version, UPDATE_STATUS.DOWNLOADING, null);

                // 使用网络模块下载文件
                if (typeof NetworkModule !== 'undefined') {
                    NetworkModule.downloadFile(downloadUrl, savePath, function(downloadErr, filePath) {
                        if (downloadErr) {
                            if (typeof LogModule !== 'undefined') {
                                LogModule.log("下载更新失败: " + downloadErr.message, "ERROR");
                            }
                            
                            // 报告下载失败状态
                            this.reportUpdateStatus(deviceId, APP_VERSION, version, UPDATE_STATUS.FAILED, downloadErr.message);
                            
                            if (callback) callback(downloadErr, null);
                            return;
                        }

                        if (typeof LogModule !== 'undefined') {
                            LogModule.log("更新下载完成: " + filePath);
                        }

                        if (callback) callback(null, filePath);
                    }, progressCallback);
                } else {
                    const networkErr = new Error("网络模块未初始化");
                    if (callback) callback(networkErr, null);
                }

            } catch (downloadException) {
                if (typeof LogModule !== 'undefined') {
                    LogModule.log("下载更新异常: " + downloadException.message, "ERROR");
                }
                if (callback) callback(downloadException, null);
            }
        },

        /**
         * 报告更新状态
         */
        reportUpdateStatus: function (deviceId, fromVersion, toVersion, status, errorMessage) {
            try {
                const statusData = {
                    deviceId: deviceId,
                    fromVersion: fromVersion,
                    toVersion: toVersion,
                    status: status,
                    timestamp: Date.now()
                };

                if (errorMessage) {
                    statusData.errorMessage = errorMessage;
                }

                if (typeof LogModule !== 'undefined') {
                    LogModule.log("报告更新状态: " + JSON.stringify(statusData));
                }

                // 发送状态报告
                if (typeof NetworkModule !== 'undefined') {
                    NetworkModule.post("/v1/update/status", statusData, function(statusErr, response) {
                        if (statusErr) {
                            if (typeof LogModule !== 'undefined') {
                                LogModule.log("报告更新状态失败: " + statusErr.message, "ERROR");
                            }
                        } else {
                            if (typeof LogModule !== 'undefined') {
                                LogModule.log("更新状态报告成功");
                            }
                        }
                    });
                }

            } catch (reportException) {
                if (typeof LogModule !== 'undefined') {
                    LogModule.log("报告更新状态异常: " + reportException.message, "ERROR");
                }
            }
        }
    };
})();

// 导出模块（AutoX.js兼容方式）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AppUpdateModule;
} else {
    // AutoX.js环境 - 将模块添加到全局作用域
    global.AppUpdateModule = AppUpdateModule;
    if (typeof window !== 'undefined') {
        window.AppUpdateModule = AppUpdateModule;
    }
}
