/**
 * 账户提现模块
 * 负责处理账户提现相关功能 - 完全按照原始main.js实现
 */

var WithdrawalModule = (function () {
    return {
        // 当前选中的提现金额
        currentAmount: 10,
        
        // 金额按钮数组
        amountButtons: [],

        /**
         * 创建提现页面 - 完全按照原始main.js实现
         */
        createWithdrawalPage: function () {
            try {
                if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                    global.LogModule.log("创建提现页面");
                }

                // 提现页面布局 - 使用可滚动布局，但不使用RecyclerView
                var withdrawalPageLayoutXml =
                    '<frame>' +
                    '<vertical h="*" w="*">' +
                    '<horizontal gravity="center_vertical" padding="16">' +
                    '<img src="@android:drawable/ic_menu_revert" w="24" h="24" tint="#009688" marginRight="16" id="backBtn"/>' +
                    '<text text="账户提现" textColor="#212121" textSize="18sp" layout_weight="1"/>' +
                    '</horizontal>' +

                    '<scroll id="mainScroll" layout_weight="1">' +
                    '<vertical padding="16">' +
                    '<card cardCornerRadius="8dp" cardElevation="2dp" margin="0 0 0 16">' +
                    '<vertical padding="16">' +
                    '<text text="可提现金额" textColor="#757575" textSize="14sp"/>' +
                    '<text id="availableAmountText" text="0.00元" textColor="#212121" textSize="24sp" textStyle="bold" margin="0 4"/>' +
                    '</vertical>' +
                    '</card>' +

                    '<text text="提现申请" textColor="#212121" textSize="16sp" margin="0 16 0 8"/>' +

                    '<card cardCornerRadius="8dp" cardElevation="2dp" margin="0 8">' +
                    '<vertical padding="16">' +
                    '<horizontal gravity="center_vertical" margin="0 8">' +
                    '<text text="提现金额" textColor="#212121" textSize="16sp" layout_weight="1"/>' +
                    '</horizontal>' +

                    '<vertical bg="#f5f5f5" padding="8" margin="0 8" radius="4dp">' +
                    '<text text="已选择金额" textColor="#757575" textSize="12sp"/>' +
                    '<horizontal gravity="center_vertical">' +
                    '<text text="¥" textColor="#212121" textSize="16sp"/>' +
                    '<text id="selectedAmountText" text="10.00" textColor="#212121" textSize="20sp" layout_weight="1"/>' +
                    '</horizontal>' +
                    '</vertical>' +

                    '<vertical id="amountButtonsContainer" margin="0 8">' +
                    '<!-- 金额按钮将由API动态加载 -->' +
                    '</vertical>' +

                    '<horizontal gravity="center_vertical" margin="0 16 0 8">' +
                    '<text text="支付宝账号" textColor="#212121" textSize="16sp" layout_weight="1"/>' +
                    '</horizontal>' +
                    '<input id="alipayAccountInput" hint="请输入支付宝账号" textSize="14sp" padding="8dp" margin="0 4" bg="#f5f5f5" h="45dp"/>' +

                    '<horizontal gravity="center_vertical" margin="0 16 0 8">' +
                    '<text text="支付宝实名" textColor="#212121" textSize="16sp" layout_weight="1"/>' +
                    '</horizontal>' +
                    '<input id="alipayNameInput" hint="请输入支付宝实名" textSize="14sp" padding="8dp" margin="0 4" bg="#f5f5f5" h="45dp"/>' +

                    '<button id="applyWithdrawalBtn" text="立即提现" textSize="16sp" textColor="#ffffff" bg="#FF5722" margin="0 16 0 8" padding="8dp" h="48dp"/>' +
                    '</vertical>' +
                    '</card>' +

                    '<text text="提现说明" textColor="#212121" textSize="16sp" margin="0 16 0 8"/>' +

                    '<card cardCornerRadius="8dp" cardElevation="2dp" margin="0 8">' +
                    '<vertical padding="16" id="withdrawalInstructionContainer">' +
                    '<text text="加载中..." textColor="#999999" textSize="14sp" gravity="center" padding="16"/>' +
                    '</vertical>' +
                    '</card>' +

                    '<text text="提现记录" textColor="#212121" textSize="16sp" margin="0 16 0 8"/>' +

                    '<card cardCornerRadius="8dp" cardElevation="2dp" margin="0 8 0 16">' +
                    '<vertical id="withdrawalRecordsContainer" padding="8" h="auto">' +
                    '<text text="加载中..." textColor="#999999" textSize="14sp" gravity="center" padding="16"/>' +
                    '</vertical>' +
                    '</card>' +

                    '<frame h="50" />' + // 底部留白，避免内容被遮挡
                    '</vertical>' +
                    '</scroll>' +
                    '</vertical>' +
                    '</frame>';

                // 设置UI布局
                if (typeof ui !== 'undefined' && ui.layout) {
                    ui.layout(withdrawalPageLayoutXml);
                } else {
                    console.error("UI对象不可用");
                    if (typeof toast !== 'undefined') {
                        toast("UI对象不可用");
                    }
                    return;
                }

                // 设置当前页面
                if (typeof global !== 'undefined' && global.UIModule && global.UIModule.setCurrentPage) {
                    global.UIModule.setCurrentPage("withdrawal");
                }

                // 绑定按钮事件
                this.bindWithdrawalPageEvents();

                // 加载可提现金额
                this.loadAvailableAmount();

                // 加载提现说明
                this.loadWithdrawalInstruction();

                // 加载提现金额选项
                this.loadWithdrawalAmountOptions();

                // 加载提现记录
                this.loadWithdrawalRecords();

            } catch (e) {
                if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                    global.LogModule.log("创建提现页面失败: " + e.message, "ERROR");
                }
                console.error("创建提现页面失败: " + e.message);
                console.error(e.stack);
                if (typeof toast !== 'undefined') {
                    toast("创建提现页面失败: " + e.message);
                }
            }
        },

        /**
         * 绑定提现页面事件
         */
        bindWithdrawalPageEvents: function () {
            var that = this;
            console.log("开始绑定提现页面事件");
            if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                global.LogModule.log("开始绑定提现页面事件", "INFO");
            }

            // 返回按钮事件
            if (ui.backBtn) {
                ui.backBtn.on("click", function () {
                    // 直接显示"我的"页面，不经过脚本中心
                    if (typeof global !== 'undefined' && global.UIModule && global.UIModule.showMyPageDirectly) {
                        global.UIModule.showMyPageDirectly();
                    } else if (typeof global !== 'undefined' && global.UIModule && global.UIModule.returnToScriptCenterMyTab) {
                        // 降级方案：使用原来的方法
                        global.UIModule.returnToScriptCenterMyTab();
                    } else {
                        console.error("UIModule 导航方法不可用");
                        if (typeof toast !== 'undefined') {
                            toast("返回失败，请重试");
                        }
                    }
                });
            }

            // 添加物理返回键监听器
            WithdrawalModule.setupBackKeyHandler();

            // 提现按钮事件
            if (ui.applyWithdrawalBtn) {
                ui.applyWithdrawalBtn.on("click", function () {
                    try {
                        var alipayAccount = ui.alipayAccountInput.text().trim();
                        var alipayName = ui.alipayNameInput.text().trim();

                        // 表单验证
                        if (!alipayAccount) {
                            if (typeof toast !== 'undefined') {
                                toast("请输入支付宝账号");
                            }
                            return;
                        }

                        if (!alipayName) {
                            if (typeof toast !== 'undefined') {
                                toast("请输入支付宝实名");
                            }
                            return;
                        }

                        // 验证提现金额
                        if (!that.currentAmount || that.currentAmount <= 0) {
                            if (typeof toast !== 'undefined') {
                                toast("请选择提现金额");
                            }
                            return;
                        }

                        // 验证可提现余额是否满足最小阈值
                        that.validateWithdrawalAmount(that.currentAmount, function(isValid, message) {
                            if (isValid) {
                                // 提交提现申请
                                that.applyWithdrawal(that.currentAmount, alipayAccount, alipayName);
                            } else {
                                if (typeof toast !== 'undefined') {
                                    toast(message || "提现金额验证失败");
                                }
                            }
                        });
                    } catch (e) {
                        if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                            global.LogModule.log("处理提现申请失败: " + e.message, "ERROR");
                        }
                        if (typeof toast !== 'undefined') {
                            toast("处理提现申请失败: " + e.message);
                        }
                    }
                });
            }
        },

        /**
         * 验证提现金额是否满足最小阈值
         * @param {number} amount - 要提现的金额
         * @param {function} callback - 回调函数，参数为(isValid, message)
         */
        validateWithdrawalAmount: function(amount, callback) {
            var that = this;

            try {
                console.log("开始验证提现金额: " + amount);

                // 获取当前可提现余额
                if (typeof global !== 'undefined' && global.NetworkModule && global.NetworkModule.getAvailableWithdrawalAmount) {
                    global.NetworkModule.getAvailableWithdrawalAmount(function (error, result) {
                        if (error) {
                            console.error("获取可提现余额失败: " + error.message);
                            if (callback) callback(false, "无法获取账户余额，请稍后重试");
                            return;
                        }

                        if (result && result.code === 200 && result.data !== undefined) {
                            // 处理服务器返回的数据格式：{"code":200,"message":"操作成功","data":0}
                            var availableAmount = parseFloat(result.data) || 0;
                            var minWithdrawalAmount = 10; // 默认最小提现金额为10元，可以后续从配置API获取

                            console.log("可提现余额: " + availableAmount + ", 最小提现金额: " + minWithdrawalAmount + ", 申请金额: " + amount);

                            // 验证余额是否充足
                            if (availableAmount < amount) {
                                if (callback) callback(false, "可提现余额不足，当前余额: " + availableAmount.toFixed(2) + "元");
                                return;
                            }

                            // 验证是否满足最小提现金额
                            if (amount < minWithdrawalAmount) {
                                if (callback) callback(false, "提现金额不能少于" + minWithdrawalAmount + "元");
                                return;
                            }

                            // 验证通过
                            if (callback) callback(true, "验证通过");
                        } else {
                            console.error("获取可提现余额响应异常: " + JSON.stringify(result));
                            if (callback) callback(false, "获取账户信息失败，请稍后重试");
                        }
                    });
                } else {
                    console.error("NetworkModule.getAvailableWithdrawalAmount 不可用");
                    if (callback) callback(false, "网络模块不可用，请稍后重试");
                }
            } catch (e) {
                console.error("验证提现金额失败: " + e.message);
                if (callback) callback(false, "验证失败: " + e.message);
            }
        },

        /**
         * 获取用户Token - 与其他模块保持一致的获取逻辑
         */
        getUserToken: function() {
            console.log("账户提现模块：开始获取用户Token...");

            // 优先从StorageModule获取Token（登录时保存的位置）
            if (typeof global !== 'undefined' && global.StorageModule) {
                console.log("账户提现模块：尝试从StorageModule获取Token...");
                var token = global.StorageModule.get("userToken");
                if (token) {
                    console.log("账户提现模块：从StorageModule获取到Token: " + token.substring(0, 10) + "...");
                    return token;
                } else {
                    console.log("账户提现模块：StorageModule中没有找到Token");
                }
            } else {
                console.log("账户提现模块：StorageModule不可用");
            }

            // 降级到ConfigModule
            if (typeof global !== 'undefined' && global.ConfigModule) {
                console.log("账户提现模块：尝试从ConfigModule获取Token...");
                var token = global.ConfigModule.get("userToken");
                if (token) {
                    console.log("账户提现模块：从ConfigModule获取到Token: " + token.substring(0, 10) + "...");
                    return token;
                } else {
                    console.log("账户提现模块：ConfigModule中没有找到Token");
                }
            } else {
                console.log("账户提现模块：ConfigModule不可用");
            }

            // 最后尝试从currentUser全局变量获取
            if (typeof currentUser !== 'undefined' && currentUser && currentUser.token) {
                console.log("账户提现模块：从currentUser获取到Token: " + currentUser.token.substring(0, 10) + "...");
                return currentUser.token;
            } else {
                console.log("账户提现模块：currentUser中没有Token");
            }

            console.warn("账户提现模块：未能获取到用户Token");
            return null;
        },

        /**
         * 设置物理返回键处理
         */
        setupBackKeyHandler: function () {
            try {
                // 监听返回键事件
                if (typeof ui !== 'undefined' && ui.emitter) {
                    ui.emitter.on("back_pressed", function (e) {
                        try {
                            // 检查当前是否在账户提现页面
                            if (typeof global !== 'undefined' && global.UIModule && global.UIModule.getCurrentPage) {
                                var currentPage = global.UIModule.getCurrentPage();
                                if (currentPage === "withdrawal") {
                                    // 阻止默认返回行为
                                    e.consumed = true;

                                    // 执行返回操作 - 直接显示"我的"页面
                                    if (global.UIModule.showMyPageDirectly) {
                                        global.UIModule.showMyPageDirectly();
                                    } else if (global.UIModule.returnToScriptCenterMyTab) {
                                        // 降级方案
                                        global.UIModule.returnToScriptCenterMyTab();
                                    } else {
                                        console.error("UIModule 导航方法不可用");
                                        if (typeof toast !== 'undefined') {
                                            toast("返回失败，请重试");
                                        }
                                    }
                                }
                            }
                        } catch (error) {
                            console.error("处理返回键事件失败: " + error.message);
                        }
                    });
                }

                // 监听按键事件（备用方案）
                if (typeof events !== 'undefined') {
                    events.on("key_down", function (keyCode, event) {
                        try {
                            if (keyCode === keys.back) {
                                // 检查当前是否在账户提现页面
                                if (typeof global !== 'undefined' && global.UIModule && global.UIModule.getCurrentPage) {
                                    var currentPage = global.UIModule.getCurrentPage();
                                    if (currentPage === "withdrawal") {
                                        // 执行返回操作 - 直接显示"我的"页面
                                        if (global.UIModule.showMyPageDirectly) {
                                            global.UIModule.showMyPageDirectly();
                                        } else if (global.UIModule.returnToScriptCenterMyTab) {
                                            global.UIModule.returnToScriptCenterMyTab();
                                        }
                                        return true; // 阻止默认行为
                                    }
                                }
                            }
                        } catch (error) {
                            console.error("处理按键事件失败: " + error.message);
                        }
                        return false;
                    });
                }

                console.log("账户提现页面返回键处理设置完成");
            } catch (e) {
                console.error("设置返回键处理失败: " + e.message);
            }
        },

        /**
         * 加载可提现金额
         */
        loadAvailableAmount: function () {
            var that = this;

            if (typeof global !== 'undefined' && global.NetworkModule && global.NetworkModule.getAvailableWithdrawalAmount) {
                global.NetworkModule.getAvailableWithdrawalAmount(function (error, result) {
                    if (error) {
                        if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                            global.LogModule.log("获取可提现金额失败: " + error.message, "ERROR");
                        }
                        if (typeof toast !== 'undefined') {
                            toast("获取可提现金额失败");
                        }
                        return;
                    }

                    if (result && result.code === 200 && result.data !== undefined) {
                        // 处理服务器返回的数据格式：{"code":200,"message":"操作成功","data":0}
                        var availableAmount = parseFloat(result.data) || 0;
                        console.log("获取到可提现余额: " + availableAmount);

                        // 更新UI显示
                        if (typeof ui !== 'undefined' && ui.run && ui.availableAmountText) {
                            ui.run(function () {
                                ui.availableAmountText.setText(availableAmount.toFixed(2) + "元");
                            });
                        }
                    } else {
                        console.error("获取可提现余额响应异常: " + JSON.stringify(result));
                        // 使用默认值
                        if (typeof ui !== 'undefined' && ui.run && ui.availableAmountText) {
                            ui.run(function () {
                                ui.availableAmountText.setText("0.00元");
                            });
                        }
                    }
                });
            } else {
                console.error("NetworkModule.getAvailableWithdrawalAmount 不可用");
                // 使用默认值
                if (typeof ui !== 'undefined' && ui.run && ui.availableAmountText) {
                    ui.run(function () {
                        ui.availableAmountText.setText("0.00元");
                    });
                }
            }
        },

        /**
         * 加载提现说明
         */
        loadWithdrawalInstruction: function () {
            var that = this;

            if (typeof global !== 'undefined' && global.NetworkModule && global.NetworkModule.getWithdrawalInstruction) {
                global.NetworkModule.getWithdrawalInstruction(function (error, result) {
                    if (error) {
                        if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                            global.LogModule.log("获取提现说明失败: " + error.message, "ERROR");
                        }
                        return;
                    }

                    if (result && result.code === 200 && result.data) {
                        // 更新UI内容
                        if (typeof ui !== 'undefined' && ui.run) {
                            ui.run(function () {
                                try {
                                    // 获取提现说明容器
                                    var instructionContainer = ui.findById("withdrawalInstructionContainer");
                                    if (instructionContainer) {
                                        // 清空容器
                                        instructionContainer.removeAllViews();

                                        // 将后端返回的说明文本按行分割
                                        var instructionLines = result.data.split('\n');
                                        for (var i = 0; i < instructionLines.length; i++) {
                                            var line = instructionLines[i].trim();
                                            if (line) {
                                                let lineView = ui.inflate(
                                                    '<text text="' + line + '" textColor="#666666" textSize="14sp" margin="0 4"/>',
                                                    instructionContainer
                                                );
                                                instructionContainer.addView(lineView);
                                            }
                                        }
                                    }
                                } catch (e) {
                                    console.error("更新提现说明UI失败: " + e.message);
                                }
                            });
                        }
                    } else {
                        // 显示默认说明
                        that.showDefaultWithdrawalInstruction();
                    }
                });
            } else {
                console.error("NetworkModule.getWithdrawalInstruction 不可用");
                this.showDefaultWithdrawalInstruction();
            }
        },

        /**
         * 显示默认提现说明
         */
        showDefaultWithdrawalInstruction: function () {
            try {
                if (typeof ui !== 'undefined' && ui.run) {
                    ui.run(function () {
                        try {
                            var instructionContainer = ui.findById("withdrawalInstructionContainer");
                            if (instructionContainer) {
                                instructionContainer.removeAllViews();

                                var defaultInstructions = [
                                    "1. 提现金额最低10元，最高100元",
                                    "2. 提现申请提交后，1-3个工作日内到账",
                                    "3. 请确保支付宝账号和实名信息正确",
                                    "4. 每日最多可申请1次提现",
                                    "5. 如有问题请联系客服"
                                ];

                                for (let i = 0; i < defaultInstructions.length; i++) {
                                    let lineView = ui.inflate(
                                        '<text text="' + defaultInstructions[i] + '" textColor="#666666" textSize="14sp" margin="0 4"/>',
                                        instructionContainer
                                    );
                                    instructionContainer.addView(lineView);
                                }
                            }
                        } catch (e) {
                            console.error("显示默认提现说明失败: " + e.message);
                        }
                    });
                }
            } catch (e) {
                console.error("显示默认提现说明失败: " + e.message);
            }
        },

        /**
         * 加载提现金额选项
         */
        loadWithdrawalAmountOptions: function () {
            var that = this;

            console.log("开始加载提现金额选项...");

            if (typeof global !== 'undefined' && global.NetworkModule && global.NetworkModule.getWithdrawalAmountOptions) {
                global.NetworkModule.getWithdrawalAmountOptions(function (error, result) {
                    if (error) {
                        console.error("获取提现金额选项失败: " + error.message);
                        console.error(error.stack);
                        if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                            global.LogModule.log("获取提现金额选项失败: " + error.message, "ERROR");
                        }

                        // 使用默认金额选项
                        that.createDefaultAmountButtons();
                        return;
                    }

                    if (result && result.code === 200 && result.data && result.data.length > 0) {
                        // 解析后端返回的金额选项数据
                        console.log("解析提现金额选项数据: " + JSON.stringify(result.data));

                        try {
                            // 提取金额数组并按sortOrder排序
                            var amountOptions = result.data
                                .map(function(item) {
                                    return {
                                        amount: parseFloat(item.amount) || 0,
                                        sortOrder: parseInt(item.sortOrder) || 0
                                    };
                                })
                                .sort(function(a, b) {
                                    return a.sortOrder - b.sortOrder;
                                })
                                .map(function(item) {
                                    return item.amount;
                                });

                            console.log("解析后的金额选项: " + JSON.stringify(amountOptions));

                            if (amountOptions.length > 0) {
                                // 设置默认选中金额为第一个选项
                                that.currentAmount = amountOptions[0];

                                // 确保UI更新在主线程中执行
                                if (typeof ui !== 'undefined' && ui.run) {
                                    ui.run(function() {
                                        try {
                                            // 更新显示金额
                                            if (ui.selectedAmountText) {
                                                ui.selectedAmountText.setText(that.currentAmount.toFixed(2));
                                            }

                                            // 使用后端返回的金额选项
                                            that.createAmountButtons(amountOptions);
                                        } catch (uiError) {
                                            console.error("UI更新失败: " + uiError.message);
                                            // 降级到默认选项
                                            that.createDefaultAmountButtons();
                                        }
                                    });
                                } else {
                                    // UI不可用时的降级处理
                                    console.error("UI对象不可用，无法更新金额选项");
                                    that.createDefaultAmountButtons();
                                }
                            } else {
                                console.log("解析后的金额选项为空，使用默认选项");
                                that.createDefaultAmountButtons();
                            }
                        } catch (parseError) {
                            console.error("解析金额选项数据失败: " + parseError.message);
                            that.createDefaultAmountButtons();
                        }
                    } else {
                        console.log("后端未返回有效的金额选项，使用默认选项");
                        that.createDefaultAmountButtons();
                    }
                });
            } else {
                console.error("NetworkModule.getWithdrawalAmountOptions 不可用");
                this.createDefaultAmountButtons();
            }
        },

        /**
         * 创建默认金额按钮
         */
        createDefaultAmountButtons: function () {
            var that = this;
            var defaultAmounts = [10, 20, 30, 40, 50, 100];

            // 设置默认选中金额为第一个选项
            this.currentAmount = defaultAmounts[0];

            // 确保UI更新在主线程中执行
            if (typeof ui !== 'undefined' && ui.run) {
                ui.run(function() {
                    try {
                        // 更新显示金额
                        if (ui.selectedAmountText) {
                            ui.selectedAmountText.setText(that.currentAmount.toFixed(2));
                        }

                        // 创建默认金额按钮
                        that.createAmountButtons(defaultAmounts);
                    } catch (uiError) {
                        console.error("创建默认金额按钮UI更新失败: " + uiError.message);
                    }
                });
            } else {
                console.error("UI对象不可用，无法创建默认金额按钮");
            }
        },

        /**
         * 创建金额按钮
         */
        createAmountButtons: function (amounts) {
            var that = this;

            try {
                if (typeof ui !== 'undefined' && ui.run) {
                    ui.run(function () {
                        try {
                            var container = ui.findById("amountButtonsContainer");
                            if (!container) {
                                console.error("找不到金额按钮容器");
                                return;
                            }

                            // 清空容器
                            container.removeAllViews();

                            // 创建按钮行
                            var buttonsPerRow = 3;
                            let currentRow = null;

                            for (var i = 0; i < amounts.length; i++) {
                                var amount = amounts[i];

                                // 每3个按钮创建一行
                                if (i % buttonsPerRow === 0) {
                                    currentRow = ui.inflate(
                                        '<horizontal gravity="center" margin="4 8"/>',
                                        container
                                    );
                                    container.addView(currentRow);
                                }

                                // 创建按钮
                                var buttonId = "amountBtn_" + amount;
                                var isSelected = amount === that.currentAmount;
                                var buttonBg = isSelected ? "#FF5722" : "#E0E0E0";
                                var textColor = isSelected ? "#FFFFFF" : "#333333";

                                var buttonXml =
                                    '<button id="' + buttonId + '" text="' + amount + '元" ' +
                                    'textSize="14sp" textColor="' + textColor + '" bg="' + buttonBg + '" ' +
                                    'margin="4" padding="8" layout_weight="1" h="40dp"/>';

                                var button = ui.inflate(buttonXml, currentRow);
                                currentRow.addView(button);

                                // 绑定点击事件
                                (function(currentAmount) {
                                    button.on("click", function () {
                                        that.updateSelectedAmount(currentAmount);
                                        that.updateAmountButtonStyles(currentAmount);
                                    });
                                })(amount);
                            }
                        } catch (e) {
                            console.error("创建金额按钮失败: " + e.message);
                        }
                    });
                }
            } catch (e) {
                console.error("创建金额按钮失败: " + e.message);
            }
        },

        /**
         * 更新选中的金额
         */
        updateSelectedAmount: function (amount) {
            var that = this;
            this.currentAmount = amount;

            // 确保UI更新在主线程中执行
            if (typeof ui !== 'undefined' && ui.run) {
                ui.run(function() {
                    try {
                        if (ui.selectedAmountText) {
                            ui.selectedAmountText.setText(amount.toFixed(2));
                        }
                    } catch (uiError) {
                        console.error("更新选中金额UI失败: " + uiError.message);
                    }
                });
            }
        },

        /**
         * 更新金额按钮样式
         */
        updateAmountButtonStyles: function (selectedAmount) {
            try {
                if (typeof ui !== 'undefined' && ui.run) {
                    ui.run(function () {
                        try {
                            var container = ui.findById("amountButtonsContainer");
                            if (container) {
                                // 遍历所有按钮，更新样式
                                for (var i = 0; i < container.getChildCount(); i++) {
                                    var row = container.getChildAt(i);
                                    if (row && row.getChildCount) {
                                        for (var j = 0; j < row.getChildCount(); j++) {
                                            var button = row.getChildAt(j);
                                            if (button && button.getText) {
                                                var buttonText = button.getText().toString();
                                                var buttonAmount = parseInt(buttonText.replace('元', ''));

                                                if (buttonAmount === selectedAmount) {
                                                    // 选中状态
                                                    button.setBackgroundColor(android.graphics.Color.parseColor("#FF5722"));
                                                    button.setTextColor(android.graphics.Color.parseColor("#FFFFFF"));
                                                } else {
                                                    // 未选中状态
                                                    button.setBackgroundColor(android.graphics.Color.parseColor("#E0E0E0"));
                                                    button.setTextColor(android.graphics.Color.parseColor("#333333"));
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        } catch (e) {
                            console.error("更新按钮样式失败: " + e.message);
                        }
                    });
                }
            } catch (e) {
                console.error("更新金额按钮样式失败: " + e.message);
            }
        },

        /**
         * 加载提现记录
         */
        loadWithdrawalRecords: function () {
            var that = this;

            if (typeof global !== 'undefined' && global.NetworkModule && global.NetworkModule.getWithdrawalRecords) {
                global.NetworkModule.getWithdrawalRecords(function (error, result) {
                    if (error) {
                        if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                            global.LogModule.log("获取提现记录失败: " + error.message, "ERROR");
                        }
                        if (typeof toast !== 'undefined') {
                            toast("获取提现记录失败");
                        }
                        that.showWithdrawalRecordsError();
                        return;
                    }

                    that.updateWithdrawalRecordsUI(result);
                });
            } else {
                console.error("NetworkModule.getWithdrawalRecords 不可用");
                this.showWithdrawalRecordsError();
            }
        },

        /**
         * 显示提现记录错误信息
         */
        showWithdrawalRecordsError: function () {
            try {
                if (typeof ui !== 'undefined' && ui.run) {
                    ui.run(function () {
                        try {
                            var container = ui.findById("withdrawalRecordsContainer");
                            if (container) {
                                container.removeAllViews();
                                let errorView = ui.inflate(
                                    '<text text="获取提现记录失败" textColor="#F44336" textSize="14sp" gravity="center" padding="16"/>',
                                    container
                                );
                                container.addView(errorView);
                            }
                        } catch (e) {
                            console.error("显示提现记录错误信息失败: " + e.message);
                        }
                    });
                }
            } catch (e) {
                console.error("显示提现记录错误信息失败: " + e.message);
            }
        },

        /**
         * 更新提现记录UI
         */
        updateWithdrawalRecordsUI: function (result) {
            try {
                if (typeof ui !== 'undefined' && ui.run) {
                    ui.run(function () {
                        try {
                            // 清空容器
                            var container = ui.findById("withdrawalRecordsContainer");
                            if (!container) {
                                console.error("找不到提现记录容器");
                                return;
                            }

                            container.removeAllViews();

                            if (!result || result.code !== 200 || !result.data || result.data.length === 0) {
                                // 处理空记录情况
                                let emptyView = ui.inflate(
                                    '<text text="暂无提现记录" textColor="#999999" textSize="14sp" gravity="center" padding="16"/>',
                                    container
                                );
                                container.addView(emptyView);
                                return;
                            }

                            // 限制最多显示10条记录
                            var maxRecords = Math.min(result.data.length, 10);

                            // 添加记录项的函数
                            var addRecordItem = function (recordItem, index) {
                                // 状态颜色映射
                                let statusColor = "#999999";
                                if (recordItem.status === 1) {
                                    statusColor = "#FF9800"; // 待处理 - 橙色
                                } else if (recordItem.status === 2) {
                                    statusColor = "#4CAF50"; // 已完成 - 绿色
                                } else if (recordItem.status === 3) {
                                    statusColor = "#F44336"; // 已拒绝 - 红色
                                }

                                // 创建记录项视图
                                let recordView = ui.inflate(
                                    '<vertical padding="8">' +
                                    '<horizontal gravity="center_vertical">' +
                                    '<text id="amountText" text="" textColor="#212121" textSize="16sp" textStyle="bold" layout_weight="1"/>' +
                                    '<text id="statusText" text="" textSize="14sp"/>' +
                                    '</horizontal>' +
                                    '<text id="timeText" text="" textColor="#757575" textSize="14sp" margin="0 4"/>' +
                                    '</vertical>',
                                    container
                                );

                                // 设置记录项数据
                                recordView.amountText.setText(recordItem.amount + "元");
                                recordView.timeText.setText(recordItem.createTime || "");
                                recordView.statusText.setText(recordItem.statusName || "");
                                recordView.statusText.setTextColor(android.graphics.Color.parseColor(statusColor));

                                // 添加到容器
                                container.addView(recordView);

                                // 如果不是最后一项，添加分隔线
                                if (index < result.data.length - 1) {
                                    let dividerView = ui.inflate(
                                        '<frame h="1" w="*" bg="#E0E0E0" margin="12 8 12 0"/>',
                                        container
                                    );
                                    container.addView(dividerView);
                                }
                            };

                            // 添加初始记录
                            for (let i = 0; i < maxRecords; i++) {
                                addRecordItem(result.data[i], i);
                            }

                            // 如果有更多记录，显示"查看更多"按钮
                            if (result.data.length > maxRecords) {
                                let moreView = ui.inflate(
                                    '<button text="查看更多" textColor="#2196F3" textSize="14sp" style="Widget.AppCompat.Button.Borderless" w="*" padding="8"/>',
                                    container
                                );
                                container.addView(moreView);

                                // 添加点击事件
                                moreView.on("click", function () {
                                    try {
                                        if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                                            global.LogModule.log("用户点击查看更多提现记录", "INFO");
                                        }

                                        // 移除"查看更多"按钮
                                        container.removeView(moreView);

                                        // 计算要显示的额外记录数量
                                        var totalRecords = result.data.length;
                                        var currentShown = maxRecords;
                                        var remainingRecords = totalRecords - currentShown;

                                        // 添加剩余记录
                                        for (let i = currentShown; i < totalRecords; i++) {
                                            addRecordItem(result.data[i], i);
                                        }
                                    } catch (e) {
                                        console.error("展开更多记录失败: " + e.message);
                                    }
                                });
                            }

                        } catch (e) {
                            console.error("设置提现记录失败: " + e.message);
                            console.error(e.stack);
                            if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                                global.LogModule.log("设置提现记录失败: " + e.message, "ERROR");
                                global.LogModule.log(e.stack, "ERROR");
                            }

                            // 显示错误信息
                            var container = ui.findById("withdrawalRecordsContainer");
                            if (container) {
                                container.removeAllViews();
                                let errorView = ui.inflate(
                                    '<text text="加载提现记录出错" textColor="#F44336" textSize="14sp" gravity="center" padding="16"/>',
                                    container
                                );
                                container.addView(errorView);
                            }
                        }
                    });
                }
            } catch (e) {
                console.error("更新提现记录UI失败: " + e.message);
            }
        },

        /**
         * 申请提现
         * @param {number} amount - 提现金额
         * @param {string} alipayAccount - 支付宝账号
         * @param {string} alipayName - 支付宝实名
         */
        applyWithdrawal: function (amount, alipayAccount, alipayName) {
            var that = this;

            // 显示加载对话框
            this.showDialog("提交中", "正在提交提现申请，请稍候...");

            if (typeof global !== 'undefined' && global.NetworkModule && global.NetworkModule.applyWithdrawal) {
                global.NetworkModule.applyWithdrawal(amount, alipayAccount, alipayName, function (error, result) {
                    // 关闭加载对话框
                    that.dismissDialog();

                    if (error) {
                        if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                            global.LogModule.log("申请提现失败: " + error.message, "ERROR");
                        }
                        if (typeof toast !== 'undefined') {
                            toast("申请提现失败: " + error.message);
                        }
                        return;
                    }

                    if (result && result.code === 200) {
                        if (typeof toast !== 'undefined') {
                            toast("提现申请已提交");
                        }

                        // 重新加载数据
                        that.loadAvailableAmount();
                        that.loadWithdrawalRecords();
                    } else {
                        var errorMsg = result && result.message ? result.message : "未知错误";
                        if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                            global.LogModule.log("申请提现失败: " + errorMsg, "ERROR");
                        }
                        if (typeof toast !== 'undefined') {
                            toast("申请提现失败: " + errorMsg);
                        }
                    }
                });
            } else {
                this.dismissDialog();
                console.error("NetworkModule.applyWithdrawal 不可用");
                if (typeof toast !== 'undefined') {
                    toast("提现功能暂时不可用");
                }
            }
        },

        /**
         * 显示对话框
         */
        showDialog: function (title, message) {
            try {
                if (typeof ui !== 'undefined' && ui.run && typeof dialogs !== 'undefined') {
                    ui.run(function () {
                        // 存储对话框引用，用于后续关闭
                        WithdrawalModule.currentDialog = dialogs.build({
                            title: title,
                            content: message,
                            cancelable: false
                        }).show();
                    });
                } else {
                    console.error("UI或dialogs对象不可用");
                    if (typeof toast !== 'undefined') {
                        toast(message);
                    }
                }
            } catch (e) {
                console.error("显示对话框失败: " + e.message);
                if (typeof toast !== 'undefined') {
                    toast(message);
                }
            }
        },

        /**
         * 关闭对话框
         */
        dismissDialog: function () {
            try {
                if (this.currentDialog) {
                    if (typeof ui !== 'undefined' && ui.run) {
                        ui.run(function () {
                            try {
                                WithdrawalModule.currentDialog.dismiss();
                                WithdrawalModule.currentDialog = null;
                            } catch (e) {
                                console.error("关闭对话框失败: " + e.message);
                            }
                        });
                    }
                }
            } catch (e) {
                console.error("关闭对话框失败: " + e.message);
            }
        },

        /**
         * 全局函数 - 显示提现页面
         */
        showWithdrawalPage: function () {
            try {
                console.log("全局函数showWithdrawalPage被调用");
                if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                    global.LogModule.log("全局函数showWithdrawalPage被调用", "INFO");
                }

                // 确保UIModule已定义
                if (typeof global === 'undefined' || !global.WithdrawalModule) {
                    console.error("WithdrawalModule未定义");
                    if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                        global.LogModule.log("WithdrawalModule未定义", "ERROR");
                    }
                    if (typeof toast !== 'undefined') {
                        toast("提现模块未加载");
                    }
                    return;
                }

                // 在UI线程中创建提现页面
                if (typeof ui !== 'undefined' && ui.run) {
                    ui.run(function () {
                        try {
                            console.log("准备在UI线程中创建提现页面");
                            if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                                global.LogModule.log("准备在UI线程中创建提现页面", "INFO");
                            }

                            // 尝试创建提现页面
                            global.WithdrawalModule.createWithdrawalPage();
                            console.log("WithdrawalModule.createWithdrawalPage()调用完成 - 全局函数");
                            if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                                global.LogModule.log("提现页面创建完成", "INFO");
                            }
                        } catch (e) {
                            console.error("UI线程中执行createWithdrawalPage失败: " + e.message);
                            console.error(e.stack);
                            if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                                global.LogModule.log("UI线程中执行createWithdrawalPage失败: " + e.message, "ERROR");
                                global.LogModule.log(e.stack, "ERROR");
                            }
                            if (typeof toast !== 'undefined') {
                                toast("加载提现页面失败: " + e.message);
                            }
                        }
                    });
                }
            } catch (e) {
                console.error("全局函数showWithdrawalPage执行失败: " + e.message);
                console.error(e.stack);
                if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                    global.LogModule.log("全局函数showWithdrawalPage执行失败: " + e.message, "ERROR");
                    global.LogModule.log(e.stack, "ERROR");
                }
                if (typeof toast !== 'undefined') {
                    toast("加载提现页面失败: " + e.message);
                }
            }
        }
    };
})();

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = WithdrawalModule;
} else if (typeof global !== 'undefined') {
    global.WithdrawalModule = WithdrawalModule;
}

// 创建全局函数，保持与原版的兼容性
if (typeof global !== 'undefined') {
    global.showWithdrawalPage = function() {
        if (global.WithdrawalModule && global.WithdrawalModule.showWithdrawalPage) {
            global.WithdrawalModule.showWithdrawalPage();
        } else {
            console.error("WithdrawalModule 不可用");
            if (typeof toast !== 'undefined') {
                toast("提现功能暂时不可用");
            }
        }
    };
}
