/**
 * 使用教程模块
 * 负责处理使用教程相关功能
 */

const TutorialModule = (function () {
    return {
        /**
         * 创建使用教程界面 - 完全按照原始main.js实现
         */
        createTutorialUI: function () {
            try {
                if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                    global.LogModule.log("开始创建使用教程界面", "INFO");
                }
                var that = this;

                // 获取使用教程数据
                if (typeof global !== 'undefined' && global.NetworkModule && global.NetworkModule.get) {
                    global.NetworkModule.get("/system/config/key/TUTORIAL_LINK", null, function (error, result) {
                        if (error) {
                            console.error("获取使用教程数据失败: " + error.message);
                            if (typeof toast !== 'undefined') {
                                toast("获取使用教程数据失败，请检查网络连接");
                            }
                            TutorialModule.showDefaultTutorial();
                            return;
                        }

                        if (result && result.code === 200 && result.data && result.data.configValue) {
                            var tutorialLink = result.data.configValue;
                            TutorialModule.createTutorialWebView(tutorialLink);
                        } else {
                            console.error("获取使用教程数据失败: " + (result ? result.message : "未知错误"));
                            if (typeof toast !== 'undefined') {
                                toast("获取使用教程数据失败: " + (result ? result.message : "未知错误"));
                            }
                            TutorialModule.showDefaultTutorial();
                        }
                    });
                } else {
                    console.error("NetworkModule 不可用");
                    this.showDefaultTutorial();
                }
            } catch (e) {
                console.error("创建使用教程界面失败: " + e.message);
                console.error(e.stack);
                if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                    global.LogModule.log("创建使用教程界面失败: " + e.message, "ERROR");
                }
                this.showDefaultTutorial();
            }
        },

        /**
         * 创建包含WebView的教程界面
         */
        createTutorialWebView: function (tutorialLink) {
            try {
                // 创建包含webview控件的UI布局
                var tutorialLayoutXml =
                    '<frame>' +
                    '<vertical padding="0" bg="#FAFAFA" h="*" w="*">' +
                    '<horizontal gravity="center_vertical" padding="10">' +
                    '<img src="@android:drawable/ic_menu_revert" w="24" h="24" tint="#333333" id="tutorialBackBtn"/>' +
                    '<text text="使用教程" textSize="18sp" textColor="#333333" gravity="center" layout_weight="1" textStyle="bold"/>' +
                    '<frame w="60dp" visibility="invisible"/>' +
                    '</horizontal>' +

                    // 使用webview控件显示网页内容
                    '<webview id="tutorialWebview" layout_weight="1" h="*" w="*"/>' +
                    '</vertical>' +
                    '</frame>';

                // 使用安全的UI创建方法
                this.safeCreateUI(tutorialLayoutXml, function () {
                    try {
                        // 设置当前页面状态
                        if (typeof global !== 'undefined' && global.UIModule && global.UIModule.setCurrentPage) {
                            global.UIModule.setCurrentPage("tutorial");
                        }

                        // 绑定事件
                        TutorialModule.bindEvents();

                        // 加载网页
                        if (ui.tutorialWebview) {
                            ui.tutorialWebview.loadUrl(tutorialLink);
                            
                            // 显示加载提示
                            if (typeof toast !== 'undefined') {
                                toast("正在加载教程页面...");
                            }
                        }
                    } catch (e) {
                        console.error("使用教程界面事件绑定失败: " + e.message);
                        console.error(e.stack);
                        if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                            global.LogModule.log("使用教程界面事件绑定失败: " + e.message, "ERROR");
                        }

                        // 降级处理：使用外部浏览器打开
                        try {
                            if (typeof app !== 'undefined') {
                                app.openUrl(tutorialLink);
                            } else {
                                console.error("app对象不可用");
                                if (typeof toast !== 'undefined') {
                                    toast("无法打开链接，请手动访问: " + tutorialLink);
                                }
                            }
                        } catch (browserError) {
                            if (typeof toast !== 'undefined') {
                                toast("无法打开链接，请手动访问: " + tutorialLink);
                            }
                        }
                    }
                });
            } catch (e) {
                console.error("创建教程WebView失败: " + e.message);
                this.showDefaultTutorial();
            }
        },

        /**
         * 安全创建UI的方法
         */
        safeCreateUI: function (layoutXml, callback) {
            try {
                if (typeof ui !== 'undefined' && ui.layout) {
                    ui.layout(layoutXml);
                    if (callback && typeof callback === 'function') {
                        // 延迟执行回调，确保UI已完全加载
                        setTimeout(callback, 100);
                    }
                } else {
                    console.error("UI对象不可用");
                    if (typeof toast !== 'undefined') {
                        toast("UI对象不可用");
                    }
                }
            } catch (e) {
                console.error("安全创建UI失败: " + e.message);
                if (typeof toast !== 'undefined') {
                    toast("创建界面失败: " + e.message);
                }
            }
        },

        /**
         * 绑定事件处理
         */
        bindEvents: function () {
            try {
                // 返回按钮点击事件
                if (ui.tutorialBackBtn) {
                    ui.tutorialBackBtn.on("click", function () {
                        if (typeof global !== 'undefined' && global.UIModule && global.UIModule.createScriptCenterUI) {
                            global.UIModule.createScriptCenterUI(false);
                        } else {
                            console.error("UIModule.createScriptCenterUI 方法不可用");
                            if (typeof toast !== 'undefined') {
                                toast("返回失败，请重试");
                            }
                        }
                        return true; // 阻止事件继续传播
                    });
                }

            } catch (e) {
                console.error("绑定使用教程事件失败: " + e.message);
            }
        },

        /**
         * 显示默认教程内容
         */
        showDefaultTutorial: function () {
            try {
                var defaultTutorialLink = "https://www.example.com/tutorial";
                
                // 尝试使用外部浏览器打开默认教程链接
                if (typeof app !== 'undefined') {
                    app.openUrl(defaultTutorialLink);
                    if (typeof toast !== 'undefined') {
                        toast("正在打开使用教程...");
                    }
                } else {
                    // 如果无法打开外部浏览器，显示对话框
                    this.showTutorialDialog();
                }
            } catch (e) {
                console.error("显示默认教程失败: " + e.message);
                this.showTutorialDialog();
            }
        },

        /**
         * 显示教程对话框
         */
        showTutorialDialog: function () {
            try {
                if (typeof ui !== 'undefined' && ui.run && typeof dialogs !== 'undefined') {
                    ui.run(function () {
                        dialogs.build({
                            title: "使用教程",
                            content: "脚本助手使用教程\n\n" +
                                "1. 登录注册\n" +
                                "   - 首次使用需要注册账号\n" +
                                "   - 输入手机号获取验证码\n" +
                                "   - 设置密码完成注册\n\n" +
                                "2. 功能使用\n" +
                                "   - 微信阅读：自动化阅读任务\n" +
                                "   - 积分系统：查看和兑换积分\n" +
                                "   - 推广分销：邀请好友获得收益\n\n" +
                                "3. 常见问题\n" +
                                "   - 确保网络连接正常\n" +
                                "   - 授予必要的系统权限\n" +
                                "   - 遇到问题请联系客服\n\n" +
                                "更多详细教程请访问官网。",
                            positive: "访问官网",
                            negative: "关闭",
                            cancelable: true
                        })
                            .on("positive", function () {
                                if (typeof app !== 'undefined') {
                                    app.openUrl("https://www.example.com/tutorial");
                                } else {
                                    console.error("app对象不可用");
                                    if (typeof toast !== 'undefined') {
                                        toast("无法打开官网链接");
                                    }
                                }
                            })
                            .show();
                    });
                } else {
                    console.error("UI或dialogs对象不可用");
                    if (typeof toast !== 'undefined') {
                        toast("使用教程功能暂时不可用");
                    }
                }
            } catch (e) {
                console.error("显示教程对话框失败: " + e.message);
                if (typeof toast !== 'undefined') {
                    toast("使用教程功能暂时不可用");
                }
            }
        }
    };
})();

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TutorialModule;
} else if (typeof global !== 'undefined') {
    global.TutorialModule = TutorialModule;
}
