/**
 * 网络模块
 * 负责处理所有网络请求功能
 */

var NetworkModule = (function () {
    // 请求超时时间（毫秒）
    var REQUEST_TIMEOUT = typeof NETWORK_CONFIG !== 'undefined' ? NETWORK_CONFIG.TIMEOUT : 10000;

    // API基础URL
    var API_BASE_URL = typeof API_CONFIG !== 'undefined' ? API_CONFIG.BASE_URL : "http://192.168.1.19:8527/api";

    // 最大重试次数
    var MAX_RETRIES = typeof NETWORK_CONFIG !== 'undefined' ? NETWORK_CONFIG.MAX_RETRIES : 3;

    return {
        /**
         * 初始化网络模块
         */
        init: function () {
            if (typeof LogModule !== 'undefined') {
                LogModule.log("网络模块初始化");
                LogModule.log("API基础URL: " + API_BASE_URL);
            } else {
                console.log("网络模块初始化");
                console.log("API基础URL: " + API_BASE_URL);
            }
        },

        /**
         * 获取API基础URL
         */
        getApiBaseUrl: function () {
            return API_BASE_URL;
        },

        /**
         * 发送GET请求
         * @param {string} url - 请求URL
         * @param {Object} params - 请求参数
         * @param {function} callback - 回调函数
         * @param {string} token - 可选的认证令牌
         */
        get: function (url, params, callback, token) {
            this.request("GET", url, params, callback, token);
        },

        /**
         * 发送POST请求
         * @param {string} url - 请求URL
         * @param {Object} data - 请求数据
         * @param {function} callback - 回调函数
         * @param {string} token - 可选的认证令牌
         */
        post: function (url, data, callback, token) {
            this.request("POST", url, data, callback, token);
        },

        /**
         * 发送PUT请求
         * @param {string} url - 请求URL
         * @param {Object} data - 请求数据
         * @param {function} callback - 回调函数
         * @param {string} token - 可选的认证令牌
         */
        put: function (url, data, callback, token) {
            this.request("PUT", url, data, callback, token);
        },

        /**
         * 发送DELETE请求
         * @param {string} url - 请求URL
         * @param {Object} params - 请求参数
         * @param {function} callback - 回调函数
         * @param {string} token - 可选的认证令牌
         */
        delete: function (url, params, callback, token) {
            this.request("DELETE", url, params, callback, token);
        },

        /**
         * 发送网络请求
         * @param {string} method - 请求方法
         * @param {string} url - 请求URL
         * @param {Object} data - 请求数据
         * @param {function} callback - 回调函数
         * @param {string} token - 可选的认证令牌
         */
        request: function (method, url, data, callback, token) {
            this.requestWithRetry(method, url, data, callback, token, 0);
        },

        /**
         * 带重试的网络请求
         * @param {string} method - 请求方法
         * @param {string} url - 请求URL
         * @param {Object} data - 请求数据
         * @param {function} callback - 回调函数
         * @param {string} token - 可选的认证令牌
         * @param {number} retryCount - 当前重试次数
         */
        requestWithRetry: function (method, url, data, callback, token, retryCount) {
            try {
                // 构建完整URL
                var fullUrl = url.startsWith("http") ? url : API_BASE_URL + url;

                if (typeof LogModule !== 'undefined') {
                    LogModule.log("发送" + method + "请求: " + fullUrl);
                } else {
                    console.log("发送" + method + "请求: " + fullUrl);
                }

                // 构建请求参数
                var options = {
                    method: method,
                    headers: {
                        "Content-Type": "application/json"
                    },
                    timeout: REQUEST_TIMEOUT
                };

                // 添加认证头
                if (token) {
                    options.headers["Authorization"] = "Bearer " + token;
                    console.log("外部网络模块添加认证头: Bearer " + token.substring(0, 10) + "...");
                    if (typeof global !== 'undefined' && global.LogModule) {
                        global.LogModule.log("外部网络模块添加认证头: Bearer " + token.substring(0, 10) + "...");
                    }
                } else {
                    console.warn("外部网络模块警告：API请求未携带Token，可能导致认证失败");
                    if (typeof global !== 'undefined' && global.LogModule) {
                        global.LogModule.log("外部网络模块警告：API请求未携带Token", "WARN");
                    }
                }

                // 处理请求体
                if (data) {
                    if (method === "GET" || method === "DELETE") {
                        // GET和DELETE请求将参数添加到URL - 使用AutoJS兼容的方式
                        var paramPairs = [];
                        for (var key in data) {
                            if (data.hasOwnProperty(key)) {
                                paramPairs.push(encodeURIComponent(key) + '=' + encodeURIComponent(data[key]));
                            }
                        }
                        if (paramPairs.length > 0) {
                            var paramString = paramPairs.join('&');
                            fullUrl += (fullUrl.includes('?') ? '&' : '?') + paramString;
                        }

                        if (typeof LogModule !== 'undefined') {
                            LogModule.log("GET请求URL参数: " + paramString);
                        }
                    } else {
                        // POST和PUT请求将数据放在请求体中
                        options.body = JSON.stringify(data);

                        if (typeof LogModule !== 'undefined') {
                            LogModule.log(method + "请求数据: " + options.body);
                        }
                    }
                }

                if (typeof LogModule !== 'undefined') {
                    LogModule.log("请求选项: " + JSON.stringify(options));
                }

                // 在后台线程中发送请求，避免NetworkOnMainThreadException
                var that = this;
                threads.start(function() {
                    try {
                        var response = http.request(fullUrl, options);

                        if (response) {
                            if (typeof global !== 'undefined' && global.LogModule) {
                                global.LogModule.log("请求成功: " + fullUrl);
                            } else if (typeof LogModule !== 'undefined') {
                                LogModule.log("请求成功: " + fullUrl);
                            }

                            // 解析响应
                            var responseData = null;
                            try {
                                if (response.body && response.body.string) {
                                    var responseText = response.body.string();
                                    if (responseText) {
                                        responseData = JSON.parse(responseText);
                                    }
                                }
                            } catch (e) {
                                if (typeof global !== 'undefined' && global.LogModule) {
                                    global.LogModule.log("解析响应数据失败: " + e.message, "ERROR");
                                } else if (typeof LogModule !== 'undefined') {
                                    LogModule.log("解析响应数据失败: " + e.message, "ERROR");
                                }
                                responseData = { error: "响应数据解析失败" };
                            }

                            // 在UI线程中调用回调函数
                            if (callback) {
                                ui.run(function () {
                                    try {
                                        callback(null, responseData);
                                    } catch (e) {
                                        if (typeof global !== 'undefined' && global.LogModule) {
                                            global.LogModule.log("回调函数执行失败: " + e.message, "ERROR");
                                        } else if (typeof LogModule !== 'undefined') {
                                            LogModule.log("回调函数执行失败: " + e.message, "ERROR");
                                        }
                                    }
                                });
                            }
                        } else {
                            throw new Error("网络请求失败，无响应");
                        }

                    } catch (e) {
                        if (typeof global !== 'undefined' && global.LogModule) {
                            global.LogModule.log("网络请求异常: " + e.message, "ERROR");
                        } else if (typeof LogModule !== 'undefined') {
                            LogModule.log("网络请求异常: " + e.message, "ERROR");
                        } else {
                            console.error("网络请求异常: " + e.message);
                        }

                        // 重试逻辑
                        if (retryCount < MAX_RETRIES) {
                            if (typeof global !== 'undefined' && global.LogModule) {
                                global.LogModule.log("网络请求失败，进行第" + (retryCount + 1) + "次重试", "WARN");
                            } else if (typeof LogModule !== 'undefined') {
                                LogModule.log("网络请求失败，进行第" + (retryCount + 1) + "次重试", "WARN");
                            }

                            setTimeout(function() {
                                that.requestWithRetry(method, url, data, callback, token, retryCount + 1);
                            }, 1000 * (retryCount + 1)); // 递增延迟
                        } else {
                            // 重试次数用完，调用回调函数返回错误
                            if (callback) {
                                ui.run(function () {
                                    try {
                                        callback(e, null);
                                    } catch (callbackError) {
                                        if (typeof global !== 'undefined' && global.LogModule) {
                                            global.LogModule.log("错误回调函数执行失败: " + callbackError.message, "ERROR");
                                        } else if (typeof LogModule !== 'undefined') {
                                            LogModule.log("错误回调函数执行失败: " + callbackError.message, "ERROR");
                                        }
                                    }
                                });
                            }
                        }
                    }
                });
            } catch (e) {
                if (typeof global !== 'undefined' && global.LogModule) {
                    global.LogModule.log("执行网络请求失败: " + e.message, "ERROR");
                } else if (typeof LogModule !== 'undefined') {
                    LogModule.log("执行网络请求失败: " + e.message, "ERROR");
                } else {
                    console.error("执行网络请求失败: " + e.message);
                }

                // 重试逻辑
                if (retryCount < MAX_RETRIES) {
                    if (typeof global !== 'undefined' && global.LogModule) {
                        global.LogModule.log("网络请求失败，进行第" + (retryCount + 1) + "次重试", "WARN");
                    } else if (typeof LogModule !== 'undefined') {
                        LogModule.log("网络请求失败，进行第" + (retryCount + 1) + "次重试", "WARN");
                    }

                    var that = this;
                    setTimeout(function() {
                        that.requestWithRetry(method, url, data, callback, token, retryCount + 1);
                    }, 1000 * (retryCount + 1)); // 递增延迟
                } else {
                    // 重试次数用完，调用回调函数返回错误
                    if (callback) {
                        ui.run(function () {
                            try {
                                callback(e, null);
                            } catch (callbackError) {
                                if (typeof global !== 'undefined' && global.LogModule) {
                                    global.LogModule.log("错误回调函数执行失败: " + callbackError.message, "ERROR");
                                } else if (typeof LogModule !== 'undefined') {
                                    LogModule.log("错误回调函数执行失败: " + callbackError.message, "ERROR");
                                }
                            }
                        });
                    }
                }
            }
        },

        /**
         * 检查网络连接
         * @returns {boolean} 是否有网络连接
         */
        isNetworkAvailable: function () {
            try {
                // 尝试发送一个简单的请求来检查网络
                var response = http.get("https://www.baidu.com", { timeout: 5000 });
                return response && response.statusCode === 200;
            } catch (e) {
                return false;
            }
        },

        /**
         * 下载文件
         * @param {string} url - 文件URL
         * @param {string} savePath - 保存路径
         * @param {function} callback - 回调函数
         * @param {function} progressCallback - 进度回调函数
         */
        downloadFile: function (url, savePath, callback, progressCallback) {
            try {
                if (typeof LogModule !== 'undefined') {
                    LogModule.log("开始下载文件: " + url);
                    LogModule.log("保存路径: " + savePath);
                }

                var fullUrl = url.startsWith("http") ? url : API_BASE_URL + url;

                var thread = threads.start(function () {
                    try {
                        // 发送HTTP请求
                        var response = http.get(fullUrl);

                        if (!response || response.statusCode < 200 || response.statusCode >= 300) {
                            var error = new Error("下载失败，状态码: " + (response ? response.statusCode : "无响应"));
                            if (callback) callback(error, null);
                            return;
                        }

                        // 获取文件内容
                        var fileBytes = response.body.bytes();
                        if (!fileBytes) {
                            var error = new Error("获取文件内容失败");
                            if (callback) callback(error, null);
                            return;
                        }

                        // 确保目录存在
                        var tempFile = savePath + ".tmp";
                        files.createWithDirs(tempFile);
                        if (files.exists(tempFile)) {
                            files.remove(tempFile);
                        }

                        // 保存文件
                        files.writeBytes(savePath, fileBytes);
                        
                        // 验证文件保存成功
                        if (files.exists(savePath)) {
                            if (typeof LogModule !== 'undefined') {
                                LogModule.log("文件下载成功: " + savePath);
                                LogModule.log("文件大小: " + fileBytes.length + " 字节");
                            }
                            if (callback) callback(null, savePath);
                        } else {
                            var error = new Error("文件保存失败");
                            if (callback) callback(error, null);
                        }

                    } catch (e) {
                        if (typeof LogModule !== 'undefined') {
                            LogModule.log("下载异常: " + e.message, "ERROR");
                        }
                        if (callback) callback(e, null);
                    }
                });

            } catch (e) {
                if (typeof LogModule !== 'undefined') {
                    LogModule.log("启动下载失败: " + e.message, "ERROR");
                }
                if (callback) callback(e, null);
            }
        },

        /**
         * 用户登录 - 完全按照原始main.js还原
         * @param {string} phone - 手机号
         * @param {string} password - 密码
         * @param {function} callback - 回调函数 callback(error, userData)
         */
        login: function(phone, password, callback) {
            try {
                // 获取设备ID
                var deviceId = device.getAndroidId();
                if (typeof global !== 'undefined' && global.LogModule) {
                    global.LogModule.log("设备ID: " + deviceId);
                    global.LogModule.log("尝试登录: " + phone);
                } else {
                    console.log("设备ID: " + deviceId);
                    console.log("尝试登录: " + phone);
                }

                // 构建登录数据 - 按照原版格式
                var loginData = {
                    phone: phone,
                    password: password,
                    deviceId: deviceId
                };

                // 发送登录请求 - 使用原版API路径
                this.post("/user/login", loginData, (error, result) => {
                    if (error) {
                        if (typeof global !== 'undefined' && global.LogModule) {
                            global.LogModule.log("登录请求失败: " + error.message, "ERROR");
                        } else {
                            console.error("登录请求失败: " + error.message);
                        }

                        // 网络失败时的演示模式兜底
                        if (phone === "13800138000" && password === "123456") {
                            var userData = {
                                id: "demo_user_001",
                                phone: phone,
                                nickname: "演示用户",
                                token: "demo_token_" + Date.now(),
                                loginTime: new Date().toISOString()
                            };
                            if (callback) callback(null, userData);
                        } else {
                            if (callback) callback(error, null);
                        }
                        return;
                    }

                    // 检查响应状态 - 按照原版逻辑
                    if (result && result.code === 200) {
                        if (typeof global !== 'undefined' && global.LogModule) {
                            global.LogModule.log("登录成功: " + phone);
                        } else {
                            console.log("登录成功: " + phone);
                        }

                        // 保存用户信息和token - 按照原版逻辑
                        if (result.data && typeof global !== 'undefined' && global.ConfigModule) {
                            // 保存用户信息到ConfigModule
                            global.ConfigModule.set("userPhone", phone);
                            global.ConfigModule.set("userId", result.data.id);
                            global.ConfigModule.set("userToken", result.data.token);
                            global.ConfigModule.set("referrerId", result.data.referrerId);
                            global.ConfigModule.set("invitationCode", result.data.invitationCode);
                            console.log("用户信息已保存到ConfigModule");
                        }

                        if (callback) callback(null, result.data);
                    } else {
                        var errorMsg = result && result.message ? result.message : "登录失败";
                        if (typeof global !== 'undefined' && global.LogModule) {
                            global.LogModule.log("登录失败: " + errorMsg, "ERROR");
                        } else {
                            console.error("登录失败: " + errorMsg);
                        }

                        // 登录失败时的演示模式兜底
                        if (phone === "13800138000" && password === "123456") {
                            var userData = {
                                id: "demo_user_001",
                                phone: phone,
                                nickname: "演示用户",
                                token: "demo_token_" + Date.now()
                            };
                            if (callback) callback(null, userData);
                        } else {
                            if (callback) callback(new Error(errorMsg), null);
                        }
                    }
                });
            } catch (e) {
                if (typeof global !== 'undefined' && global.LogModule) {
                    global.LogModule.log("执行登录操作失败: " + e.message, "ERROR");
                } else {
                    console.error("执行登录操作失败: " + e.message);
                }

                // 异常情况下的演示模式兜底
                if (phone === "13800138000" && password === "123456") {
                    var userData = {
                        id: "demo_user_001",
                        phone: phone,
                        nickname: "演示用户",
                        token: "demo_token_" + Date.now()
                    };
                    if (callback) callback(null, userData);
                } else {
                    if (callback) callback(e, null);
                }
            }
        },

        /**
         * 发送注册验证码 - 按照原版还原
         * @param {string} phone - 手机号
         * @param {function} callback - 回调函数
         */
        sendRegisterCode: function(phone, callback) {
            this.post("/user/register/code", { phone: phone }, callback);
        },

        /**
         * 检查手机号是否已注册 - 按照原版还原
         * @param {string} phone - 手机号
         * @param {function} callback - 回调函数
         */
        checkPhoneRegistered: function(phone, callback) {
            this.post("/user/check/phone", { phone: phone }, callback);
        },

        /**
         * 检查推荐人ID是否存在 - 按照原版还原
         * @param {string} referrerId - 推荐人ID
         * @param {function} callback - 回调函数
         */
        checkReferrerId: function(referrerId, callback) {
            this.post("/user/check/referrer", { referrerId: referrerId }, callback);
        },

        /**
         * 用户注册 - 完全按照原始main.js还原
         * @param {string} phone - 手机号
         * @param {string} code - 验证码
         * @param {string} password - 密码
         * @param {string} referrerId - 推荐人ID
         * @param {function} callback - 回调函数
         */
        register: function(phone, code, password, referrerId, callback) {
            var deviceId = device.getAndroidId();
            this.post("/user/register", {
                phone: phone,
                code: code,
                password: password,
                referrerId: referrerId,
                deviceId: deviceId
            }, callback);
        },

        /**
         * 发送重置密码验证码 - 按照原版还原
         * @param {string} phone - 手机号
         * @param {function} callback - 回调函数
         */
        sendResetPasswordCode: function(phone, callback) {
            this.post("/user/reset/password/code", { phone: phone }, callback);
        },

        /**
         * 重置密码 - 完全按照原始main.js还原
         * @param {string} phone - 手机号
         * @param {string} code - 验证码
         * @param {string} newPassword - 新密码
         * @param {function} callback - 回调函数
         */
        resetPassword: function(phone, code, newPassword, callback) {
            this.post("/user/reset/password", {
                phone: phone,
                code: code,
                newPassword: newPassword
            }, callback);
        },

        /**
         * 获取最新公告 - 按照原版还原
         * @param {function} callback - 回调函数
         */
        getLatestAnnouncement: function(callback) {
            this.get("/announcement/latest", null, callback);
        },

        /**
         * 获取公告配置信息（包含QQ群信息）
         * @param {function} callback - 回调函数
         */
        getAnnouncementConfig: function(callback) {
            try {
                console.log("外部网络模块：获取公告配置信息");
                this.get("/announcement/enabled", null, function(error, result) {
                    if (error) {
                        console.log("获取公告配置失败，返回默认信息");
                        // 返回默认配置信息
                        setTimeout(function() {
                            callback(null, {
                                code: 200,
                                data: {
                                    enabled: true,
                                    qqGroup: "123456789",
                                    content: "欢迎使用脚本助手！"
                                },
                                message: "获取成功"
                            });
                        }, 300);
                    } else {
                        console.log("获取公告配置成功: " + JSON.stringify(result));
                        callback(null, result);
                    }
                });
            } catch (e) {
                console.error("获取公告配置异常: " + e.message);
                if (callback) callback(e, null);
            }
        },

        /**
         * 查询用户卡密信息接口 - 按照原版还原
         * @param {string} phone - 手机号
         * @param {function} callback - 回调函数
         */
        queryCardKeyInfo: function(phone, callback) {
            console.log("外部网络模块：开始查询卡密信息，手机号: " + phone);

            // 获取用户token - 与基础网络模块保持一致的获取逻辑
            var token = this.getUserToken();
            console.log("外部网络模块：查询卡密信息，使用Token: " + (token ? token.substring(0, 10) + "..." : "null"));

            if (!token) {
                console.error("外部网络模块：Token为空，无法进行卡密查询API调用");
                if (callback) callback(new Error("Token为空"), null);
                return;
            }

            this.post("/user/query/cardkey", { phone: phone }, function(error, result) {
                console.log("外部网络模块：卡密查询API响应");
                if (error) {
                    console.log("外部网络模块：卡密查询API调用失败: " + error.message);
                } else {
                    console.log("外部网络模块：卡密查询API调用成功，响应: " + JSON.stringify(result));
                    if (result && result.message && result.message.includes("Token")) {
                        console.error("外部网络模块：后端返回Token相关错误: " + result.message);
                    }
                }
                if (callback) callback(error, result);
            }, token);
        },

        /**
         * 获取用户Token - 与基础网络模块保持一致
         */
        getUserToken: function() {
            console.log("外部网络模块：开始获取用户Token...");

            // 优先从StorageModule获取Token（登录时保存的位置）
            if (typeof global !== 'undefined' && global.StorageModule) {
                console.log("外部网络模块：尝试从StorageModule获取Token...");
                var token = global.StorageModule.get("userToken");
                if (token) {
                    console.log("外部网络模块：从StorageModule获取到Token: " + token.substring(0, 10) + "...");
                    return token;
                } else {
                    console.log("外部网络模块：StorageModule中没有找到Token");
                }
            } else {
                console.log("外部网络模块：StorageModule不可用");
            }

            // 降级到ConfigModule
            if (typeof global !== 'undefined' && global.ConfigModule) {
                console.log("外部网络模块：尝试从ConfigModule获取Token...");
                var token = global.ConfigModule.get("userToken");
                if (token) {
                    console.log("外部网络模块：从ConfigModule获取到Token: " + token.substring(0, 10) + "...");
                    return token;
                } else {
                    console.log("外部网络模块：ConfigModule中没有找到Token");
                }
            } else {
                console.log("外部网络模块：ConfigModule不可用");
            }

            // 最后尝试从currentUser全局变量获取
            if (typeof currentUser !== 'undefined' && currentUser && currentUser.token) {
                console.log("外部网络模块：从currentUser获取到Token: " + currentUser.token.substring(0, 10) + "...");
                return currentUser.token;
            } else {
                console.log("外部网络模块：currentUser中没有Token");
            }

            console.warn("外部网络模块：未能获取到用户Token");
            return null;
        },

        /**
         * 获取可提现金额
         */
        getAvailableWithdrawalAmount: function(callback) {
            try {
                console.log("外部网络模块：获取可提现金额");

                // 获取用户token和用户ID
                var token = this.getUserToken();
                if (!token) {
                    console.error("未找到用户token，无法获取可提现金额");
                    if (callback) callback(new Error("未找到用户token"), null);
                    return;
                }

                // 获取用户ID
                var userId = null;
                if (typeof global !== 'undefined' && global.StorageModule) {
                    userId = global.StorageModule.get("userId");
                } else if (typeof global !== 'undefined' && global.ConfigModule) {
                    userId = global.ConfigModule.get("userId");
                } else if (typeof currentUser !== 'undefined' && currentUser && currentUser.id) {
                    userId = currentUser.id;
                }

                if (!userId) {
                    console.error("未找到用户ID，无法获取可提现金额");
                    if (callback) callback(new Error("未找到用户ID"), null);
                    return;
                }

                var fullUrl = API_BASE_URL + "/withdrawal/available-amount";

                // 对于GET请求，将参数添加到URL中
                var paramString = "userId=" + encodeURIComponent(userId);
                fullUrl += "?" + paramString;

                var options = {
                    method: "GET",
                    headers: {
                        "Content-Type": "application/json",
                        "Authorization": "Bearer " + token
                    },
                    timeout: 10000
                };

                console.log("外部网络模块：发送获取余额请求到: " + fullUrl);

                // 在后台线程中发送请求，避免NetworkOnMainThreadException
                threads.start(function() {
                    try {
                        var response = http.request(fullUrl, options);

                        if (response && response.statusCode === 200) {
                            // 解析响应
                            var responseData = null;
                            try {
                                if (response.body && response.body.string) {
                                    var responseText = response.body.string();
                                    console.log("外部网络模块：获取余额响应: " + responseText);
                                    responseData = JSON.parse(responseText);
                                }
                            } catch (parseError) {
                                console.error("解析获取余额响应失败: " + parseError.message);
                                if (callback) callback(parseError, null);
                                return;
                            }

                            if (callback) callback(null, responseData);
                        } else {
                            var error = new Error("获取余额失败，状态码: " + (response ? response.statusCode : "无响应"));
                            console.error("外部网络模块：" + error.message);
                            if (callback) callback(error, null);
                        }
                    } catch (threadError) {
                        console.error("获取余额请求执行失败: " + threadError.message);
                        if (callback) callback(threadError, null);
                    }
                });
            } catch (e) {
                console.error("获取可提现金额失败: " + e.message);
                if (callback) callback(e, null);
            }
        },

        /**
         * 申请提现
         */
        applyWithdrawal: function(amount, alipayAccount, alipayName, callback) {
            try {
                console.log("外部网络模块：申请提现 - 金额: " + amount + ", 支付宝账号: " + alipayAccount);

                // 获取用户token和用户ID
                var token = this.getUserToken();
                if (!token) {
                    console.error("未找到用户token，无法申请提现");
                    if (callback) callback(new Error("未找到用户token"), null);
                    return;
                }

                // 获取用户ID
                var userId = null;
                if (typeof global !== 'undefined' && global.StorageModule) {
                    userId = global.StorageModule.get("userId");
                } else if (typeof global !== 'undefined' && global.ConfigModule) {
                    userId = global.ConfigModule.get("userId");
                } else if (typeof currentUser !== 'undefined' && currentUser && currentUser.id) {
                    userId = currentUser.id;
                }

                if (!userId) {
                    console.error("未找到用户ID，无法申请提现");
                    if (callback) callback(new Error("未找到用户ID"), null);
                    return;
                }

                var fullUrl = API_BASE_URL + "/withdrawal/apply?userId=" + userId;
                var requestData = {
                    amount: amount,
                    alipayAccount: alipayAccount,
                    alipayName: alipayName
                };

                var options = {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "Authorization": "Bearer " + token
                    },
                    body: JSON.stringify(requestData),
                    timeout: 15000
                };

                console.log("外部网络模块：发送提现申请到: " + fullUrl);
                console.log("外部网络模块：提现申请数据: " + JSON.stringify(requestData));

                // 在后台线程中发送请求，避免NetworkOnMainThreadException
                threads.start(function() {
                    try {
                        var response = http.request(fullUrl, options);

                        if (response && response.statusCode === 200) {
                            // 解析响应
                            var responseData = null;
                            try {
                                if (response.body && response.body.string) {
                                    var responseText = response.body.string();
                                    console.log("外部网络模块：提现申请响应: " + responseText);
                                    responseData = JSON.parse(responseText);
                                }
                            } catch (parseError) {
                                console.error("解析提现申请响应失败: " + parseError.message);
                                if (callback) callback(parseError, null);
                                return;
                            }

                            if (callback) callback(null, responseData);
                        } else {
                            var error = new Error("提现申请失败，状态码: " + (response ? response.statusCode : "无响应"));
                            console.error("外部网络模块：" + error.message);
                            if (callback) callback(error, null);
                        }
                    } catch (threadError) {
                        console.error("提现申请请求执行失败: " + threadError.message);
                        if (callback) callback(threadError, null);
                    }
                });
            } catch (e) {
                console.error("申请提现失败: " + e.message);
                if (callback) callback(e, null);
            }
        },

        /**
         * 获取提现记录
         */
        getWithdrawalRecords: function(callback) {
            try {
                console.log("外部网络模块：获取提现记录");

                // 获取用户token和用户ID
                var token = this.getUserToken();
                if (!token) {
                    console.error("未找到用户token，无法获取提现记录");
                    if (callback) callback(new Error("未找到用户token"), null);
                    return;
                }

                // 获取用户ID
                var userId = null;
                if (typeof global !== 'undefined' && global.StorageModule) {
                    userId = global.StorageModule.get("userId");
                } else if (typeof global !== 'undefined' && global.ConfigModule) {
                    userId = global.ConfigModule.get("userId");
                } else if (typeof currentUser !== 'undefined' && currentUser && currentUser.id) {
                    userId = currentUser.id;
                }

                if (!userId) {
                    console.error("未找到用户ID，无法获取提现记录");
                    if (callback) callback(new Error("未找到用户ID"), null);
                    return;
                }

                var fullUrl = API_BASE_URL + "/withdrawal/user/requests";

                var options = {
                    method: "GET",
                    headers: {
                        "Content-Type": "application/json",
                        "Authorization": "Bearer " + token
                    },
                    timeout: 10000
                };

                // 对于GET请求，将参数添加到URL中
                var paramString = "userId=" + encodeURIComponent(userId);
                fullUrl += "?" + paramString;

                console.log("外部网络模块：发送获取提现记录请求到: " + fullUrl);

                // 在后台线程中发送请求，避免NetworkOnMainThreadException
                threads.start(function() {
                    try {
                        var response = http.request(fullUrl, options);

                        if (response && response.statusCode === 200) {
                            // 解析响应
                            var responseData = null;
                            try {
                                if (response.body && response.body.string) {
                                    var responseText = response.body.string();
                                    console.log("外部网络模块：提现记录响应: " + responseText);
                                    responseData = JSON.parse(responseText);
                                }
                            } catch (parseError) {
                                console.error("解析提现记录响应失败: " + parseError.message);
                                if (callback) callback(parseError, null);
                                return;
                            }

                            if (callback) callback(null, responseData);
                        } else {
                            var error = new Error("获取提现记录失败，状态码: " + (response ? response.statusCode : "无响应"));
                            console.error("外部网络模块：" + error.message);
                            if (callback) callback(error, null);
                        }
                    } catch (threadError) {
                        console.error("获取提现记录请求执行失败: " + threadError.message);
                        if (callback) callback(threadError, null);
                    }
                });
            } catch (e) {
                console.error("获取提现记录失败: " + e.message);
                if (callback) callback(e, null);
            }
        },

        /**
         * 获取提现说明
         */
        getWithdrawalInstruction: function(callback) {
            try {
                console.log("外部网络模块：获取提现说明");

                // 获取用户token
                var token = this.getUserToken();
                if (!token) {
                    console.error("未找到用户token，无法获取提现说明");
                    if (callback) callback(new Error("未找到用户token"), null);
                    return;
                }

                var fullUrl = API_BASE_URL + "/withdrawal/config/instruction";
                var options = {
                    method: "GET",
                    headers: {
                        "Content-Type": "application/json",
                        "Authorization": "Bearer " + token
                    },
                    timeout: 10000
                };

                console.log("外部网络模块：发送获取提现说明请求到: " + fullUrl);

                // 在后台线程中发送请求，避免NetworkOnMainThreadException
                threads.start(function() {
                    try {
                        var response = http.request(fullUrl, options);

                        if (response && response.statusCode === 200) {
                            // 解析响应
                            var responseData = null;
                            try {
                                if (response.body && response.body.string) {
                                    var responseText = response.body.string();
                                    console.log("外部网络模块：提现说明响应: " + responseText);
                                    responseData = JSON.parse(responseText);
                                }
                            } catch (parseError) {
                                console.error("解析提现说明响应失败: " + parseError.message);
                                if (callback) callback(parseError, null);
                                return;
                            }

                            if (callback) callback(null, responseData);
                        } else {
                            var error = new Error("获取提现说明失败，状态码: " + (response ? response.statusCode : "无响应"));
                            console.error("外部网络模块：" + error.message);
                            if (callback) callback(error, null);
                        }
                    } catch (threadError) {
                        console.error("获取提现说明请求执行失败: " + threadError.message);
                        if (callback) callback(threadError, null);
                    }
                });
            } catch (e) {
                console.error("获取提现说明失败: " + e.message);
                if (callback) callback(e, null);
            }
        },

        /**
         * 获取提现金额选项
         */
        getWithdrawalAmountOptions: function(callback) {
            try {
                console.log("外部网络模块：获取提现金额选项");

                // 获取用户token
                var token = this.getUserToken();
                if (!token) {
                    console.error("未找到用户token，无法获取提现金额选项");
                    if (callback) callback(new Error("未找到用户token"), null);
                    return;
                }

                var fullUrl = API_BASE_URL + "/withdrawal/config/amount-options";
                var options = {
                    method: "GET",
                    headers: {
                        "Content-Type": "application/json",
                        "Authorization": "Bearer " + token
                    },
                    timeout: 10000
                };

                console.log("外部网络模块：发送获取提现金额选项请求到: " + fullUrl);

                // 在后台线程中发送请求，避免NetworkOnMainThreadException
                threads.start(function() {
                    try {
                        var response = http.request(fullUrl, options);

                        if (response && response.statusCode === 200) {
                            // 解析响应
                            var responseData = null;
                            try {
                                if (response.body && response.body.string) {
                                    var responseText = response.body.string();
                                    console.log("外部网络模块：提现金额选项响应: " + responseText);
                                    responseData = JSON.parse(responseText);
                                }
                            } catch (parseError) {
                                console.error("解析提现金额选项响应失败: " + parseError.message);
                                if (callback) callback(parseError, null);
                                return;
                            }

                            if (callback) callback(null, responseData);
                        } else {
                            var error = new Error("获取提现金额选项失败，状态码: " + (response ? response.statusCode : "无响应"));
                            console.error("外部网络模块：" + error.message);
                            if (callback) callback(error, null);
                        }
                    } catch (threadError) {
                        console.error("获取提现金额选项请求执行失败: " + threadError.message);
                        if (callback) callback(threadError, null);
                    }
                });
            } catch (e) {
                console.error("获取提现金额选项失败: " + e.message);
                if (callback) callback(e, null);
            }
        },

        /**
         * 获取推广排名数据
         */
        getPromotionRank: function(callback) {
            try {
                console.log("外部网络模块：获取推广排名数据");

                // 获取用户token和用户ID
                var token = this.getUserToken();
                if (!token) {
                    console.error("未找到用户token，无法获取推广排名数据");
                    if (callback) callback(new Error("未找到用户token"), null);
                    return;
                }

                // 获取用户ID
                var userId = null;
                if (typeof global !== 'undefined' && global.StorageModule) {
                    userId = global.StorageModule.get("userId");
                } else if (typeof global !== 'undefined' && global.ConfigModule) {
                    userId = global.ConfigModule.get("userId");
                } else if (typeof currentUser !== 'undefined' && currentUser && currentUser.id) {
                    userId = currentUser.id;
                }

                if (!userId) {
                    console.error("未找到用户ID，无法获取推广排名数据");
                    if (callback) callback(new Error("未找到用户ID"), null);
                    return;
                }

                // 构建带参数的URL
                var fullUrl = API_BASE_URL + "/promotion/rank";
                var paramString = "userId=" + encodeURIComponent(userId) + "&limit=30";
                fullUrl += "?" + paramString;

                var options = {
                    method: "GET",
                    headers: {
                        "Content-Type": "application/json",
                        "Authorization": "Bearer " + token
                    },
                    timeout: 10000
                };

                console.log("外部网络模块：发送获取推广排名请求到: " + fullUrl);

                // 在后台线程中发送请求，避免NetworkOnMainThreadException
                threads.start(function() {
                    try {
                        var response = http.request(fullUrl, options);

                        if (response && response.statusCode === 200) {
                            // 解析响应
                            var responseData = null;
                            try {
                                if (response.body && response.body.string) {
                                    var responseText = response.body.string();
                                    console.log("外部网络模块：推广排名响应: " + responseText);
                                    responseData = JSON.parse(responseText);
                                }
                            } catch (parseError) {
                                console.error("解析推广排名响应失败: " + parseError.message);
                                if (callback) callback(parseError, null);
                                return;
                            }

                            if (callback) callback(null, responseData);
                        } else {
                            var error = new Error("获取推广排名失败，状态码: " + (response ? response.statusCode : "无响应"));
                            console.error("外部网络模块：" + error.message);
                            if (callback) callback(error, null);
                        }
                    } catch (threadError) {
                        console.error("获取推广排名请求执行失败: " + threadError.message);
                        if (callback) callback(threadError, null);
                    }
                });
            } catch (e) {
                console.error("获取推广排名失败: " + e.message);
                if (callback) callback(e, null);
            }
        }
    };
})();

// 导出模块（AutoX.js兼容方式）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NetworkModule;
} else {
    // AutoX.js环境 - 将模块添加到全局作用域
    global.NetworkModule = NetworkModule;
    if (typeof window !== 'undefined') {
        window.NetworkModule = NetworkModule;
    }
}
