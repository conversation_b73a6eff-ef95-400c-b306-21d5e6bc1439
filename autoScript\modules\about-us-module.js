/**
 * 关于我们模块
 * 负责处理关于我们相关功能
 */

const AboutUsModule = (function () {
    return {
        /**
         * 创建关于我们界面（对话框形式） - 完全按照原始main.js实现
         * 从后端获取数据并展示
         */
        createAboutUsUI: function () {
            try {
                if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                    global.LogModule.log("开始创建关于我们对话框", "INFO");
                }
                
                // 尝试从后端获取数据（不阻塞UI显示）
                try {
                    // 获取用户Token
                    let userToken = null;
                    if (typeof global !== 'undefined' && global.ConfigModule && global.ConfigModule.get) {
                        userToken = global.ConfigModule.get("userToken");
                    }
                    
                    if (!userToken) {
                        console.error("获取用户Token失败，可能未登录");
                        this.showDefaultAboutUsDialog();
                        return;
                    }

                    // 从后端获取关于我们信息
                    if (typeof global !== 'undefined' && global.NetworkModule && global.NetworkModule.get) {
                        global.NetworkModule.get("/about-us", null, function (error, result) {
                            if (error) {
                                console.error("获取关于我们信息失败: " + error.message);
                                if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                                    global.LogModule.log("获取关于我们信息失败: " + error.message, "ERROR");
                                }
                                AboutUsModule.showDefaultAboutUsDialog();
                                return;
                            }

                            if (result && result.code === 200 && result.data && result.data.length > 0) {
                                // 获取第一条数据
                                let aboutInfo = result.data[0];

                                // 更新对话框内容
                                AboutUsModule.showAboutUsDialog(aboutInfo.title || "关于我们", aboutInfo.content || "暂无内容");
                            } else {
                                AboutUsModule.showDefaultAboutUsDialog();
                            }
                        }, userToken); // 传递用户Token用于认证
                    } else {
                        console.error("NetworkModule 不可用");
                        this.showDefaultAboutUsDialog();
                    }
                } catch (networkError) {
                    console.error("获取关于我们网络请求失败: " + networkError.message);
                    if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                        global.LogModule.log("获取关于我们网络请求失败: " + networkError.message, "ERROR");
                    }
                    this.showDefaultAboutUsDialog();
                }

            } catch (e) {
                console.error("创建关于我们对话框失败: " + e.message);
                console.error(e.stack);
                if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                    global.LogModule.log("创建关于我们对话框失败: " + e.message, "ERROR");
                }
                this.showDefaultAboutUsDialog();
            }
        },

        /**
         * 显示关于我们对话框
         */
        showAboutUsDialog: function (title, content) {
            try {
                if (typeof ui !== 'undefined' && ui.run && typeof dialogs !== 'undefined') {
                    ui.run(function () {
                        dialogs.build({
                            title: title,
                            content: content,
                            positive: "确定",
                            cancelable: true
                        }).show();
                    });
                } else {
                    console.error("UI或dialogs对象不可用");
                    if (typeof toast !== 'undefined') {
                        toast("关于我们: " + content);
                    }
                }
            } catch (e) {
                console.error("显示关于我们对话框失败: " + e.message);
                if (typeof toast !== 'undefined') {
                    toast("显示关于我们对话框失败");
                }
            }
        },

        /**
         * 显示默认关于我们对话框
         */
        showDefaultAboutUsDialog: function () {
            try {
                var defaultTitle = "关于我们";
                var defaultContent = "脚本助手 v1.0\n\n" +
                    "一款专业的自动化脚本工具，致力于为用户提供便捷、高效的自动化解决方案。\n\n" +
                    "主要功能：\n" +
                    "• 微信阅读自动化\n" +
                    "• 积分管理系统\n" +
                    "• 推广分销功能\n" +
                    "• 多平台脚本支持\n\n" +
                    "感谢您的使用！";

                this.showAboutUsDialog(defaultTitle, defaultContent);
            } catch (e) {
                console.error("显示默认关于我们对话框失败: " + e.message);
                if (typeof toast !== 'undefined') {
                    toast("关于我们功能暂时不可用");
                }
            }
        },

        /**
         * 创建更多项目界面 - 完全按照原始main.js实现
         */
        createMoreProjectsUI: function () {
            try {
                if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                    global.LogModule.log("开始创建更多项目对话框", "INFO");
                }

                // 请求后端接口获取更多项目链接
                if (typeof global !== 'undefined' && global.NetworkModule && global.NetworkModule.get) {
                    global.NetworkModule.get("/system/config/key/MORE_PROJECTS_LINK", null, function (error, result) {
                        if (error) {
                            console.error("获取更多项目链接失败: " + error.message);
                            if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                                global.LogModule.log("获取更多项目链接失败: " + error.message, "ERROR");
                            }
                            AboutUsModule.showDefaultMoreProjectsDialog();
                            return;
                        }

                        try {
                            if (result && result.code === 200 && result.data && result.data.configValue) {
                                let projectLink = result.data.configValue;
                                if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                                    global.LogModule.log("成功获取更多项目链接: " + projectLink, "INFO");
                                }

                                // 使用获取到的链接打开网页
                                if (typeof ui !== 'undefined' && ui.run && typeof app !== 'undefined') {
                                    ui.run(function () {
                                        app.openUrl(projectLink);
                                    });
                                } else {
                                    console.error("UI或app对象不可用");
                                    if (typeof toast !== 'undefined') {
                                        toast("无法打开链接: " + projectLink);
                                    }
                                }
                            } else {
                                throw new Error("获取更多项目链接失败或数据格式错误");
                            }
                        } catch (parseError) {
                            console.error("解析更多项目链接数据失败: " + parseError.message);
                            if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                                global.LogModule.log("解析更多项目链接数据失败: " + parseError.message, "ERROR");
                            }
                            AboutUsModule.showDefaultMoreProjectsDialog();
                        }
                    });
                } else {
                    console.error("NetworkModule 不可用");
                    this.showDefaultMoreProjectsDialog();
                }

                if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                    global.LogModule.log("更多项目对话框创建完成", "INFO");
                }
            } catch (e) {
                console.error("创建更多项目对话框失败: " + e.message);
                console.error(e.stack);
                if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                    global.LogModule.log("创建更多项目对话框失败: " + e.message, "ERROR");
                }
                this.showDefaultMoreProjectsDialog();
            }
        },

        /**
         * 显示默认更多项目对话框
         */
        showDefaultMoreProjectsDialog: function () {
            try {
                // 网络请求失败时显示默认对话框
                if (typeof ui !== 'undefined' && ui.run && typeof dialogs !== 'undefined') {
                    ui.run(function () {
                        dialogs.build({
                            title: "更多项目",
                            content: "我们团队的其他项目:\n\n1. 短视频助手 - 抖音、快手等平台视频批量下载与管理工具\n\n2. 电商助手 - 淘宝、京东等平台自动化运营与管理工具\n\n3. 游戏辅助 - 多款热门游戏的自动化辅助工具\n\n以上项目正在开发中，敬请期待！",
                            positive: "访问官网",
                            negative: "关闭",
                            cancelable: true
                        })
                            .on("positive", function () {
                                if (typeof app !== 'undefined') {
                                    app.openUrl("https://www.example.com");
                                } else {
                                    console.error("app对象不可用");
                                    if (typeof toast !== 'undefined') {
                                        toast("无法打开官网链接");
                                    }
                                }
                            })
                            .show();
                    });
                } else {
                    console.error("UI或dialogs对象不可用");
                    if (typeof toast !== 'undefined') {
                        toast("更多项目功能暂时不可用");
                    }
                }
            } catch (e) {
                console.error("显示默认更多项目对话框失败: " + e.message);
                if (typeof toast !== 'undefined') {
                    toast("更多项目功能暂时不可用");
                }
            }
        },

        /**
         * 跳转发财交流群 - 完全按照原始main.js实现
         */
        jumpWealthExchange: function () {
            try {
                // 请求后端接口获取发财交流群链接
                if (typeof global !== 'undefined' && global.NetworkModule && global.NetworkModule.get) {
                    global.NetworkModule.get("/system/config/key/MONEY_GROUP_LINK", null, function (error, result) {
                        if (error) {
                            if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                                global.LogModule.log("获取MONEY_GROUP_LINK链接失败: " + error.message, "ERROR");
                            }
                            AboutUsModule.showDefaultWealthExchangeDialog();
                            return;
                        }

                        try {
                            if (result && result.code === 200 && result.data && result.data.configValue) {
                                let projectLink = result.data.configValue;
                                if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                                    global.LogModule.log("成功获取MONEY_GROUP_LINK项目链接: " + projectLink, "INFO");
                                }

                                // 使用获取到的链接打开网页
                                if (typeof ui !== 'undefined' && ui.run && typeof app !== 'undefined') {
                                    ui.run(function () {
                                        app.openUrl(projectLink);
                                    });
                                } else {
                                    console.error("UI或app对象不可用");
                                    if (typeof toast !== 'undefined') {
                                        toast("无法打开群链接: " + projectLink);
                                    }
                                }
                            } else {
                                throw new Error("获取MONEY_GROUP_LINK失败或数据格式错误");
                            }
                        } catch (parseError) {
                            console.error("解析MONEY_GROUP_LINK链接数据失败: " + parseError.message);
                            AboutUsModule.showDefaultWealthExchangeDialog();
                        }
                    });
                } else {
                    console.error("NetworkModule 不可用");
                    this.showDefaultWealthExchangeDialog();
                }
            } catch (e) {
                console.error("jumpWealthExchange失败: " + e.message);
                this.showDefaultWealthExchangeDialog();
            }
        },

        /**
         * 显示默认发财交流群对话框
         */
        showDefaultWealthExchangeDialog: function () {
            try {
                if (typeof ui !== 'undefined' && ui.run && typeof dialogs !== 'undefined') {
                    ui.run(function () {
                        dialogs.build({
                            title: "发财交流群",
                            content: "欢迎加入我们的发财交流群！\n\n在这里您可以：\n• 获取最新的赚钱资讯\n• 与其他用户交流经验\n• 获得专业的指导和建议\n• 参与群内活动获得奖励\n\n请联系客服获取群链接。",
                            positive: "联系客服",
                            negative: "关闭",
                            cancelable: true
                        })
                            .on("positive", function () {
                                if (typeof toast !== 'undefined') {
                                    toast("请通过应用内客服功能联系我们");
                                }
                            })
                            .show();
                    });
                } else {
                    console.error("UI或dialogs对象不可用");
                    if (typeof toast !== 'undefined') {
                        toast("发财交流群功能暂时不可用");
                    }
                }
            } catch (e) {
                console.error("显示默认发财交流群对话框失败: " + e.message);
                if (typeof toast !== 'undefined') {
                    toast("发财交流群功能暂时不可用");
                }
            }
        },

        /**
         * 跳转脚本通知群 - 完全按照原始main.js实现
         */
        jumpScriptNotificationGroup: function () {
            try {
                // 请求后端接口获取脚本通知群链接
                if (typeof global !== 'undefined' && global.NetworkModule && global.NetworkModule.get) {
                    global.NetworkModule.get("/system/config/key/SCRIPT_GROUP_LINK", null, function (error, result) {
                        if (error) {
                            if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                                global.LogModule.log("获取SCRIPT_GROUP_LINK链接失败: " + error.message, "ERROR");
                            }
                            AboutUsModule.showDefaultScriptNotificationDialog();
                            return;
                        }

                        try {
                            if (result && result.code === 200 && result.data && result.data.configValue) {
                                let groupLink = result.data.configValue;
                                if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                                    global.LogModule.log("成功获取SCRIPT_GROUP_LINK链接: " + groupLink, "INFO");
                                }

                                // 使用获取到的链接打开网页
                                if (typeof ui !== 'undefined' && ui.run && typeof app !== 'undefined') {
                                    ui.run(function () {
                                        app.openUrl(groupLink);
                                    });
                                } else {
                                    console.error("UI或app对象不可用");
                                    if (typeof toast !== 'undefined') {
                                        toast("无法打开群链接: " + groupLink);
                                    }
                                }
                            } else {
                                throw new Error("获取SCRIPT_GROUP_LINK失败或数据格式错误");
                            }
                        } catch (parseError) {
                            console.error("解析SCRIPT_GROUP_LINK链接数据失败: " + parseError.message);
                            AboutUsModule.showDefaultScriptNotificationDialog();
                        }
                    });
                } else {
                    console.error("NetworkModule 不可用");
                    this.showDefaultScriptNotificationDialog();
                }
            } catch (e) {
                console.error("jumpScriptNotificationGroup失败: " + e.message);
                this.showDefaultScriptNotificationDialog();
            }
        },

        /**
         * 显示默认脚本通知群对话框
         */
        showDefaultScriptNotificationDialog: function () {
            try {
                if (typeof ui !== 'undefined' && ui.run && typeof dialogs !== 'undefined') {
                    ui.run(function () {
                        dialogs.build({
                            title: "脚本通知群",
                            content: "欢迎加入脚本通知群！\n\n在这里您可以：\n• 第一时间获取脚本更新通知\n• 了解新功能发布信息\n• 获得技术支持和帮助\n• 反馈问题和建议\n\n请联系客服获取群链接。",
                            positive: "联系客服",
                            negative: "关闭",
                            cancelable: true
                        })
                            .on("positive", function () {
                                if (typeof toast !== 'undefined') {
                                    toast("请通过应用内客服功能联系我们");
                                }
                            })
                            .show();
                    });
                } else {
                    console.error("UI或dialogs对象不可用");
                    if (typeof toast !== 'undefined') {
                        toast("脚本通知群功能暂时不可用");
                    }
                }
            } catch (e) {
                console.error("显示默认脚本通知群对话框失败: " + e.message);
                if (typeof toast !== 'undefined') {
                    toast("脚本通知群功能暂时不可用");
                }
            }
        }
    };
})();

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AboutUsModule;
} else if (typeof global !== 'undefined') {
    global.AboutUsModule = AboutUsModule;
}
