// 使用"ui"模式
"ui";

/**
 * 脚本助手 - 1:1还原版本
 * 基于原始main.js完全还原的版本
 * 保持技术改进的同时还原原始功能和界面
 */

console.log("=== 脚本助手启动 ===");

// 全局错误处理
try {
    if (typeof process !== 'undefined' && process.on) {
        process.on('uncaughtException', function(error) {
            console.error('未捕获的异常:', error);
            if (typeof global.LogModule !== 'undefined') {
                global.LogModule.log('未捕获的异常: ' + error.message, 'ERROR');
            }
        });
    }
} catch (e) {
    console.warn("无法设置全局错误处理: " + e.message);
}

// 应用状态管理
let appState = {
    currentUser: null,
    currentPage: 'main',
    isLoggedIn: false,
    modules: {},
    userToken: null,
    userPhone: null
};

// 获取脚本根目录
function getScriptRoot() {
    try {
        // 方法1: 通过当前脚本路径获取
        let currentScript = engines.myEngine().getSource().toString();
        if (currentScript && currentScript.indexOf('/') !== -1) {
            let scriptPath = currentScript.substring(0, currentScript.lastIndexOf('/'));
            console.log("检测到脚本路径: " + scriptPath);
            return scriptPath;
        }

        // 方法2: 尝试常见路径
        let commonPaths = [
            "/storage/emulated/0/脚本/autoScript",
            "/sdcard/脚本/autoScript",
            "/storage/emulated/0/autoScript",
            "/sdcard/autoScript"
        ];

        for (let path of commonPaths) {
            if (files.exists(path + "/main.js")) {
                console.log("找到脚本根目录: " + path);
                return path;
            }
        }

        // 方法3: 使用相对路径基准
        let relativePath = files.cwd();
        if (relativePath && files.exists(relativePath + "/main.js")) {
            console.log("使用当前工作目录: " + relativePath);
            return relativePath;
        }

        // 默认路径
        console.warn("无法自动检测脚本路径，使用默认路径");
        return "/storage/emulated/0/脚本/autoScript";

    } catch (e) {
        console.error("获取脚本根目录失败: " + e.message);
        return "/storage/emulated/0/脚本/autoScript";
    }
}

// 自定义模块加载器（增强版）
function loadModule(relativePath) {
    let scriptRoot = getScriptRoot();
    let possiblePaths = [];

    try {
        // 构建可能的路径列表
        if (relativePath.startsWith('./')) {
            // 相对路径
            possiblePaths.push(scriptRoot + "/" + relativePath.substring(2));
        } else if (relativePath.startsWith('/')) {
            // 绝对路径
            possiblePaths.push(relativePath);
        } else {
            // 相对路径（无前缀）
            possiblePaths.push(scriptRoot + "/" + relativePath);
        }

        // 添加备用路径
        possiblePaths.push("/storage/emulated/0/脚本/autoScript/" + relativePath.replace('./', ''));
        possiblePaths.push("/sdcard/脚本/autoScript/" + relativePath.replace('./', ''));

        console.log("尝试加载模块: " + relativePath);

        // 尝试每个可能的路径
        for (let modulePath of possiblePaths) {
            try {
                console.log("  尝试路径: " + modulePath);

                // 检查文件是否存在
                if (!files.exists(modulePath)) {
                    console.log("  文件不存在，跳过");
                    continue;
                }

                // 读取模块文件内容
                let moduleContent = files.read(modulePath);
                if (!moduleContent) {
                    console.log("  文件内容为空，跳过");
                    continue;
                }

                // 创建模块执行环境
                let moduleFunction = new Function(
                    'require', 'module', 'exports', 'console', 'files', 'http', 'ui',
                    'device', 'storages', 'dialogs', 'toast', 'threads', 'setTimeout',
                    'setInterval', 'clearTimeout', 'clearInterval', 'global',
                    moduleContent
                );

                // 创建模块对象
                let module = { exports: {} };
                let exports = module.exports;

                // 执行模块
                moduleFunction(
                    loadModule, module, exports, console, files, http, ui, device,
                    storages, dialogs, toast, threads, setTimeout, setInterval,
                    clearTimeout, clearInterval, global
                );

                console.log("✓ 模块加载成功: " + modulePath);
                return module.exports;

            } catch (e) {
                console.log("  路径加载失败: " + e.message);
                continue;
            }
        }

        throw new Error("所有路径都加载失败");

    } catch (e) {
        console.error("✗ 模块加载失败: " + relativePath + " - " + e.message);
        console.error("  尝试的路径: " + possiblePaths.join(', '));
        return null;
    }
}

// 初始化应用目录
function initializeDirectories() {
    try {
        const dirs = [
            "/sdcard/脚本助手",
            "/sdcard/脚本助手/storage",
            "/sdcard/脚本助手/logs",
            "/sdcard/脚本助手/updates"
        ];

        dirs.forEach(dir => {
            if (!files.exists(dir)) {
                files.createWithDirs(dir + "/temp.txt");
                files.remove(dir + "/temp.txt");
                console.log("创建目录: " + dir);
            }
        });

        return true;
    } catch (e) {
        console.error("创建目录失败: " + e.message);
        return false;
    }
}

// 加载所有模块
function loadAllModules() {
    try {
        console.log("开始加载所有模块...");

        // 1. 加载配置
        console.log("1. 加载配置...");
        let config = loadModule("./config/constants.js");

        if (config) {
            global.API_CONFIG = config.API_CONFIG || {
                BASE_URL: "http://192.168.1.19:8527/api",
                TIMEOUT: 10000,
                RETRY_COUNT: 3
            };

            global.APP_INFO = config.APP_INFO || {
                VERSION: "1.0.0",
                VERSION_CODE: 100,
                APP_NAME: "脚本助手"
            };

            global.UPDATE_STATUS = config.UPDATE_STATUS || {
                CHECKING: 0, DOWNLOADING: 1, INSTALLING: 2,
                SUCCESS: 3, FAILED: 4, NO_UPDATE: 5
            };

            console.log("✓ 配置加载成功");
            console.log("API基础URL: " + global.API_CONFIG.BASE_URL);
        } else {
            console.warn("配置加载失败，使用默认配置");
            global.API_CONFIG = { BASE_URL: "http://192.168.1.19:8527/api", TIMEOUT: 10000 };
            global.APP_INFO = { VERSION: "1.0.0", APP_NAME: "脚本助手" };
            global.UPDATE_STATUS = { CHECKING: 0, DOWNLOADING: 1, SUCCESS: 3, FAILED: 4 };
        }

        // 2. 加载工具函数
        console.log("2. 加载工具函数...");
        let utils = loadModule("./utils/common-utils.js");
        if (utils) {
            console.log("✓ 工具函数加载成功");
        } else {
            console.warn("工具函数加载失败，部分功能可能受影响");
        }

        // 3. 加载日志模块
        console.log("3. 加载日志模块...");
        let LogModule = loadModule("./modules/log-module.js");
        if (LogModule) {
            global.LogModule = LogModule;
            appState.modules.LogModule = LogModule;
            LogModule.init();
            LogModule.log("日志模块加载成功", "INFO");
            console.log("✓ 日志模块加载成功");
        } else {
            console.warn("日志模块加载失败，使用基础日志功能");
            global.LogModule = {
                init: function() {},
                log: function(msg, level) { console.log("[" + (level || "INFO") + "] " + msg); }
            };
        }

        // 4. 加载存储模块
        console.log("4. 加载存储模块...");
        let StorageModule = loadModule("./modules/storage-module.js");
        if (StorageModule) {
            global.StorageModule = StorageModule;
            appState.modules.StorageModule = StorageModule;
            StorageModule.init();
            console.log("✓ 存储模块加载成功");
        } else {
            console.warn("存储模块加载失败，使用基础存储功能");
            global.StorageModule = {
                init: function() {},
                get: function(key, defaultValue) {
                    try { return storages.create("脚本助手").get(key, defaultValue); }
                    catch(e) { return defaultValue; }
                },
                set: function(key, value) {
                    try { return storages.create("脚本助手").put(key, value); }
                    catch(e) { return false; }
                }
            };
        }

        // 5. 加载网络模块
        console.log("5. 加载网络模块...");
        let NetworkModule = loadModule("./modules/network-module.js");
        if (NetworkModule) {
            global.NetworkModule = NetworkModule;
            appState.modules.NetworkModule = NetworkModule;
            NetworkModule.init();
            console.log("✓ 网络模块加载成功");
        } else {
            console.warn("网络模块加载失败，使用基础网络功能");
            global.NetworkModule = {
                init: function() {},
                get: function(url, data, callback) {
                    if (callback) callback(new Error("网络模块未加载"), null);
                },
                post: function(url, data, callback) {
                    if (callback) callback(new Error("网络模块未加载"), null);
                }
            };
        }

        // 6. 加载应用更新模块
        console.log("6. 加载应用更新模块...");
        let AppUpdateModule = loadModule("./modules/app-update-module.js");
        if (AppUpdateModule) {
            global.AppUpdateModule = AppUpdateModule;
            appState.modules.AppUpdateModule = AppUpdateModule;
            AppUpdateModule.init();
            console.log("✓ 应用更新模块加载成功");
        } else {
            console.warn("应用更新模块加载失败，使用基础更新功能");
            // 创建基础更新模块
            global.AppUpdateModule = {
                init: function() {
                    console.log("基础应用更新模块初始化");
                },
                checkUpdate: function(callback) {
                    if (callback) callback(new Error("更新功能暂不可用"), null);
                },
                getDeviceInfo: function() {
                    try {
                        return {
                            deviceId: device.getAndroidId() || "unknown",
                            platform: "android",
                            model: device.model || "unknown"
                        };
                    } catch (e) {
                        return { deviceId: "unknown", platform: "android", model: "unknown" };
                    }
                },
                downloadUpdate: function(version, deviceId, callback) {
                    if (callback) callback(new Error("下载功能暂不可用"), null);
                },
                reportUpdateStatus: function() {
                    // 空实现
                }
            };
            appState.modules.AppUpdateModule = global.AppUpdateModule;
            global.AppUpdateModule.init();
            console.log("✓ 基础应用更新模块已启用");
        }

        console.log("所有模块加载完成");
        return true;

    } catch (e) {
        console.error("模块加载失败: " + e.message);
        return false;
    }
}

// 创建主界面UI - 完全按照原始版本还原
function createMainUI() {
    try {
        console.log("创建主界面UI...");

        const mainUIXml = `
            <vertical bg="#F5F5F5">
                <!-- 顶部标题栏 -->
                <frame bg="#F5F5F5" h="50">
                    <horizontal gravity="center_vertical" h="*" w="*">
                        <frame w="60dp" visibility="invisible"/>
                        <text text="脚本助手" textSize="18sp" textColor="#333333" gravity="center" layout_weight="1" textStyle="bold"/>
                        <frame w="60dp" visibility="invisible"/>
                    </horizontal>
                </frame>

                <!-- 主要内容区域 -->
                <scroll layout_weight="1">
                    <vertical padding="16">
                        <!-- 欢迎卡片 -->
                        <card cardCornerRadius="8dp" cardElevation="2dp" margin="0 0 16 0">
                            <vertical padding="16" bg="#FFFFFF">
                                <text text="欢迎使用脚本助手" textSize="18sp" textColor="#333333" gravity="center" margin="0 0 8 0" textStyle="bold"/>
                                <text id="versionText" text="版本 1.0.0" textSize="14sp" textColor="#666666" gravity="center" margin="0 0 4 0"/>
                                <text id="statusText" text="系统就绪" textSize="14sp" textColor="#009688" gravity="center"/>
                            </vertical>
                        </card>

                        <!-- 权限设置卡片 -->
                        <card cardCornerRadius="8dp" cardElevation="2dp" margin="0 0 16 0">
                            <vertical padding="16" bg="#FFFFFF">
                                <text text="权限设置" textSize="16sp" textColor="#333333" margin="0 0 12 0" textStyle="bold"/>

                                <horizontal gravity="center_vertical" margin="0 0 8 0">
                                    <vertical layout_weight="1">
                                        <text text="无障碍服务" textSize="14sp" textColor="#333333"/>
                                        <text text="(点击、滑动、长按等)" textSize="12sp" textColor="#999999"/>
                                    </vertical>
                                    <Switch id="accessibilitySwitch" checked="false"/>
                                </horizontal>

                                <horizontal gravity="center_vertical">
                                    <vertical layout_weight="1">
                                        <text text="悬浮窗权限" textSize="14sp" textColor="#333333"/>
                                        <text text="(增加脚本存活率)" textSize="12sp" textColor="#999999"/>
                                    </vertical>
                                    <Switch id="floatingWindowSwitch" checked="false"/>
                                </horizontal>
                            </vertical>
                        </card>

                        <!-- 登录注册按钮区域 -->
                        <horizontal margin="0 0 16 0">
                            <button id="loginBtn" text="登录" textSize="16sp" layout_weight="1" margin="0 4 0 0" bg="#009688" textColor="#FFFFFF"/>
                            <button id="registerBtn" text="注册" textSize="16sp" layout_weight="1" margin="0 0 0 4" bg="#FF9800" textColor="#FFFFFF"/>
                        </horizontal>
                    </vertical>
                </scroll>
            </vertical>`;

        ui.layout(mainUIXml);
        console.log("✓ 主界面UI创建成功");
        return true;

    } catch (e) {
        console.error("创建主界面UI失败: " + e.message);
        return false;
    }
}

// 创建登录界面
function createLoginUI() {
    try {
        const loginUIXml = `
            <vertical bg="#F5F5F5">
                <!-- 顶部标题栏 -->
                <frame bg="#009688" h="50">
                    <horizontal gravity="center_vertical" h="*" w="*">
                        <text id="backToMainBtn" text="←" textSize="20sp" textColor="#FFFFFF" w="60dp" gravity="center" clickable="true"/>
                        <text text="用户登录" textSize="18sp" textColor="#FFFFFF" gravity="center" layout_weight="1" textStyle="bold"/>
                        <frame w="60dp"/>
                    </horizontal>
                </frame>

                <!-- 登录表单 -->
                <scroll layout_weight="1">
                    <vertical padding="24">
                        <card cardCornerRadius="8dp" cardElevation="4dp" margin="0 0 24 0">
                            <vertical padding="24" bg="#FFFFFF">
                                <text text="欢迎回来" textSize="24sp" textColor="#333333" gravity="center" margin="0 0 24 0" textStyle="bold"/>

                                <input id="phoneInput" hint="请输入手机号" inputType="phone" textSize="16sp" margin="0 0 16 0"/>
                                <input id="passwordInput" hint="请输入密码" inputType="textPassword" textSize="16sp" margin="0 0 16 0"/>

                                <horizontal gravity="center_vertical" margin="0 0 24 0">
                                    <checkbox id="rememberCheck" text="记住密码" textSize="14sp" textColor="#666666"/>
                                    <text text="忘记密码？" textSize="14sp" textColor="#009688" clickable="true" layout_weight="1" gravity="right"/>
                                </horizontal>

                                <button id="loginSubmitBtn" text="登录" textSize="16sp" bg="#009688" textColor="#FFFFFF" margin="0 0 16 0"/>

                                <horizontal gravity="center" margin="16 0 0 0">
                                    <text text="还没有账号？" textSize="14sp" textColor="#666666"/>
                                    <text id="goRegisterBtn" text="立即注册" textSize="14sp" textColor="#009688" clickable="true" margin="8 0 0 0"/>
                                </horizontal>
                            </vertical>
                        </card>
                    </vertical>
                </scroll>
            </vertical>`;

        ui.layout(loginUIXml);
        console.log("✓ 登录界面创建成功");
        return true;

    } catch (e) {
        console.error("创建登录界面失败: " + e.message);
        return false;
    }
}

// 创建注册界面
function createRegisterUI() {
    try {
        const registerUIXml = `
            <vertical bg="#F5F5F5">
                <!-- 顶部标题栏 -->
                <frame bg="#FF9800" h="50">
                    <horizontal gravity="center_vertical" h="*" w="*">
                        <text id="backToMainFromRegBtn" text="←" textSize="20sp" textColor="#FFFFFF" w="60dp" gravity="center" clickable="true"/>
                        <text text="用户注册" textSize="18sp" textColor="#FFFFFF" gravity="center" layout_weight="1" textStyle="bold"/>
                        <frame w="60dp"/>
                    </horizontal>
                </frame>

                <!-- 注册表单 -->
                <scroll layout_weight="1">
                    <vertical padding="24">
                        <card cardCornerRadius="8dp" cardElevation="4dp" margin="0 0 24 0">
                            <vertical padding="24" bg="#FFFFFF">
                                <text text="创建新账号" textSize="24sp" textColor="#333333" gravity="center" margin="0 0 24 0" textStyle="bold"/>

                                <input id="regPhoneInput" hint="请输入手机号" inputType="phone" textSize="16sp" margin="0 0 16 0"/>
                                <input id="regPasswordInput" hint="请输入密码" inputType="textPassword" textSize="16sp" margin="0 0 16 0"/>
                                <input id="regConfirmPasswordInput" hint="请确认密码" inputType="textPassword" textSize="16sp" margin="0 0 16 0"/>

                                <horizontal gravity="center_vertical" margin="0 0 24 0">
                                    <checkbox id="agreeTermsCheck" text="我同意用户协议和隐私政策" textSize="14sp" textColor="#666666"/>
                                </horizontal>

                                <button id="registerSubmitBtn" text="注册" textSize="16sp" bg="#FF9800" textColor="#FFFFFF" margin="0 0 16 0"/>

                                <horizontal gravity="center" margin="16 0 0 0">
                                    <text text="已有账号？" textSize="14sp" textColor="#666666"/>
                                    <text id="goLoginBtn" text="立即登录" textSize="14sp" textColor="#FF9800" clickable="true" margin="8 0 0 0"/>
                                </horizontal>
                            </vertical>
                        </card>
                    </vertical>
                </scroll>
            </vertical>`;

        ui.layout(registerUIXml);
        console.log("✓ 注册界面创建成功");
        return true;

    } catch (e) {
        console.error("创建注册界面失败: " + e.message);
        return false;
    }
}

// 创建脚本中心界面
function createScriptCenterUI() {
    try {
        const scriptCenterUIXml = `
            <vertical bg="#F5F5F5">
                <!-- 顶部标题栏 -->
                <frame bg="#009688" h="50">
                    <horizontal gravity="center_vertical" h="*" w="*">
                        <text id="backToMainFromScriptBtn" text="←" textSize="20sp" textColor="#FFFFFF" w="60dp" gravity="center" clickable="true"/>
                        <text text="脚本中心" textSize="18sp" textColor="#FFFFFF" gravity="center" layout_weight="1" textStyle="bold"/>
                        <text id="logoutBtn" text="退出" textSize="14sp" textColor="#FFFFFF" w="60dp" gravity="center" clickable="true"/>
                    </horizontal>
                </frame>

                <!-- 用户信息 -->
                <card cardCornerRadius="8dp" cardElevation="2dp" margin="16 16 16 8">
                    <vertical padding="16" bg="#FFFFFF">
                        <text id="userWelcomeText" text="欢迎使用脚本助手" textSize="16sp" textColor="#333333" gravity="center" textStyle="bold"/>
                        <text id="userStatusText" text="请选择要运行的脚本" textSize="14sp" textColor="#666666" gravity="center" margin="4 0 0 0"/>
                    </vertical>
                </card>

                <!-- 脚本列表 -->
                <scroll layout_weight="1">
                    <vertical padding="16 8 16 16">
                        <!-- 微信读书脚本 -->
                        <card cardCornerRadius="8dp" cardElevation="2dp" margin="0 0 16 0" clickable="true" id="wechatReadScript">
                            <horizontal padding="16" gravity="center_vertical" bg="#FFFFFF">
                                <text text="📖" textSize="32sp" margin="0 16 0 0"/>
                                <vertical layout_weight="1">
                                    <text text="微信读书脚本" textSize="16sp" textColor="#333333" textStyle="bold"/>
                                    <text text="自动阅读、收集书币" textSize="14sp" textColor="#666666" margin="4 0 0 0"/>
                                </vertical>
                                <text text="▶" textSize="20sp" textColor="#009688"/>
                            </horizontal>
                        </card>

                        <!-- 蚂蚁森林脚本 -->
                        <card cardCornerRadius="8dp" cardElevation="2dp" margin="0 0 16 0" clickable="true" id="antForestScript">
                            <horizontal padding="16" gravity="center_vertical" bg="#FFFFFF">
                                <text text="🌳" textSize="32sp" margin="0 16 0 0"/>
                                <vertical layout_weight="1">
                                    <text text="蚂蚁森林脚本" textSize="16sp" textColor="#333333" textStyle="bold"/>
                                    <text text="自动收取能量" textSize="14sp" textColor="#666666" margin="4 0 0 0"/>
                                </vertical>
                                <text text="▶" textSize="20sp" textColor="#009688"/>
                            </horizontal>
                        </card>

                        <!-- 自定义脚本 -->
                        <card cardCornerRadius="8dp" cardElevation="2dp" margin="0 0 16 0" clickable="true" id="customScript">
                            <horizontal padding="16" gravity="center_vertical" bg="#FFFFFF">
                                <text text="⚙️" textSize="32sp" margin="0 16 0 0"/>
                                <vertical layout_weight="1">
                                    <text text="自定义脚本" textSize="16sp" textColor="#333333" textStyle="bold"/>
                                    <text text="运行自定义脚本文件" textSize="14sp" textColor="#666666" margin="4 0 0 0"/>
                                </vertical>
                                <text text="▶" textSize="20sp" textColor="#009688"/>
                            </horizontal>
                        </card>
                    </vertical>
                </scroll>
            </vertical>`;

        ui.layout(scriptCenterUIXml);
        console.log("✓ 脚本中心界面创建成功");
        return true;

    } catch (e) {
        console.error("创建脚本中心界面失败: " + e.message);
        return false;
    }
}

// 绑定UI事件
function bindUIEvents() {
    try {
        console.log("绑定UI事件...");

        // 更新版本信息
        if (ui.versionText && global.APP_INFO) {
            ui.versionText.setText("版本 " + global.APP_INFO.VERSION);
        }

        // 侧边栏菜单
        if (ui.menuBtn) {
            ui.menuBtn.on("click", () => {
                ui.drawer.openDrawer(android.view.Gravity.START);
            });
        }

        // 权限开关事件
        if (ui.accessibilitySwitch) {
            ui.accessibilitySwitch.on("check", (checked) => {
                if (checked) {
                    checkAndRequestAccessibilityPermission();
                } else {
                    showToast("已关闭无障碍服务");
                }
            });
        }

        if (ui.floatingWindowSwitch) {
            ui.floatingWindowSwitch.on("check", (checked) => {
                if (checked) {
                    checkAndRequestFloatingWindowPermission();
                } else {
                    showToast("已关闭悬浮窗权限");
                }
            });
        }

        // 主页功能卡片
        if (ui.loginCard) {
            ui.loginCard.on("click", () => {
                // 检查必要权限
                if (!checkAccessibilityPermission()) {
                    showAccessibilityDialog();
                    return;
                }

                if (!checkFloatingWindowPermission()) {
                    showToast("建议开启悬浮窗权限以获得完整功能");
                }

                showLoginPage();
            });
        }

        if (ui.scriptCenterCard) {
            ui.scriptCenterCard.on("click", () => {
                showToast("脚本中心功能开发中...");
                if (global.LogModule) {
                    global.LogModule.log("用户点击脚本中心", "INFO");
                }
            });
        }

        if (ui.dailyTaskCard) {
            ui.dailyTaskCard.on("click", () => {
                showToast("日常任务功能开发中...");
                if (global.LogModule) {
                    global.LogModule.log("用户点击日常任务", "INFO");
                }
            });
        }

        if (ui.agentSalesCard) {
            ui.agentSalesCard.on("click", () => {
                showToast("代理销售功能开发中...");
                if (global.LogModule) {
                    global.LogModule.log("用户点击代理销售", "INFO");
                }
            });
        }

        if (ui.pointsCard) {
            ui.pointsCard.on("click", () => {
                showToast("积分系统功能开发中...");
                if (global.LogModule) {
                    global.LogModule.log("用户点击积分系统", "INFO");
                }
            });
        }

        if (ui.moonBoxCard) {
            ui.moonBoxCard.on("click", () => {
                showMoonBoxPage();
            });
        }

        // 快捷操作按钮
        if (ui.checkUpdateBtn) {
            ui.checkUpdateBtn.on("click", () => {
                checkForUpdates();
            });
        }

        if (ui.settingsBtn) {
            ui.settingsBtn.on("click", () => {
                showToast("设置功能开发中...");
            });
        }

        if (ui.aboutBtn) {
            ui.aboutBtn.on("click", () => {
                showAboutDialog();
            });
        }

        // 登录页面事件
        if (ui.loginSubmitBtn) {
            ui.loginSubmitBtn.on("click", () => {
                performLogin();
            });
        }

        if (ui.goRegisterBtn) {
            ui.goRegisterBtn.on("click", () => {
                showRegisterPage();
            });
        }

        if (ui.backToMainBtn) {
            ui.backToMainBtn.on("click", () => {
                showMainPage();
            });
        }

        // 注册页面事件
        if (ui.registerSubmitBtn) {
            ui.registerSubmitBtn.on("click", () => {
                performRegister();
            });
        }

        if (ui.goLoginBtn) {
            ui.goLoginBtn.on("click", () => {
                showLoginPage();
            });
        }

        if (ui.backToMainFromRegBtn) {
            ui.backToMainFromRegBtn.on("click", () => {
                showMainPage();
            });
        }

        // 脚本中心页面事件
        if (ui.wechatReadScript) {
            ui.wechatReadScript.on("click", () => {
                runWechatReadScript();
            });
        }

        if (ui.antForestScript) {
            ui.antForestScript.on("click", () => {
                runAntForestScript();
            });
        }

        if (ui.customScript) {
            ui.customScript.on("click", () => {
                runCustomScript();
            });
        }

        if (ui.backToMainFromScriptBtn) {
            ui.backToMainFromScriptBtn.on("click", () => {
                showMainPage();
            });
        }

        if (ui.logoutBtn) {
            ui.logoutBtn.on("click", () => {
                performLogout();
            });
        }



        console.log("✓ UI事件绑定成功");
        return true;

    } catch (e) {
        console.error("绑定UI事件失败: " + e.message);
        return false;
    }
}

// 页面切换函数
function showMainPage() {
    try {
        createMainUI();
        bindMainUIEvents();
        updatePermissionSwitches();
        appState.currentPage = 'main';

        if (global.LogModule) {
            global.LogModule.log("切换到主页", "INFO");
        }
    } catch (e) {
        console.error("显示主页失败: " + e.message);
    }
}

function showLoginPage() {
    try {
        // 检查必要权限
        if (!checkAccessibilityPermission()) {
            showAccessibilityDialog();
            return;
        }

        if (!checkFloatingWindowPermission()) {
            showToast("建议开启悬浮窗权限以获得完整功能");
        }

        createLoginUI();
        bindLoginUIEvents();
        appState.currentPage = 'login';

        if (global.LogModule) {
            global.LogModule.log("切换到登录页", "INFO");
        }
    } catch (e) {
        console.error("显示登录页失败: " + e.message);
    }
}

function showRegisterPage() {
    try {
        createRegisterUI();
        bindRegisterUIEvents();
        appState.currentPage = 'register';

        if (global.LogModule) {
            global.LogModule.log("切换到注册页", "INFO");
        }
    } catch (e) {
        console.error("显示注册页失败: " + e.message);
    }
}

function showScriptCenterPage() {
    try {
        createScriptCenterUI();
        bindScriptCenterUIEvents();
        appState.currentPage = 'scriptCenter';

        // 更新欢迎信息
        if (ui.userWelcomeText && appState.currentUser) {
            let welcomeMsg = "欢迎, " + (appState.currentUser.nickname || appState.currentUser.phone);
            ui.userWelcomeText.setText(welcomeMsg);
        }

        if (global.LogModule) {
            global.LogModule.log("切换到脚本中心", "INFO");
        }
    } catch (e) {
        console.error("显示脚本中心失败: " + e.message);
    }
}

// 功能实现函数
function performLogin() {
    try {
        let username = ui.usernameInput.text();
        let password = ui.passwordInput.text();

        if (!username || !password) {
            showToast("请输入用户名和密码");
            return;
        }

        // 验证手机号格式
        if (!/^1[3-9]\d{9}$/.test(username)) {
            showToast("请输入正确的手机号");
            return;
        }

        // 验证密码长度
        if (password.length < 6) {
            showToast("密码长度不能少于6位");
            return;
        }

        if (global.LogModule) {
            global.LogModule.log("用户尝试登录: " + username, "INFO");
        }

        // 显示登录中状态
        ui.loginSubmitBtn.setText("登录中...");
        ui.loginSubmitBtn.setEnabled(false);

        // 模拟登录请求
        if (global.NetworkModule) {
            global.NetworkModule.post("/v1/auth/login", {
                phone: username,
                password: password,
                deviceInfo: global.AppUpdateModule ? global.AppUpdateModule.getDeviceInfo() : {}
            }, (error, response) => {
                ui.loginSubmitBtn.setText("登录");
                ui.loginSubmitBtn.setEnabled(true);

                if (error) {
                    showToast("登录失败: " + error.message);
                    if (global.LogModule) {
                        global.LogModule.log("登录失败: " + error.message, "ERROR");
                    }
                } else if (response && response.success) {
                    appState.isLoggedIn = true;
                    appState.currentUser = response.data;

                    // 保存登录状态
                    if (global.StorageModule) {
                        global.StorageModule.set("isLoggedIn", true);
                        global.StorageModule.set("currentUser", response.data);
                        global.StorageModule.set("loginTime", Date.now());
                    }

                    showToast("登录成功！欢迎 " + (response.data.nickname || response.data.phone));
                    updateUserInfo();

                    // 登录成功后跳转到脚本中心或主页
                    setTimeout(() => {
                        showScriptCenterPage();
                    }, 1000);

                    if (global.LogModule) {
                        global.LogModule.log("用户登录成功: " + username, "INFO");
                    }
                } else {
                    let errorMsg = "登录失败";
                    if (response && response.message) {
                        errorMsg = response.message;
                    } else if (response && response.error) {
                        errorMsg = response.error;
                    }
                    showToast(errorMsg);

                    if (global.LogModule) {
                        global.LogModule.log("登录失败: " + errorMsg, "ERROR");
                    }
                }
            });
        } else {
            ui.loginSubmitBtn.setText("登录");
            ui.loginSubmitBtn.setEnabled(true);

            // 网络模块未加载时的模拟登录
            if (username === "13800138000" && password === "123456") {
                appState.isLoggedIn = true;
                appState.currentUser = {
                    phone: username,
                    nickname: "测试用户",
                    userId: "test_user_001"
                };

                if (global.StorageModule) {
                    global.StorageModule.set("isLoggedIn", true);
                    global.StorageModule.set("currentUser", appState.currentUser);
                }

                showToast("登录成功！(演示模式)");
                updateUserInfo();
                setTimeout(() => {
                    showScriptCenterPage();
                }, 1000);
            } else {
                showToast("演示模式: 请使用 13800138000 / 123456");
            }
        }

    } catch (e) {
        console.error("登录失败: " + e.message);
        showToast("登录失败: " + e.message);

        if (ui.loginSubmitBtn) {
            ui.loginSubmitBtn.setText("登录");
            ui.loginSubmitBtn.setEnabled(true);
        }
    }
}

function performRegister() {
    try {
        let username = ui.regUsernameInput.text();
        let phone = ui.regPhoneInput.text();
        let email = ui.regEmailInput.text();
        let password = ui.regPasswordInput.text();
        let confirmPassword = ui.regConfirmPasswordInput.text();

        if (!username || !phone || !email || !password || !confirmPassword) {
            showToast("请填写完整信息");
            return;
        }

        if (password !== confirmPassword) {
            showToast("两次输入的密码不一致");
            return;
        }

        if (!ui.agreeTermsCheck.isChecked()) {
            showToast("请同意用户协议和隐私政策");
            return;
        }

        if (global.LogModule) {
            global.LogModule.log("用户尝试注册: " + username, "INFO");
        }

        // 显示注册中状态
        ui.registerSubmitBtn.setText("注册中...");
        ui.registerSubmitBtn.setEnabled(false);

        // 模拟注册请求
        if (global.NetworkModule) {
            global.NetworkModule.post("/v1/auth/register", {
                username: username,
                phone: phone,
                email: email,
                password: password
            }, (error, response) => {
                ui.registerSubmitBtn.setText("注册");
                ui.registerSubmitBtn.setEnabled(true);

                if (error) {
                    showToast("注册失败: " + error.message);
                    if (global.LogModule) {
                        global.LogModule.log("注册失败: " + error.message, "ERROR");
                    }
                } else if (response && response.success) {
                    showToast("注册成功！请登录");
                    showLoginPage();

                    if (global.LogModule) {
                        global.LogModule.log("用户注册成功: " + username, "INFO");
                    }
                } else {
                    showToast("注册失败: " + (response ? response.message : "未知错误"));
                }
            });
        } else {
            ui.registerSubmitBtn.setText("注册");
            ui.registerSubmitBtn.setEnabled(true);
            showToast("网络模块未加载，无法注册");
        }

    } catch (e) {
        console.error("注册失败: " + e.message);
        showToast("注册失败: " + e.message);

        if (ui.registerSubmitBtn) {
            ui.registerSubmitBtn.setText("注册");
            ui.registerSubmitBtn.setEnabled(true);
        }
    }
}

// 权限管理函数
function checkAccessibilityPermission() {
    try {
        return auto.service != null;
    } catch (e) {
        console.error("检查无障碍权限失败: " + e.message);
        return false;
    }
}

function checkFloatingWindowPermission() {
    try {
        return floaty.checkPermission();
    } catch (e) {
        console.error("检查悬浮窗权限失败: " + e.message);
        return false;
    }
}

function checkAndRequestAccessibilityPermission() {
    if (!checkAccessibilityPermission()) {
        showAccessibilityDialog();
    } else {
        showToast("无障碍服务已开启");
        if (global.StorageModule) {
            global.StorageModule.set("accessibilityEnabled", true);
        }
    }
}

function checkAndRequestFloatingWindowPermission() {
    if (!checkFloatingWindowPermission()) {
        try {
            floaty.requestPermission();
            showToast("请在设置中开启悬浮窗权限");
        } catch (e) {
            showToast("请手动开启悬浮窗权限");
        }
    } else {
        showToast("悬浮窗权限已开启");
        if (global.StorageModule) {
            global.StorageModule.set("floatingWindowEnabled", true);
        }
    }
}

function showAccessibilityDialog() {
    try {
        dialogs.confirm("需要无障碍权限",
            "脚本助手需要无障碍服务权限才能正常工作。\n\n" +
            "无障碍服务用于:\n" +
            "• 自动点击和滑动\n" +
            "• 读取屏幕内容\n" +
            "• 执行自动化任务\n\n" +
            "是否前往设置开启？"
        ).then(confirmed => {
            if (confirmed) {
                try {
                    auto();
                    showToast("请在无障碍设置中开启脚本助手");
                } catch (e) {
                    showToast("请手动前往设置开启无障碍服务");
                }
            }
        });
    } catch (e) {
        showToast("请在设置中开启无障碍服务");
    }
}

function updatePermissionSwitches() {
    try {
        if (ui.accessibilitySwitch) {
            ui.accessibilitySwitch.setChecked(checkAccessibilityPermission());
        }
        if (ui.floatingWindowSwitch) {
            ui.floatingWindowSwitch.setChecked(checkFloatingWindowPermission());
        }
    } catch (e) {
        console.error("更新权限开关状态失败: " + e.message);
    }
}

// 其他功能函数
function checkForUpdates() {
    try {
        if (global.LogModule) {
            global.LogModule.log("用户手动检查更新", "INFO");
        }

        if (ui.checkUpdateBtn) {
            ui.checkUpdateBtn.setText("检查中...");
            ui.checkUpdateBtn.setEnabled(false);
        }

        if (global.AppUpdateModule) {
            global.AppUpdateModule.checkUpdate((error, updateInfo) => {
                if (ui.checkUpdateBtn) {
                    ui.checkUpdateBtn.setText("检查更新");
                    ui.checkUpdateBtn.setEnabled(true);
                }

                if (error) {
                    showToast("检查更新失败: " + error.message);
                    if (global.LogModule) {
                        global.LogModule.log("检查更新失败: " + error.message, "ERROR");
                    }
                } else if (updateInfo && updateInfo.hasUpdate) {
                    dialogs.confirm("发现新版本",
                        "发现新版本 " + updateInfo.version + "\n\n" +
                        (updateInfo.description || "修复了一些问题，提升了用户体验") + "\n\n" +
                        "是否立即更新？"
                    ).then(confirmed => {
                        if (confirmed) {
                            startUpdate(updateInfo);
                        }
                    });
                } else {
                    showToast("当前已是最新版本");
                }
            });
        } else {
            if (ui.checkUpdateBtn) {
                ui.checkUpdateBtn.setText("检查更新");
                ui.checkUpdateBtn.setEnabled(true);
            }
            showToast("应用更新模块未加载");
        }

    } catch (e) {
        console.error("检查更新失败: " + e.message);
        showToast("检查更新失败: " + e.message);

        if (ui.checkUpdateBtn) {
            ui.checkUpdateBtn.setText("检查更新");
            ui.checkUpdateBtn.setEnabled(true);
        }
    }
}

function startUpdate(updateInfo) {
    try {
        showToast("开始下载更新...");

        if (global.AppUpdateModule) {
            let deviceInfo = global.AppUpdateModule.getDeviceInfo();

            global.AppUpdateModule.downloadUpdate(updateInfo.version, deviceInfo.deviceId, (error, filePath) => {
                if (error) {
                    showToast("下载更新失败: " + error.message);
                    if (global.LogModule) {
                        global.LogModule.log("下载更新失败: " + error.message, "ERROR");
                    }
                } else {
                    showToast("下载完成，准备安装...");

                    // 这里可以调用安装逻辑
                    dialogs.alert("安装提示",
                        "更新文件已下载完成！\n\n" +
                        "文件位置: " + filePath + "\n\n" +
                        "请手动安装APK文件完成更新。"
                    );
                }
            });
        }

    } catch (e) {
        console.error("开始更新失败: " + e.message);
        showToast("开始更新失败: " + e.message);
    }
}

function showMoonBoxPage() {
    try {
        if (global.LogModule) {
            global.LogModule.log("用户点击月光宝盒", "INFO");
        }

        dialogs.alert("月光宝盒",
            "月光宝盒功能正在开发中...\n\n" +
            "即将支持:\n" +
            "• 微信阅读任务\n" +
            "• 点赞任务\n" +
            "• 评论任务\n" +
            "• 关注任务\n\n" +
            "敬请期待！"
        );

    } catch (e) {
        console.error("显示月光宝盒页面失败: " + e.message);
        showToast("月光宝盒功能暂不可用");
    }
}

function showAboutDialog() {
    try {
        let aboutText = "脚本助手 v" + (global.APP_INFO ? global.APP_INFO.VERSION : "1.0.0") + "\n\n" +
                       "基于AutoX.js开发的安卓脚本软件\n\n" +
                       "主要功能:\n" +
                       "• 用户登录注册\n" +
                       "• 脚本中心\n" +
                       "• 日常任务\n" +
                       "• 代理销售\n" +
                       "• 积分系统\n" +
                       "• 月光宝盒\n" +
                       "• 自动更新\n\n" +
                       "技术支持: 脚本助手团队";

        dialogs.alert("关于脚本助手", aboutText);

        if (global.LogModule) {
            global.LogModule.log("用户查看关于信息", "INFO");
        }

    } catch (e) {
        console.error("显示关于对话框失败: " + e.message);
        showToast("无法显示关于信息");
    }
}

function updateUserInfo() {
    try {
        if (appState.isLoggedIn && appState.currentUser) {
            if (ui.userNameText) {
                ui.userNameText.setText(appState.currentUser.username || "已登录用户");
            }
            if (ui.userStatusText) {
                ui.userStatusText.setText("欢迎回来！");
            }
        } else {
            if (ui.userNameText) {
                ui.userNameText.setText("未登录");
            }
            if (ui.userStatusText) {
                ui.userStatusText.setText("点击登录获取更多功能");
            }
        }
    } catch (e) {
        console.error("更新用户信息失败: " + e.message);
    }
}

function showToast(message) {
    try {
        toast(message);
        console.log("Toast: " + message);
    } catch (e) {
        console.log("Toast失败: " + e.message + ", 消息: " + message);
    }
}

function exitApp() {
    try {
        if (global.LogModule) {
            global.LogModule.log("用户退出应用", "INFO");
        }

        dialogs.confirm("退出应用", "确定要退出脚本助手吗？").then(confirmed => {
            if (confirmed) {
                showToast("再见！");
                setTimeout(() => {
                    exit();
                }, 1000);
            }
        });

    } catch (e) {
        console.error("退出应用失败: " + e.message);
        exit();
    }
}

// 脚本运行功能
function runWechatReadScript() {
    try {
        if (!checkAccessibilityPermission()) {
            showAccessibilityDialog();
            return;
        }

        if (global.LogModule) {
            global.LogModule.log("启动微信读书脚本", "INFO");
        }

        dialogs.confirm("运行脚本", "确定要运行微信读书脚本吗？\n\n脚本将自动打开微信读书并开始阅读。").then(confirmed => {
            if (confirmed) {
                showToast("正在启动微信读书脚本...");

                // 这里可以调用具体的脚本文件
                // engines.execScriptFile("./scripts/wechat-read.js");

                // 模拟脚本运行
                setTimeout(() => {
                    showToast("微信读书脚本运行中，请保持屏幕常亮");
                }, 2000);
            }
        });

    } catch (e) {
        console.error("运行微信读书脚本失败: " + e.message);
        showToast("脚本运行失败: " + e.message);
    }
}

function runAntForestScript() {
    try {
        if (!checkAccessibilityPermission()) {
            showAccessibilityDialog();
            return;
        }

        if (global.LogModule) {
            global.LogModule.log("启动蚂蚁森林脚本", "INFO");
        }

        dialogs.confirm("运行脚本", "确定要运行蚂蚁森林脚本吗？\n\n脚本将自动打开支付宝并收取能量。").then(confirmed => {
            if (confirmed) {
                showToast("正在启动蚂蚁森林脚本...");

                // 这里可以调用具体的脚本文件
                // engines.execScriptFile("./scripts/ant-forest.js");

                // 模拟脚本运行
                setTimeout(() => {
                    showToast("蚂蚁森林脚本运行中，请保持屏幕常亮");
                }, 2000);
            }
        });

    } catch (e) {
        console.error("运行蚂蚁森林脚本失败: " + e.message);
        showToast("脚本运行失败: " + e.message);
    }
}

function runCustomScript() {
    try {
        if (!checkAccessibilityPermission()) {
            showAccessibilityDialog();
            return;
        }

        dialogs.rawInput("自定义脚本", "请输入脚本文件路径:", "/sdcard/script.js").then(scriptPath => {
            if (scriptPath && files.exists(scriptPath)) {
                dialogs.confirm("运行脚本", "确定要运行脚本文件吗？\n\n" + scriptPath).then(confirmed => {
                    if (confirmed) {
                        try {
                            engines.execScriptFile(scriptPath);
                            showToast("脚本已启动");

                            if (global.LogModule) {
                                global.LogModule.log("运行自定义脚本: " + scriptPath, "INFO");
                            }
                        } catch (e) {
                            showToast("脚本运行失败: " + e.message);
                        }
                    }
                });
            } else if (scriptPath) {
                showToast("脚本文件不存在: " + scriptPath);
            }
        });

    } catch (e) {
        console.error("运行自定义脚本失败: " + e.message);
        showToast("脚本运行失败: " + e.message);
    }
}

function performLogout() {
    try {
        dialogs.confirm("退出登录", "确定要退出登录吗？").then(confirmed => {
            if (confirmed) {
                // 清除登录状态
                appState.isLoggedIn = false;
                appState.currentUser = null;

                if (global.StorageModule) {
                    global.StorageModule.set("isLoggedIn", false);
                    global.StorageModule.set("currentUser", null);
                }

                // 更新UI
                updateUserInfo();

                // 跳转到主页
                showMainPage();

                showToast("已退出登录");

                if (global.LogModule) {
                    global.LogModule.log("用户退出登录", "INFO");
                }
            }
        });

    } catch (e) {
        console.error("退出登录失败: " + e.message);
        showToast("退出登录失败: " + e.message);
    }
}

// 检查登录状态
function checkLoginStatus() {
    try {
        if (global.StorageModule) {
            let isLoggedIn = global.StorageModule.get("isLoggedIn", false);
            let currentUser = global.StorageModule.get("currentUser", null);

            if (isLoggedIn && currentUser) {
                appState.isLoggedIn = true;
                appState.currentUser = currentUser;
                updateUserInfo();

                if (global.LogModule) {
                    global.LogModule.log("自动登录成功: " + currentUser.username, "INFO");
                }
            }
        }
    } catch (e) {
        console.error("检查登录状态失败: " + e.message);
    }
}

// 应用初始化
function initializeApp() {
    try {
        console.log("开始初始化应用...");

        // 1. 初始化目录
        if (!initializeDirectories()) {
            throw new Error("初始化目录失败");
        }

        // 2. 加载所有模块
        if (!loadAllModules()) {
            throw new Error("加载模块失败");
        }

        // 3. 显示主页面
        showMainPage();

        // 5. 检查登录状态
        checkLoginStatus();

        // 6. 更新权限状态
        setTimeout(() => {
            updatePermissionSwitches();
        }, 1000);

        // 7. 更新状态
        if (ui.statusText) {
            ui.statusText.setText("应用启动成功");
        }

        console.log("✓ 应用初始化完成");

        if (global.LogModule) {
            global.LogModule.log("脚本助手启动成功", "INFO");
        }

        return true;

    } catch (e) {
        console.error("应用初始化失败: " + e.message);

        // 显示错误对话框
        try {
            dialogs.alert("启动失败",
                "脚本助手启动失败:\n\n" + e.message + "\n\n" +
                "请检查应用权限和运行环境。\n\n" +
                "如果问题持续存在，请联系技术支持。"
            );
        } catch (dialogError) {
            showToast("启动失败: " + e.message);
        }

        return false;
    }
}

// 主函数
function main() {
    try {
        console.log("脚本助手主函数启动...");

        // 检查基础环境
        if (typeof device === 'undefined' || typeof ui === 'undefined' ||
            typeof files === 'undefined' || typeof http === 'undefined') {
            throw new Error("AutoX.js环境异常，缺少必要对象");
        }

        // 初始化应用
        if (!initializeApp()) {
            throw new Error("应用初始化失败");
        }

        console.log("=== 脚本助手启动完成 ===");

    } catch (e) {
        console.error("主函数执行失败: " + e.message);

        // 最后的错误处理
        try {
            dialogs.alert("严重错误",
                "脚本助手无法启动:\n\n" + e.message + "\n\n" +
                "可能的原因:\n" +
                "• AutoX.js版本不兼容\n" +
                "• 应用权限不足\n" +
                "• 系统环境异常\n\n" +
                "请检查环境后重试。"
            );
        } catch (dialogError) {
            console.error("无法显示错误对话框: " + dialogError.message);
            toast("脚本助手启动失败: " + e.message);
        }

        // 延迟退出
        setTimeout(() => {
            exit();
        }, 5000);
    }
}

// 启动应用
console.log("准备启动脚本助手...");

// 延迟启动，确保环境完全准备好
setTimeout(() => {
    main();
}, 500);

// 导出主要函数供外部使用（如果需要）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        main,
        initializeApp,
        loadAllModules,
        createMainUI,
        bindUIEvents,
        appState
    };
}
