/**
 * 应用更新模块
 * 负责处理应用版本更新相关功能
 */

const AppUpdateModule = (function () {
    // 应用版本信息
    const APP_VERSION = typeof APP_INFO !== 'undefined' ? APP_INFO.VERSION : "1.0.0";
    const VERSION_CODE = typeof APP_INFO !== 'undefined' ? APP_INFO.VERSION_CODE : 100;

    // 更新状态常量
    const UPDATE_STATUS = typeof window !== 'undefined' && window.UPDATE_STATUS ? window.UPDATE_STATUS : {
        CHECKING: 0,        // 检查中
        DOWNLOADING: 1,     // 下载中
        INSTALLING: 2,      // 安装中
        SUCCESS: 3,         // 成功
        FAILED: 4,          // 失败
        NO_UPDATE: 5        // 无更新
    };

    return {
        /**
         * 初始化应用更新模块
         */
        init: function () {
            try {
                if (typeof LogModule !== 'undefined') {
                    LogModule.log("应用更新模块初始化");
                    LogModule.log("当前应用版本: " + APP_VERSION + " (版本代码: " + VERSION_CODE + ")");
                } else {
                    console.log("应用更新模块初始化");
                    console.log("当前应用版本: " + APP_VERSION + " (版本代码: " + VERSION_CODE + ")");
                }
            } catch (e) {
                console.error("应用更新模块初始化失败: " + e.message);
                throw e;
            }
        },

        /**
         * 获取当前版本信息
         * @returns {Object} 版本信息
         */
        getCurrentVersion: function () {
            return {
                version: APP_VERSION,
                versionCode: VERSION_CODE
            };
        },

        /**
         * 获取更新状态常量
         * @returns {Object} 更新状态常量
         */
        getUpdateStatus: function () {
            return UPDATE_STATUS;
        },

        /**
         * 获取设备信息
         * @returns {Object} 设备信息对象
         */
        getDeviceInfo: function () {
            try {
                return {
                    deviceId: device.getAndroidId() || "unknown_device",
                    platform: "android",
                    deviceModel: device.model || "Unknown Model",
                    androidVersion: device.release || "Unknown Version",
                    sdkVersion: android.os.Build.VERSION.SDK_INT || 0
                };
            } catch (e) {
                if (typeof LogModule !== 'undefined') {
                    LogModule.log("获取设备信息失败: " + e.message, "ERROR");
                }
                return {
                    deviceId: "unknown_device",
                    platform: "android",
                    deviceModel: "Unknown Model",
                    androidVersion: "Unknown Version",
                    sdkVersion: 0
                };
            }
        },

        /**
         * 检查更新
         * @param {function} callback - 回调函数 (error, updateInfo)
         */
        checkUpdate: function (callback) {
            try {
                if (typeof LogModule !== 'undefined') {
                    LogModule.log("开始检查应用更新");
                }

                // 获取设备信息
                const deviceInfo = this.getDeviceInfo();

                // 构建请求参数
                const requestData = {
                    deviceId: deviceInfo.deviceId,
                    currentVersion: APP_VERSION,
                    versionCode: VERSION_CODE,
                    platform: deviceInfo.platform,
                    deviceModel: deviceInfo.deviceModel,
                    androidVersion: deviceInfo.androidVersion
                };

                // 发送检查更新请求
                if (typeof NetworkModule !== 'undefined') {
                    NetworkModule.get("/v1/update/check", requestData, (error, response) => {
                        if (error) {
                            if (typeof LogModule !== 'undefined') {
                                LogModule.log("检查更新失败: " + error.message, "ERROR");
                            }
                            if (callback) callback(error, null);
                            return;
                        }

                        try {
                            if (response && response.success) {
                                const updateInfo = response.data;
                                
                                if (typeof LogModule !== 'undefined') {
                                    LogModule.log("检查更新成功: " + JSON.stringify(updateInfo));
                                }

                                // 判断是否有更新
                                if (updateInfo && updateInfo.hasUpdate) {
                                    if (typeof LogModule !== 'undefined') {
                                        LogModule.log("发现新版本: " + updateInfo.version);
                                    }
                                } else {
                                    if (typeof LogModule !== 'undefined') {
                                        LogModule.log("当前已是最新版本");
                                    }
                                }

                                if (callback) callback(null, updateInfo);
                            } else {
                                const checkError = new Error(response ? response.message || "检查更新失败" : "服务器响应异常");
                                if (callback) callback(checkError, null);
                            }
                        } catch (e) {
                            if (typeof LogModule !== 'undefined') {
                                LogModule.log("解析更新信息失败: " + e.message, "ERROR");
                            }
                            if (callback) callback(e, null);
                        }
                    });
                } else {
                    const networkError = new Error("网络模块未初始化");
                    if (callback) callback(networkError, null);
                }

            } catch (e) {
                if (typeof LogModule !== 'undefined') {
                    LogModule.log("检查更新异常: " + e.message, "ERROR");
                }
                if (callback) callback(e, null);
            }
        },

        /**
         * 下载更新
         * @param {string} version - 版本号
         * @param {string} deviceId - 设备ID
         * @param {function} callback - 回调函数 (error, filePath)
         * @param {function} progressCallback - 进度回调函数
         */
        downloadUpdate: function (version, deviceId, callback, progressCallback) {
            try {
                if (typeof LogModule !== 'undefined') {
                    LogModule.log("开始下载更新: " + version);
                }

                // 构建下载URL
                const downloadUrl = "/v1/update/download/" + version + "?deviceId=" + encodeURIComponent(deviceId);
                
                // 构建保存路径
                const savePath = "/sdcard/脚本助手/updates/app_v" + version + ".apk";

                // 确保更新目录存在
                const updateDir = "/sdcard/脚本助手/updates";
                if (!files.exists(updateDir)) {
                    files.createWithDirs(updateDir + "/temp.txt");
                    files.remove(updateDir + "/temp.txt");
                }

                // 报告下载开始状态
                this.reportUpdateStatus(deviceId, APP_VERSION, version, UPDATE_STATUS.DOWNLOADING, null);

                // 使用网络模块下载文件
                if (typeof NetworkModule !== 'undefined') {
                    NetworkModule.downloadFile(downloadUrl, savePath, (error, filePath) => {
                        if (error) {
                            if (typeof LogModule !== 'undefined') {
                                LogModule.log("下载更新失败: " + error.message, "ERROR");
                            }
                            
                            // 报告下载失败状态
                            this.reportUpdateStatus(deviceId, APP_VERSION, version, UPDATE_STATUS.FAILED, error.message);
                            
                            if (callback) callback(error, null);
                            return;
                        }

                        if (typeof LogModule !== 'undefined') {
                            LogModule.log("更新下载完成: " + filePath);
                        }

                        if (callback) callback(null, filePath);
                    }, progressCallback);
                } else {
                    const downloadError = new Error("网络模块未初始化");
                    if (callback) callback(downloadError, null);
                }

            } catch (e) {
                if (typeof LogModule !== 'undefined') {
                    LogModule.log("下载更新异常: " + e.message, "ERROR");
                }
                if (callback) callback(e, null);
            }
        },

        /**
         * 报告更新状态
         * @param {string} deviceId - 设备ID
         * @param {string} fromVersion - 原版本
         * @param {string} toVersion - 目标版本
         * @param {number} status - 状态
         * @param {string} errorMessage - 错误信息（可选）
         */
        reportUpdateStatus: function (deviceId, fromVersion, toVersion, status, errorMessage) {
            try {
                const statusData = {
                    deviceId: deviceId,
                    fromVersion: fromVersion,
                    toVersion: toVersion,
                    status: status,
                    timestamp: Date.now()
                };

                if (errorMessage) {
                    statusData.errorMessage = errorMessage;
                }

                if (typeof LogModule !== 'undefined') {
                    LogModule.log("报告更新状态: " + JSON.stringify(statusData));
                }

                // 发送状态报告
                if (typeof NetworkModule !== 'undefined') {
                    NetworkModule.post("/v1/update/status", statusData, (error, response) => {
                        if (error) {
                            if (typeof LogModule !== 'undefined') {
                                LogModule.log("报告更新状态失败: " + error.message, "ERROR");
                            }
                        } else {
                            if (typeof LogModule !== 'undefined') {
                                LogModule.log("更新状态报告成功");
                            }
                        }
                    });
                }

            } catch (e) {
                if (typeof LogModule !== 'undefined') {
                    LogModule.log("报告更新状态异常: " + e.message, "ERROR");
                }
            }
        },

        /**
         * 自动检查更新（启动时调用）
         * @param {function} callback - 回调函数
         */
        autoCheckUpdate: function (callback) {
            try {
                // 检查是否启用自动更新
                let autoUpdateEnabled = true;
                if (typeof StorageModule !== 'undefined') {
                    autoUpdateEnabled = StorageModule.get("autoUpdateEnabled", true);
                }

                if (!autoUpdateEnabled) {
                    if (typeof LogModule !== 'undefined') {
                        LogModule.log("自动更新已禁用");
                    }
                    if (callback) callback(null, { hasUpdate: false, reason: "自动更新已禁用" });
                    return;
                }

                // 检查更新频率限制（避免频繁检查）
                let lastCheckTime = 0;
                if (typeof StorageModule !== 'undefined') {
                    lastCheckTime = StorageModule.get("lastUpdateCheckTime", 0);
                }

                const now = Date.now();
                const checkInterval = 24 * 60 * 60 * 1000; // 24小时

                if (now - lastCheckTime < checkInterval) {
                    if (typeof LogModule !== 'undefined') {
                        LogModule.log("距离上次检查更新时间过短，跳过检查");
                    }
                    if (callback) callback(null, { hasUpdate: false, reason: "检查频率限制" });
                    return;
                }

                // 执行更新检查
                this.checkUpdate((error, updateInfo) => {
                    // 更新最后检查时间
                    if (typeof StorageModule !== 'undefined') {
                        StorageModule.set("lastUpdateCheckTime", now);
                    }

                    if (callback) callback(error, updateInfo);
                });

            } catch (e) {
                if (typeof LogModule !== 'undefined') {
                    LogModule.log("自动检查更新异常: " + e.message, "ERROR");
                }
                if (callback) callback(e, null);
            }
        }
    };
})();

// 导出模块（AutoX.js兼容方式）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AppUpdateModule;
} else {
    // AutoX.js环境 - 将模块添加到全局作用域
    global.AppUpdateModule = AppUpdateModule;
    if (typeof window !== 'undefined') {
        window.AppUpdateModule = AppUpdateModule;
    }
}
