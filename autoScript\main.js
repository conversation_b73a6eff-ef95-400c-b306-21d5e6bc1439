// 使用"ui"模式
"ui";

/**
 * 脚本助手 - 1:1完全还原版本
 * 基于原始main.js的精确还原
 * 保持技术改进的同时完全还原原始功能和界面
 */

console.log("=== 脚本助手启动 ===");
console.log("准备启动脚本助手...");

// 全局状态管理
var currentPage = 'main';
var isLoggedIn = false;
var currentUser = null;

// 退出确认相关变量
var exitConfirmationTime = 0;
var exitConfirmationTimeout = null;

// 自定义模块加载器 - 修复API兼容性问题
function loadModule(modulePath) {
    var scriptPath = files.cwd();
    console.log("检测到脚本路径: " + scriptPath);

    var possiblePaths = [
        files.join(scriptPath, modulePath),
        files.join("/storage/emulated/0/脚本/autoScript", modulePath),
        files.join("/sdcard/脚本/autoScript", modulePath)
    ];

    for (var i = 0; i < possiblePaths.length; i++) {
        var path = possiblePaths[i];
        console.log("尝试加载模块: " + modulePath);
        console.log("  尝试路径: " + path);

        try {
            if (files.exists(path)) {
                console.log("  文件存在，开始读取内容...");
                var moduleContent = files.read(path);
                console.log("  文件内容读取成功，长度: " + moduleContent.length);

                var moduleFunction = new Function('module', 'exports', 'require', '__filename', '__dirname', moduleContent);
                var module = { exports: {} };

                // 修复：使用兼容的路径处理方法
                var parentPath = getParentPath(path);
                console.log("  父路径: " + parentPath);

                // 执行模块函数
                moduleFunction(module, module.exports, require, path, parentPath);
                console.log("✓ 模块加载成功: " + path);
                return module.exports;
            } else {
                console.log("  文件不存在: " + path);
            }
        } catch (e) {
            console.log("  路径加载失败: " + e.message);
            console.log("  错误详情: " + e.stack);
        }
    }

    console.error("✗ 模块加载失败: " + modulePath + " - 所有路径都加载失败");
    console.error("  尝试的路径: " + possiblePaths.join(", "));
    return null;
}

// 兼容的路径处理函数
function getParentPath(filePath) {
    try {
        // 方法1：尝试使用files.getParent（如果存在）
        if (typeof files.getParent === 'function') {
            return files.getParent(filePath);
        }

        // 方法2：尝试使用files.getParentPath（备选API）
        if (typeof files.getParentPath === 'function') {
            return files.getParentPath(filePath);
        }

        // 方法3：使用字符串处理获取父目录
        if (filePath && typeof filePath === 'string') {
            // 标准化路径分隔符
            var normalizedPath = filePath.replace(/\\/g, '/');
            var lastSlashIndex = normalizedPath.lastIndexOf('/');
            if (lastSlashIndex > 0) {
                return normalizedPath.substring(0, lastSlashIndex);
            }
        }

        // 方法4：返回默认路径
        return files.cwd();
    } catch (e) {
        console.warn("获取父路径失败，使用当前工作目录: " + e.message);
        try {
            return files.cwd();
        } catch (cwdError) {
            console.warn("获取当前工作目录也失败，使用固定路径");
            return "/storage/emulated/0/脚本/autoScript";
        }
    }
}

// 加载所有模块
function loadAllModules() {
    console.log("开始加载所有模块...");

    // 1. 加载配置
    console.log("1. 加载配置...");
    try {
        var constants = loadModule("./config/constants.js");
        if (constants) {
            global.APP_INFO = constants.APP_INFO;
            global.API_CONFIG = constants.API_CONFIG;
            console.log("✓ 配置加载成功");
            console.log("API基础URL: " + global.API_CONFIG.BASE_URL);
        } else {
            throw new Error("配置模块加载失败");
        }
    } catch (e) {
        console.warn("配置加载失败，使用默认配置: " + e.message);
        global.APP_INFO = { VERSION: "1.0.0", NAME: "脚本助手" };
        global.API_CONFIG = { BASE_URL: "http://************:8527/api" };
    }

    // 2. 加载工具函数
    console.log("2. 加载工具函数...");
    try {
        var commonUtils = loadModule("./utils/common-utils.js");
        if (commonUtils) {
            global.CommonUtils = commonUtils;
            console.log("✓ 工具函数加载成功");
        } else {
            throw new Error("工具函数模块加载失败");
        }
    } catch (e) {
        console.warn("工具函数加载失败，使用基础功能: " + e.message);
        global.CommonUtils = {
            formatDate: function (date) { return date.toString(); },
            generateId: function () { return Date.now().toString(); }
        };
    }

    // 3. 加载日志模块
    console.log("3. 加载日志模块...");
    try {
        var logModule = loadModule("./modules/log-module.js");
        if (logModule) {
            global.LogModule = logModule;
            global.LogModule.init();
            console.log("✓ 日志模块加载成功");
        } else {
            throw new Error("日志模块加载失败");
        }
    } catch (e) {
        console.warn("日志模块加载失败，使用基础日志功能: " + e.message);
        global.LogModule = {
            init: function () { },
            log: function (message, level) {
                console.log("[" + (level || "INFO") + "] " + message);
            }
        };
    }

    // 4. 加载存储模块
    console.log("4. 加载存储模块...");
    try {
        var storageModule = loadModule("./modules/storage-module.js");
        if (storageModule) {
            global.StorageModule = storageModule;
            global.StorageModule.init();
            console.log("✓ 存储模块加载成功");
        } else {
            throw new Error("存储模块加载失败");
        }
    } catch (e) {
        console.warn("存储模块加载失败，使用基础存储功能: " + e.message);
        global.StorageModule = {
            init: function () { },
            get: function (key, defaultValue) { return defaultValue; },
            set: function (key, value) { return true; }
        };
    }

    // 4.5. 初始化配置模块
    console.log("4.5. 初始化配置模块...");
    try {
        // 尝试加载外部配置模块
        var configModule = loadModule("./modules/config-module.js");
        if (configModule) {
            global.ConfigModule = configModule;
            global.ConfigModule.init();
            console.log("✓ 外部配置模块加载成功");
        } else {
            throw new Error("外部配置模块加载失败");
        }
    } catch (e) {
        console.warn("外部配置模块加载失败，使用基础配置功能: " + e.message);
        // 创建基础配置模块
        global.ConfigModule = {
            data: {},
            init: function () {
                console.log("基础配置模块初始化完成");
            },
            get: function (key, defaultValue) {
                var value = this.data[key];
                console.log("ConfigModule.get('" + key + "') = " + (value !== undefined ? value : defaultValue));
                return value !== undefined ? value : defaultValue;
            },
            set: function (key, value) {
                this.data[key] = value;
                console.log("ConfigModule.set('" + key + "', '" + value + "')");
                return true;
            }
        };
        global.ConfigModule.init();
    }

    // 5. 加载网络模块
    console.log("5. 加载网络模块...");

    // 先定义基础网络功能
    var baseNetworkFunctions = {
        init: function () { },

        // 获取用户Token
        getUserToken: function () {
            console.log("开始获取用户Token...");

            // 优先从StorageModule获取Token（登录时保存的位置）
            if (global.StorageModule) {
                console.log("尝试从StorageModule获取Token...");
                var token = global.StorageModule.get("userToken");
                if (token) {
                    console.log("从StorageModule获取到Token: " + token.substring(0, 10) + "...");
                    return token;
                } else {
                    console.log("StorageModule中没有找到Token");
                }
            } else {
                console.log("StorageModule不可用");
            }

            // 降级到ConfigModule
            if (global.ConfigModule) {
                console.log("尝试从ConfigModule获取Token...");
                var token = global.ConfigModule.get("userToken");
                if (token) {
                    console.log("从ConfigModule获取到Token: " + token.substring(0, 10) + "...");
                    return token;
                } else {
                    console.log("ConfigModule中没有找到Token");
                }
            } else {
                console.log("ConfigModule不可用");
            }

            // 最后尝试从currentUser全局变量获取
            if (currentUser && currentUser.token) {
                console.log("从currentUser获取到Token: " + currentUser.token.substring(0, 10) + "...");
                return currentUser.token;
            } else {
                console.log("currentUser中没有Token");
            }

            console.warn("未能获取到用户Token");
            return null;
        },

            // Token调试工具函数
            debugTokenStatus: function() {
                console.log("========== Token状态调试 ==========");

                // 检查StorageModule中的Token
                if (global.StorageModule) {
                    var storageToken = global.StorageModule.get("userToken");
                    console.log("StorageModule中的Token: " + (storageToken ? storageToken.substring(0, 10) + "..." : "null"));
                } else {
                    console.log("StorageModule不可用");
                }

                // 检查ConfigModule中的Token
                if (global.ConfigModule) {
                    var configToken = global.ConfigModule.get("userToken");
                    console.log("ConfigModule中的Token: " + (configToken ? configToken.substring(0, 10) + "..." : "null"));
                } else {
                    console.log("ConfigModule不可用");
                }

                // 检查currentUser全局变量
                if (currentUser && currentUser.token) {
                    console.log("currentUser中的Token: " + currentUser.token.substring(0, 10) + "...");
                } else {
                    console.log("currentUser中无Token");
                }

                // 检查getUserToken()函数的返回值
                var token = this.getUserToken();
                console.log("getUserToken()返回: " + (token ? token.substring(0, 10) + "..." : "null"));

                console.log("========== Token状态调试结束 ==========");
                return token;
            },

            // Token完整性测试函数
            testTokenFlow: function() {
                console.log("========== Token完整性测试 ==========");

                // 1. 测试Token获取
                var token = this.getUserToken();
                console.log("1. Token获取测试: " + (token ? "成功 (" + token.substring(0, 10) + "...)" : "失败"));

                if (!token) {
                    console.log("Token获取失败，无法继续测试");
                    return false;
                }

                // 2. 测试网络请求Token传递
                console.log("2. 测试网络请求Token传递...");
                this.post("/test/token", { test: true }, function(error, result) {
                    if (error) {
                        console.log("Token传递测试失败: " + error.message);
                    } else {
                        console.log("Token传递测试结果: " + JSON.stringify(result));
                    }
                }, token);

                console.log("========== Token完整性测试结束 ==========");
                return true;
            },

            // HTTP请求头验证函数
            verifyAuthorizationHeader: function(token) {
                console.log("========== Authorization头验证 ==========");

                if (!token) {
                    console.error("Token为空，无法验证Authorization头");
                    return false;
                }

                // 验证Token格式
                if (typeof token !== 'string') {
                    console.error("Token不是字符串类型: " + typeof token);
                    return false;
                }

                if (token.length === 0) {
                    console.error("Token是空字符串");
                    return false;
                }

                // 构建Authorization头
                var authHeader = "Bearer " + token;
                console.log("构建的Authorization头: " + authHeader);
                console.log("Authorization头长度: " + authHeader.length);
                console.log("Bearer前缀检查: " + (authHeader.startsWith("Bearer ") ? "正确" : "错误"));
                console.log("Token部分: " + token.substring(0, 10) + "...");

                // 验证格式
                var bearerPattern = /^Bearer .+$/;
                var isValidFormat = bearerPattern.test(authHeader);
                console.log("Authorization头格式验证: " + (isValidFormat ? "通过" : "失败"));

                console.log("========== Authorization头验证结束 ==========");
                return isValidFormat;
            },

            // 发送网络请求 - 完全按照原版还原
            request: function (method, url, data, callback, token) {
                try {
                    // 构建完整URL
                    var fullUrl = url.startsWith("http") ? url : "http://8.137.107.224:8527/api" + url;

                    global.LogModule.log("发送" + method + "请求: " + fullUrl);

                    // 构建请求参数
                    var options = {
                        method: method,
                        headers: {
                            "Content-Type": "application/json"
                        },
                        timeout: 30000
                    };

                    // 添加认证令牌
                    if (token) {
                        options.headers["Authorization"] = "Bearer " + token;
                        global.LogModule.log("基础网络模块添加认证令牌: Bearer " + token.substring(0, 10) + "...");
                        console.log("基础网络模块HTTP请求头包含Authorization: Bearer " + token.substring(0, 10) + "...");
                        console.log("基础网络模块完整请求头: " + JSON.stringify(options.headers));
                    } else {
                        console.warn("基础网络模块警告：API请求未携带Token，可能导致认证失败");
                        global.LogModule.log("基础网络模块警告：API请求未携带Token", "WARN");
                    }

                    // 添加请求数据
                    if (data && method !== "GET") {
                        options.body = JSON.stringify(data);
                    }

                    // 发送请求
                    http.request(fullUrl, options, function (response, error) {
                        if (error) {
                            global.LogModule.log("网络请求失败: " + error.message, "ERROR");
                            if (callback) callback(error, null);
                            return;
                        }

                        try {
                            var result = JSON.parse(response.body.string());
                            global.LogModule.log("网络请求成功: " + JSON.stringify(result));
                            if (callback) callback(null, result);
                        } catch (parseError) {
                            global.LogModule.log("解析响应失败: " + parseError.message, "ERROR");
                            if (callback) callback(parseError, null);
                        }
                    });

                } catch (e) {
                    global.LogModule.log("发送网络请求异常: " + e.message, "ERROR");
                    if (callback) callback(e, null);
                }
            },

            // POST请求
            post: function (url, data, callback, token) {
                this.request("POST", url, data, callback, token);
            },

            // GET请求
            get: function (url, data, callback, token) {
                this.request("GET", url, data, callback, token);
            },

            login: function (phone, password, callback) {
                var that = this;
                // 演示模式登录
                if (phone === "13800138000" && password === "123456") {
                    setTimeout(function() {
                        var userData = {
                            id: "user123",
                            phone: phone,
                            token: "demo_token_" + Date.now(),
                            referrer: "admin",
                            invitationCode: "DEMO123"
                        };
                        console.log("演示模式登录成功，生成Token: " + userData.token);
                        callback(null, userData);
                    }, 1000);
                } else {
                    // 尝试真实API调用
                    this.post("/user/login", { phone: phone, password: password }, function (error, result) {
                        if (error) {
                            // API调用失败，使用演示模式
                            setTimeout(function() {
                                callback(new Error("用户名或密码错误"), null);
                            }, 500);
                        } else {
                            callback(null, result);
                        }
                    });
                }
            },

            sendRegisterCode: function (phone, callback) {
                this.post("/user/send/register/code", { phone: phone }, function (error, result) {
                    if (error) {
                        setTimeout(() => {
                            callback(null, { code: 200, message: "验证码发送成功" });
                        }, 1000);
                    } else {
                        callback(null, result);
                    }
                });
            },

            sendResetPasswordCode: function (phone, callback) {
                this.post("/user/send/reset/code", { phone: phone }, function (error, result) {
                    if (error) {
                        setTimeout(() => {
                            callback(null, { code: 200, message: "验证码发送成功" });
                        }, 1000);
                    } else {
                        callback(null, result);
                    }
                });
            },

            checkReferrerId: function (referrerId, callback) {
                this.post("/user/check/referrer", { referrerId: referrerId }, function (error, result) {
                    if (error) {
                        setTimeout(() => {
                            callback(null, { code: 200, data: true, message: "推荐人ID有效" });
                        }, 500);
                    } else {
                        callback(null, result);
                    }
                });
            },

            register: function (phone, code, password, referrerId, callback) {
                this.post("/user/register", {
                    phone: phone,
                    code: code,
                    password: password,
                    referrerId: referrerId
                }, function (error, result) {
                    if (error) {
                        setTimeout(() => {
                            callback(null, { code: 200, message: "注册成功" });
                        }, 1000);
                    } else {
                        callback(null, result);
                    }
                });
            },

            resetPassword: function (phone, code, newPassword, callback) {
                this.post("/user/reset/password", {
                    phone: phone,
                    code: code,
                    newPassword: newPassword
                }, function (error, result) {
                    if (error) {
                        setTimeout(() => {
                            callback(null, { code: 200, message: "密码重置成功" });
                        }, 1000);
                    } else {
                        callback(null, result);
                    }
                });
            },

            queryCardKeyInfo: function (phone, callback) {
                var token = this.getUserToken();
                console.log("========== 卡密查询API调用开始 ==========");
                console.log("查询卡密信息，手机号: " + phone);
                console.log("使用Token: " + (token ? token.substring(0, 10) + "..." : "null"));

                if (!token) {
                    console.error("Token为空，无法进行API调用");
                    callback(new Error("Token为空"), null);
                    return;
                }

                console.log("准备发送POST请求到 /user/query/cardkey");
                this.post("/user/query/cardkey", { phone: phone }, function (error, result) {
                    console.log("========== 卡密查询API响应 ==========");
                    if (error) {
                        console.log("卡密查询API调用失败: " + error.message);
                        console.log("使用演示数据作为降级方案");
                        setTimeout(() => {
                            callback(null, {
                                code: 200,
                                data: "2025-12-31 23:59:59",
                                message: "查询成功"
                            });
                        }, 500);
                    } else {
                        console.log("卡密查询API调用成功，响应: " + JSON.stringify(result));
                        if (result && result.message && result.message.includes("Token")) {
                            console.error("后端返回Token相关错误: " + result.message);
                        }
                        callback(null, result);
                    }
                }, token);
            },

            redeemCardKey: function (cardKey, phone, deviceId, callback) {
                var token = this.getUserToken();
                console.log("兑换卡密，使用Token: " + (token ? token.substring(0, 10) + "..." : "null"));
                this.post("/user/redeem/cardkey", {
                    keyCode: cardKey,
                    phone: phone,
                    deviceId: deviceId
                }, function (error, result) {
                    if (error) {
                        console.log("卡密兑换API调用失败，使用演示数据: " + error.message);
                        setTimeout(() => {
                            callback(null, {
                                code: 200,
                                data: "2025-12-31 23:59:59",
                                message: "兑换成功"
                            });
                        }, 1000);
                    } else {
                        callback(null, result);
                    }
                }, token);
            },

            bindDevice: function (phone, deviceId, callback) {
                var token = this.getUserToken();
                console.log("绑定设备，使用Token: " + (token ? token.substring(0, 10) + "..." : "null"));
                this.post("/user/bind/device", {
                    phone: phone,
                    deviceId: deviceId
                }, function (error, result) {
                    if (error) {
                        console.log("设备绑定API调用失败，使用演示数据: " + error.message);
                        setTimeout(() => {
                            callback(null, { code: 200, data: true, message: "绑定成功" });
                        }, 1000);
                    } else {
                        callback(null, result);
                    }
                }, token);
            },

            unbindDevice: function (phone, deviceId, callback) {
                var token = this.getUserToken();
                console.log("解绑设备，使用Token: " + (token ? token.substring(0, 10) + "..." : "null"));
                this.post("/user/unbind/device", {
                    phone: phone,
                    deviceId: deviceId
                }, function (error, result) {
                    if (error) {
                        console.log("设备解绑API调用失败，使用演示数据: " + error.message);
                        setTimeout(() => {
                            callback(null, {
                                code: 200,
                                data: "2025-12-31 23:59:59",
                                message: "解绑成功"
                            });
                        }, 1000);
                    } else {
                        callback(null, result);
                    }
                }, token);
            },

            getLatestAnnouncement: function (callback) {
                this.post("/announcement/latest", {}, function (error, result) {
                    if (error) {
                        setTimeout(function() {
                            callback(null, {
                                code: 200,
                                data: { content: "欢迎使用脚本助手！这是最新公告。" },
                                message: "获取成功"
                            });
                        }, 500);
                    } else {
                        callback(null, result);
                    }
                });
            },

            getRandomPromotion: function (callback) {
                // 使用GET请求获取启用的推广内容，不需要token认证
                this.get("/promotion/enabled", {}, function (error, result) {
                    if (error) {
                        console.log("获取推广内容失败，使用默认内容: " + error.message);
                        // 提供默认的推广内容
                        setTimeout(function() {
                            callback(null, {
                                code: 200,
                                data: {
                                    title: "每日任务",
                                    content: "完成每日推广任务，获取积分奖励！\n\n任务内容：\n1. 保存推广图片到相册\n2. 复制推广信息分享\n3. 发送到朋友圈\n4. 联系客服领取积分奖励\n\n完成以上任务可获得积分奖励！",
                                    imageUrl: null
                                },
                                message: "获取成功"
                            });
                        }, 500);
                    } else {
                        console.log("成功获取推广内容: " + JSON.stringify(result));
                        callback(null, result);
                    }
                }, null); // 明确传递null作为token，表示不需要认证
            }
    };

    // 尝试加载外部网络模块
    try {
        var networkModule = loadModule("./modules/network-module.js");
        if (networkModule) {
            // 合并外部模块和基础功能
            global.NetworkModule = Object.assign({}, baseNetworkFunctions, networkModule);
            global.NetworkModule.init();
            console.log("✓ 外部网络模块加载成功，已合并基础功能");
        } else {
            throw new Error("外部网络模块加载失败");
        }
    } catch (e) {
        console.warn("外部网络模块加载失败，使用基础网络功能: " + e.message);
        global.NetworkModule = baseNetworkFunctions;
    }

    // 6. 加载月光宝盒模块
    console.log("6. 加载月光宝盒模块...");
    try {
        var moonBoxModule = loadModule("./modules/moonbox-module.js");
        if (moonBoxModule) {
            global.MoonBoxModule = moonBoxModule;
            global.MoonBoxModule.init();
            console.log("✓ 月光宝盒模块加载成功");
        } else {
            throw new Error("月光宝盒模块加载失败");
        }
    } catch (e) {
        console.warn("月光宝盒模块加载失败，使用基础功能: " + e.message);
    }

    // 7. 加载每日任务模块
    console.log("7. 加载每日任务模块...");
    try {
        var dailyTaskModule = loadModule("./modules/daily-task-module.js");
        if (dailyTaskModule) {
            global.DailyTaskModule = dailyTaskModule;
            console.log("✓ 每日任务模块加载成功");
        } else {
            throw new Error("每日任务模块加载失败");
        }
    } catch (e) {
        console.warn("每日任务模块加载失败，使用基础功能: " + e.message);
        // 创建基础每日任务模块
        global.DailyTaskModule = {
            createDailyTaskUI: function() {
                try {
                    console.log("调用基础每日任务UI创建功能");

                    // 创建简化的每日任务界面
                    var basicDailyTaskLayoutXml =
                        '<frame bg="#f5ffe0">' +
                        '<vertical padding="16" h="*">' +
                        '<horizontal gravity="center_vertical" h="50">' +
                        '<button id="backFromDailyTaskBtn" text="返回" textSize="14sp" style="Widget.AppCompat.Button.Borderless" w="60dp" h="40dp"/>' +
                        '<text text="每日任务" textSize="18sp" textColor="#333333" gravity="center" layout_weight="1" textStyle="bold"/>' +
                        '<frame w="60dp" visibility="invisible"/>' +
                        '</horizontal>' +

                        '<scroll layout_weight="1">' +
                        '<vertical>' +
                        '<card cardCornerRadius="10dp" cardElevation="2dp" margin="16">' +
                        '<vertical padding="20">' +
                        '<text text="每日任务" textSize="18sp" textColor="#333333" textStyle="bold" gravity="center"/>' +
                        '<text text="完成每日推广任务，获取积分奖励！" textSize="14sp" textColor="#666666" marginTop="16" gravity="center"/>' +
                        '<text text="1. 分享推广内容到朋友圈" textSize="14sp" textColor="#666666" marginTop="8"/>' +
                        '<text text="2. 邀请好友注册使用" textSize="14sp" textColor="#666666" marginTop="4"/>' +
                        '<text text="3. 完成每日签到" textSize="14sp" textColor="#666666" marginTop="4"/>' +
                        '<text text="4. 联系客服领取奖励" textSize="14sp" textColor="#666666" marginTop="4"/>' +
                        '</vertical>' +
                        '</card>' +
                        '</vertical>' +
                        '</scroll>' +

                        '<vertical padding="16">' +
                        '<button id="contactServiceBtn" text="联系客服" textSize="16sp" textColor="#ffffff" bg="#4CAF50" h="48dp" margin="8"/>' +
                        '</vertical>' +
                        '</vertical>' +
                        '</frame>';

                    // 创建UI
                    ui.layout(basicDailyTaskLayoutXml);

                    // 设置当前页面
                    if (typeof global !== 'undefined' && global.UIModule && global.UIModule.setCurrentPage) {
                        global.UIModule.setCurrentPage("dailyTask");
                    }

                    // 绑定返回按钮事件
                    if (ui.backFromDailyTaskBtn) {
                        ui.backFromDailyTaskBtn.on("click", function () {
                            if (typeof global !== 'undefined' && global.UIModule && global.UIModule.showMyPageDirectly) {
                                global.UIModule.showMyPageDirectly();
                            } else if (typeof global !== 'undefined' && global.UIModule && global.UIModule.returnToScriptCenterMyTab) {
                                global.UIModule.returnToScriptCenterMyTab();
                            } else {
                                console.error("UIModule 导航方法不可用");
                                if (typeof toast !== 'undefined') {
                                    toast("返回失败，请重试");
                                }
                            }
                        });
                    }

                    // 绑定联系客服按钮事件
                    if (ui.contactServiceBtn) {
                        ui.contactServiceBtn.on("click", function () {
                            if (typeof toast !== 'undefined') {
                                toast("请联系客服获取更多信息");
                            }
                        });
                    }

                    console.log("基础每日任务UI创建成功");

                } catch (error) {
                    console.error("创建基础每日任务UI失败: " + error.message);
                    if (typeof toast !== 'undefined') {
                        toast("每日任务功能暂时不可用");
                    }
                }
            }
        };
    }

    // 8. 加载代理分销模块
    console.log("8. 加载代理分销模块...");
    try {
        var agentSalesModule = loadModule("./modules/agent-sales-module.js");
        if (agentSalesModule) {
            global.AgentSalesModule = agentSalesModule;
            console.log("✓ 代理分销模块加载成功");
        } else {
            throw new Error("代理分销模块加载失败");
        }
    } catch (e) {
        console.warn("代理分销模块加载失败，使用基础功能: " + e.message);
        // 创建基础代理分销模块
        global.AgentSalesModule = {
            createAgentSalesUI: function() {
                console.log("调用基础代理分销UI创建功能");
                if (typeof toast !== 'undefined') {
                    toast("代理分销功能暂时不可用");
                }
            }
        };
    }

    // 9. 加载我的积分模块
    console.log("9. 加载我的积分模块...");
    try {
        var myPointsModule = loadModule("./modules/my-points-module.js");
        if (myPointsModule) {
            global.MyPointsModule = myPointsModule;
            console.log("✓ 我的积分模块加载成功");
        } else {
            throw new Error("我的积分模块加载失败");
        }
    } catch (e) {
        console.warn("我的积分模块加载失败，使用基础功能: " + e.message);
        // 创建基础我的积分模块
        global.MyPointsModule = {
            createMyPointsUI: function() {
                console.log("调用基础我的积分UI创建功能");
                if (typeof toast !== 'undefined') {
                    toast("我的积分功能暂时不可用");
                }
            }
        };
    }

    // 10. 加载积分兑换模块
    console.log("10. 加载积分兑换模块...");
    try {
        var pointsExchangeModule = loadModule("./modules/points-exchange-module.js");
        if (pointsExchangeModule) {
            global.PointsExchangeModule = pointsExchangeModule;
            console.log("✓ 积分兑换模块加载成功");
        } else {
            throw new Error("积分兑换模块加载失败");
        }
    } catch (e) {
        console.warn("积分兑换模块加载失败，使用基础功能: " + e.message);
        // 创建基础积分兑换模块
        global.PointsExchangeModule = {
            showPointsExchangePage: function() {
                console.log("调用基础积分兑换UI创建功能");
                if (typeof toast !== 'undefined') {
                    toast("积分兑换功能暂时不可用");
                }
            }
        };
    }

    // 11. 加载推广排名模块
    console.log("11. 加载推广排名模块...");
    try {
        var promotionRankModule = loadModule("./modules/promotion-rank-module.js");
        if (promotionRankModule) {
            global.PromotionRankModule = promotionRankModule;
            console.log("✓ 推广排名模块加载成功");
        } else {
            throw new Error("推广排名模块加载失败");
        }
    } catch (e) {
        console.warn("推广排名模块加载失败，使用基础功能: " + e.message);
        // 创建基础推广排名模块
        global.PromotionRankModule = {
            createPromotionRankUI: function() {
                console.log("调用基础推广排名UI创建功能");
                if (typeof toast !== 'undefined') {
                    toast("推广排名功能暂时不可用");
                }
            }
        };
    }

    // 12. 加载关于我们模块
    console.log("12. 加载关于我们模块...");
    try {
        var aboutUsModule = loadModule("./modules/about-us-module.js");
        if (aboutUsModule) {
            global.AboutUsModule = aboutUsModule;
            console.log("✓ 关于我们模块加载成功");
        } else {
            throw new Error("关于我们模块加载失败");
        }
    } catch (e) {
        console.warn("关于我们模块加载失败，使用基础功能: " + e.message);
        // 创建基础关于我们模块
        global.AboutUsModule = {
            createAboutUsUI: function() {
                console.log("调用基础关于我们UI创建功能");
                if (typeof toast !== 'undefined') {
                    toast("关于我们功能暂时不可用");
                }
            },
            createMoreProjectsUI: function() {
                console.log("调用基础更多项目UI创建功能");
                if (typeof toast !== 'undefined') {
                    toast("更多项目功能暂时不可用");
                }
            },
            jumpWealthExchange: function() {
                console.log("调用基础发财交流群功能");
                if (typeof toast !== 'undefined') {
                    toast("发财交流群功能暂时不可用");
                }
            },
            jumpScriptNotificationGroup: function() {
                console.log("调用基础脚本通知群功能");
                if (typeof toast !== 'undefined') {
                    toast("脚本通知群功能暂时不可用");
                }
            }
        };
    }

    // 13. 加载使用教程模块
    console.log("13. 加载使用教程模块...");
    try {
        var tutorialModule = loadModule("./modules/tutorial-module.js");
        if (tutorialModule) {
            global.TutorialModule = tutorialModule;
            console.log("✓ 使用教程模块加载成功");
        } else {
            throw new Error("使用教程模块加载失败");
        }
    } catch (e) {
        console.warn("使用教程模块加载失败，使用基础功能: " + e.message);
        // 创建基础使用教程模块
        global.TutorialModule = {
            createTutorialUI: function() {
                console.log("调用基础使用教程UI创建功能");
                if (typeof toast !== 'undefined') {
                    toast("使用教程功能暂时不可用");
                }
            }
        };
    }

    // 14. 加载账户提现模块
    console.log("14. 加载账户提现模块...");
    try {
        var withdrawalModule = loadModule("./modules/withdrawal-module.js");
        if (withdrawalModule) {
            global.WithdrawalModule = withdrawalModule;
            console.log("✓ 账户提现模块加载成功");
        } else {
            throw new Error("账户提现模块加载失败");
        }
    } catch (e) {
        console.warn("账户提现模块加载失败，使用基础功能: " + e.message);
        // 创建基础账户提现模块
        global.WithdrawalModule = {
            createWithdrawalPage: function() {
                console.log("调用基础账户提现UI创建功能");
                if (typeof toast !== 'undefined') {
                    toast("账户提现功能暂时不可用");
                }
            },
            showWithdrawalPage: function() {
                console.log("调用基础账户提现显示功能");
                if (typeof toast !== 'undefined') {
                    toast("账户提现功能暂时不可用");
                }
            }
        };

        // 创建全局函数，保持兼容性
        global.showWithdrawalPage = function() {
            console.log("调用基础全局提现功能");
            if (typeof toast !== 'undefined') {
                toast("账户提现功能暂时不可用");
            }
        };
    }

    // 创建基础月光宝盒模块（如果加载失败）
    if (!global.MoonBoxModule) {
        global.MoonBoxModule = {
            init: function () {
                console.log("基础月光宝盒模块初始化完成");
            },
            createMoonBoxUI: function() {
                console.log("调用基础月光宝盒UI创建功能");
                try {
                    // 基础月光宝盒功能：优先创建完整配置界面
                    if (typeof global !== 'undefined' && global.MoonBoxModule && global.MoonBoxModule.createMoonBoxUILayout) {
                        console.log("基础月光宝盒：调用外部模块创建完整配置界面");
                        // 调用外部模块的完整UI创建功能
                        global.MoonBoxModule.createMoonBoxUI();
                    } else {
                        console.log("基础月光宝盒：使用降级方案");
                        // 降级到配置对话框
                        this.showBasicConfigDialog();
                    }
                } catch (e) {
                    console.error("基础月光宝盒UI创建失败: " + e.message);
                    // 最终降级
                    this.showBasicConfigDialog();
                }
            },

            // 基础配置对话框
            showBasicConfigDialog: function() {
                try {
                    // 显示简单的配置对话框
                    if (typeof dialogs !== 'undefined') {
                        var options = ["阅读任务", "点赞任务", "评论任务", "关注任务"];
                        var selectedIndex = dialogs.select("选择月光宝盒功能", options);

                        if (selectedIndex >= 0) {
                            var functionNames = ["阅读", "点赞", "评论", "关注"];
                            var selectedFunction = functionNames[selectedIndex];

                            // 询问数量
                            var quantity = dialogs.rawInput("请输入目标数量", "10");
                            if (quantity && !isNaN(quantity)) {
                                // 显示开始执行的提示
                                if (typeof global !== 'undefined' && global.UIModule) {
                                    global.UIModule.showToast("开始执行" + selectedFunction + "任务，目标数量: " + quantity);
                                } else {
                                    toast("开始执行" + selectedFunction + "任务，目标数量: " + quantity);
                                }

                                // 模拟任务执行
                                setTimeout(function() {
                                    if (typeof global !== 'undefined' && global.UIModule) {
                                        global.UIModule.showToast(selectedFunction + "任务执行完成！");
                                    } else {
                                        toast(selectedFunction + "任务执行完成！");
                                    }
                                }, 3000);
                            }
                        }
                    } else {
                        // 如果没有dialogs，显示基础提示
                        if (typeof global !== 'undefined' && global.UIModule) {
                            global.UIModule.showToast("月光宝盒功能已启动，正在执行默认任务...");
                        } else {
                            toast("月光宝盒功能已启动，正在执行默认任务...");
                        }
                    }
                } catch (e) {
                    console.error("基础配置对话框显示失败: " + e.message);
                    if (typeof global !== 'undefined' && global.UIModule) {
                        global.UIModule.showToast("月光宝盒功能暂时不可用");
                    } else if (typeof toast !== 'undefined') {
                        toast("月光宝盒功能暂时不可用");
                    }
                }
            },
            start: function(config, callback) {
                console.log("基础月光宝盒开始执行");
                if (callback) callback(null, { message: "基础功能执行完成" });
            },
            stop: function() {
                console.log("基础月光宝盒停止执行");
            },
            isRunning: function() {
                return false;
            }
        };
        global.MoonBoxModule.init();
    }

    console.log("所有模块加载完成");
}

// UI模块 - 完全按照原始版本还原
var UIModule = {
    /**
     * 设置当前页面
     */
    setCurrentPage: function (page) {
        var previousPage = currentPage;
        currentPage = page;

        // 页面切换时重置退出确认状态
        if (previousPage !== page) {
            this.resetExitConfirmation();
        }
    },

    /**
     * 获取当前页面
     */
    getCurrentPage: function () {
        return currentPage;
    },

    /**
     * 初始化全局返回键处理
     */
    initGlobalBackKeyHandler: function() {
        var that = this;

        // 全局物理返回键处理
        ui.emitter.on("back_pressed", function(e) {
            var currentPageState = that.getCurrentPage();
            console.log("全局返回键处理 - 当前页面: " + currentPageState);

            // 主页面退出确认逻辑
            if (currentPageState === "scriptCenter" || currentPageState === "my") {
                e.consumed = true; // 阻止默认返回行为
                that.handleMainPageExit();
            }
            // 其他页面的返回键处理由各自模块处理
        });

        console.log("全局返回键处理器已初始化");
    },

    /**
     * 处理主页面退出确认
     */
    handleMainPageExit: function() {
        var currentTime = Date.now();

        // 检查是否在2秒内连续按返回键
        if (currentTime - exitConfirmationTime < 2000) {
            // 第二次按返回键，退出应用
            console.log("用户确认退出应用");
            this.showToast("正在退出应用...");

            // 清理退出确认状态
            this.clearExitConfirmation();

            // 退出应用
            setTimeout(function() {
                if (typeof exit !== 'undefined') {
                    exit();
                } else if (typeof engines !== 'undefined' && engines.myEngine) {
                    engines.myEngine().forceStop();
                } else {
                    // 最终降级方案
                    back();
                }
            }, 500);
        } else {
            // 第一次按返回键，显示提示
            console.log("显示退出确认提示");
            this.showToast("再按一次返回退出软件");

            // 记录按键时间
            exitConfirmationTime = currentTime;

            // 清除之前的超时
            if (exitConfirmationTimeout) {
                clearTimeout(exitConfirmationTimeout);
            }

            // 设置2秒后清除退出确认状态
            exitConfirmationTimeout = setTimeout(function() {
                exitConfirmationTime = 0;
                console.log("退出确认状态已重置");
            }, 2000);
        }
    },

    /**
     * 清除退出确认状态
     */
    clearExitConfirmation: function() {
        exitConfirmationTime = 0;
        if (exitConfirmationTimeout) {
            clearTimeout(exitConfirmationTimeout);
            exitConfirmationTimeout = null;
        }
    },

    /**
     * 重置退出确认状态（页面切换时调用）
     */
    resetExitConfirmation: function() {
        this.clearExitConfirmation();
        console.log("页面切换，退出确认状态已重置");
    },

    /**
     * 创建主界面 - 完全按照原始版本
     */
    createMainUI: function () {
        try {
            // 创建UI布局 - 精确还原原始版本
            var layoutXml =
                '<frame>' +
                '<vertical padding="16">' +
                '<text id="appTitle" text="脚本助手" textSize="24sp" textColor="#3F51B5" gravity="center" margin="0 10"/>' +

                '<card margin="10" cardCornerRadius="8dp" cardElevation="5dp">' +
                '<img id="bannerImage" src="@drawable/ic_launcher" scaleType="centerCrop" h="150"/>' +
                '</card>' +

                '<card margin="10" cardCornerRadius="8dp" cardElevation="3dp">' +
                '<vertical padding="16">' +
                '<horizontal gravity="center_vertical">' +
                '<text text="无障碍" textSize="16sp" layout_weight="1"/>' +
                '<text text="(点击、滑动、长按等)" textSize="12sp" textColor="#888888" layout_weight="2"/>' +
                '<Switch id="accessibilitySwitch" checked="false"/>' +
                '</horizontal>' +

                '<horizontal gravity="center_vertical" marginTop="20">' +
                '<text text="悬浮框" textSize="16sp" layout_weight="1"/>' +
                '<text text="(增加脚本存活率)" textSize="12sp" textColor="#888888" layout_weight="2"/>' +
                '<Switch id="floatingWindowSwitch" checked="false"/>' +
                '</horizontal>' +
                '</vertical>' +
                '</card>' +

                '<horizontal margin="10 20">' +
                '<button id="loginBtn" text="登录" style="Widget.AppCompat.Button.Colored" layout_weight="1" margin="5"/>' +
                '<button id="registerBtn" text="注册" style="Widget.AppCompat.Button.Colored" layout_weight="1" margin="5"/>' +
                '</horizontal>' +
                '</vertical>' +
                '</frame>';

            // 设置UI布局
            ui.layout(layoutXml);

            // 绑定事件
            this.bindEvents();

            // 初始化状态
            this.updateUIState();

            // 初始化全局返回键处理
            this.initGlobalBackKeyHandler();

            // 更新当前页面状态
            this.setCurrentPage("main");
            global.LogModule.log("主界面创建成功");
        } catch (e) {
            global.LogModule.log("创建主界面失败: " + e.message, "ERROR");
            console.error("创建主界面失败: " + e.message);
            console.error(e.stack);
        }
    },

    /**
     * 绑定主界面事件 - 完全按照原始版本
     */
    bindEvents: function () {
        var that = this;

        // 无障碍服务开关
        ui.accessibilitySwitch.on("check", function (checked) {
            if (checked && !that.checkAccessibilityPermission()) {
                // 请求无障碍服务权限
                that.requestAccessibilityPermission();
            }
            if (global.StorageModule) {
                global.StorageModule.set("enableAccessibility", checked);
            }
        });

        // 悬浮窗开关
        ui.floatingWindowSwitch.on("check", function (checked) {
            if (checked && !that.checkFloatingWindowPermission()) {
                // 请求悬浮窗权限
                that.requestFloatingWindowPermission();
            }
            if (global.StorageModule) {
                global.StorageModule.set("enableFloatWindow", checked);
            }
        });

        // 登录按钮
        ui.loginBtn.on("click", function () {
            // 检查无障碍服务状态
            var accessibilityEnabled = that.isAccessibilityServiceEnabled();

            // 检查悬浮窗权限
            var floatingWindowEnabled = that.isFloatingWindowPermissionGranted();

            // 必须开启无障碍服务才能登录
            if (!accessibilityEnabled) {
                that.showAccessibilitySettingsDialog();
                return;
            }

            // 悬浮窗权限可选，但给出提示
            if (!floatingWindowEnabled) {
                that.showToast("建议开启悬浮窗权限以获得完整功能");
            }

            // 跳转到登录页面
            that.createLoginUI();
        });

        // 注册按钮
        ui.registerBtn.on("click", function () {
            // 检查必要权限
            if (!that.checkAllRequiredPermissions()) {
                that.showToast("请先开启无障碍服务和悬浮窗权限");
                return;
            }

            // 跳转到注册页面
            that.createRegisterUI();
        });
    },

    /**
     * 创建登录界面 - 完全按照原始版本
     */
    createLoginUI: function () {
        try {
            // 创建登录UI布局 - 精确还原原始版本
            var loginLayoutXml =
                '<frame bg="#e8f5e9">' +
                '<vertical padding="16" gravity="center_horizontal">' +
                '<text id="loginTitle" text="登录" textSize="24sp" textColor="#333333" gravity="center" margin="0 20 0 40"/>' +

                '<text text="手机号" textSize="16sp" textColor="#666666" marginTop="20"/>' +
                '<input id="phoneInput" hint="请输入手机号" inputType="phone" textSize="16sp" marginTop="4" padding="8" bg="#ffffff" h="50"/>' +

                '<text text="密码" textSize="16sp" textColor="#666666" marginTop="20"/>' +
                '<horizontal>' +
                '<input id="passwordInput" hint="请输入密码" inputType="textPassword" textSize="16sp" marginTop="4" padding="8" bg="#ffffff" h="50" layout_weight="1"/>' +
                '<text id="forgotPassword" text="忘记密码" textSize="14sp" textColor="#FF5722" gravity="center" marginLeft="10"/>' +
                '</horizontal>' +

                '<button id="loginSubmitBtn" text="登录" textSize="16sp" color="#ffffff" bg="#CD853F" marginTop="30" h="50"/>' +

                '<horizontal marginTop="20" gravity="center">' +
                '<text text="没有账号? " textSize="14sp" textColor="#666666"/>' +
                '<text id="goToRegister" text="立即注册" textSize="14sp" textColor="#4CAF50" />' +
                '</horizontal>' +

                '<button id="backToMainBtn" text="返回" textSize="14sp" style="Widget.AppCompat.Button.Borderless" marginTop="20"/>' +
                '</vertical>' +
                '</frame>';

            // 设置UI布局
            ui.layout(loginLayoutXml);

            // 绑定登录页面事件
            this.bindLoginEvents();

            // 更新当前页面状态
            this.setCurrentPage("login");
            global.LogModule.log("登录界面创建成功");
        } catch (e) {
            global.LogModule.log("创建登录界面失败: " + e.message, "ERROR");
            console.error("创建登录界面失败: " + e.message);
            console.error(e.stack);
        }
    },

    /**
     * 绑定登录页面事件 - 完全按照原始版本
     */
    bindLoginEvents: function () {
        var that = this;

        // 登录按钮
        ui.loginSubmitBtn.on("click", function () {
            try {
                console.log("登录按钮被点击");
                global.LogModule.log("用户点击登录按钮", "INFO");

                var phone = ui.phoneInput.text();
                var password = ui.passwordInput.text();

                console.log("获取输入信息 - 手机号: " + phone + ", 密码长度: " + password.length);

                // 再次检查无障碍服务状态
                var accessibilityEnabled = that.isAccessibilityServiceEnabled();
                console.log("无障碍服务状态: " + accessibilityEnabled);

                // 必须开启无障碍服务才能登录
                if (!accessibilityEnabled) {
                    console.log("无障碍服务未开启，显示设置对话框");
                    that.showAccessibilitySettingsDialog();
                    return;
                }

                // 验证手机号格式
                if (!that.isValidPhoneNumber(phone)) {
                    console.log("手机号格式验证失败: " + phone);
                    that.showToast("请输入正确的手机号");
                    return;
                }

                // 验证密码
                if (!that.isValidPassword(password)) {
                    console.log("密码验证失败，长度: " + password.length);
                    that.showToast("密码长度不能少于6位");
                    return;
                }

                // 显示加载提示
                that.showToast("登录中...");
                console.log("开始调用登录接口");

                // 调用登录接口
                try {
                    global.NetworkModule.login(phone, password, function (error, userData) {
                        try {
                            console.log("登录接口回调执行");
                            if (error) {
                                // 登录失败
                                console.log("登录失败: " + error.message);
                                that.showToast("登录失败: " + error.message);
                                global.LogModule.log("登录失败: " + error.message, "ERROR");
                            } else {
                                // 登录成功
                                console.log("登录成功，用户数据: " + JSON.stringify(userData));
                                that.showToast("登录成功");
                                global.LogModule.log("登录成功: 用户ID=" + userData.id);
                                global.LogModule.log("登录成功: 用户token=" + userData.token);
                                // 保存登录状态
                                isLoggedIn = true;
                                currentUser = userData;

                                // 保存到StorageModule
                                console.log("开始保存用户信息到StorageModule...");
                                if (global.StorageModule) {
                                    global.StorageModule.set("isLoggedIn", true);
                                    global.StorageModule.set("currentUser", userData);
                                    global.StorageModule.set("userToken", userData.token);
                                    global.StorageModule.set("userPhone", userData.phone);  // 添加手机号保存
                                    global.StorageModule.set("userId", userData.id);        // 添加用户ID保存
                                    console.log("Token已保存到StorageModule: " + userData.token.substring(0, 10) + "...");
                                    console.log("手机号已保存到StorageModule: " + userData.phone);

                                    // 验证保存是否成功
                                    var savedToken = global.StorageModule.get("userToken");
                                    var savedPhone = global.StorageModule.get("userPhone");
                                    if (savedToken === userData.token) {
                                        console.log("StorageModule Token保存验证成功");
                                    } else {
                                        console.error("StorageModule Token保存验证失败: " + savedToken);
                                    }
                                    if (savedPhone === userData.phone) {
                                        console.log("StorageModule 手机号保存验证成功");
                                    } else {
                                        console.error("StorageModule 手机号保存验证失败: " + savedPhone);
                                    }
                                } else {
                                    console.error("StorageModule不可用，无法保存Token");
                                }

                                // 同时保存到ConfigModule以确保兼容性
                                console.log("开始保存用户信息到ConfigModule...");
                                if (global.ConfigModule) {
                                    global.ConfigModule.set("userPhone", userData.phone);
                                    global.ConfigModule.set("userId", userData.id);
                                    global.ConfigModule.set("userToken", userData.token);
                                    global.ConfigModule.set("referrerId", userData.referrerId);
                                    global.ConfigModule.set("invitationCode", userData.invitationCode);
                                    console.log("Token已保存到ConfigModule: " + userData.token.substring(0, 10) + "...");

                                    // 验证保存是否成功
                                    var savedToken = global.ConfigModule.get("userToken");
                                    if (savedToken === userData.token) {
                                        console.log("ConfigModule Token保存验证成功");
                                    } else {
                                        console.error("ConfigModule Token保存验证失败: " + savedToken);
                                    }
                                } else {
                                    console.error("ConfigModule不可用，无法保存Token");
                                }

                                // 跳转到脚本中心页面，并显示公告
                                console.log("准备跳转到脚本中心");
                                setTimeout(function () {
                                    try {
                                        that.createScriptCenterUI(true);
                                    } catch (uiError) {
                                        console.error("创建脚本中心界面失败: " + uiError.message);
                                        that.showToast("界面跳转失败，请重试");
                                    }
                                }, 500);
                            }
                        } catch (callbackError) {
                            console.error("登录回调处理失败: " + callbackError.message);
                            console.error(callbackError.stack);
                            that.showToast("登录处理失败: " + callbackError.message);
                        }
                    });
                } catch (networkError) {
                    console.error("网络请求失败: " + networkError.message);
                    console.error(networkError.stack);
                    that.showToast("网络请求失败: " + networkError.message);
                }
            } catch (e) {
                console.error("登录按钮事件处理失败: " + e.message);
                console.error(e.stack);
                that.showToast("登录失败: " + e.message);
                global.LogModule.log("登录按钮事件处理失败: " + e.message, "ERROR");
            }
        });

        // 忘记密码
        ui.forgotPassword.on("click", function () {
            try {
                global.LogModule.log("跳转到重置密码页面");
                that.createResetPasswordUI();
            } catch (e) {
                console.error("跳转重置密码页面失败: " + e.message);
                that.showToast("页面跳转失败: " + e.message);
            }
        });

        // 去注册
        ui.goToRegister.on("click", function () {
            global.LogModule.log("跳转到注册页面");
            that.createRegisterUI();
        });

        // 返回主页
        ui.backToMainBtn.on("click", function () {
            global.LogModule.log("返回主页");
            that.createMainUI();
        });
    },

    /**
     * 创建重置密码界面 - 完全按照原始版本
     */
    createResetPasswordUI: function () {
        try {
            // 创建重置密码UI布局 - 精确还原原始版本
            var resetPasswordLayoutXml =
                '<frame bg="#e8f5e9">' +
                '<vertical padding="16" gravity="center_horizontal">' +
                '<text id="resetTitle" text="重置密码" textSize="24sp" textColor="#333333" gravity="center" margin="0 20 0 40"/>' +

                '<text text="手机号" textSize="16sp" textColor="#666666" marginTop="20"/>' +
                '<input id="resetPhoneInput" hint="请输入手机号" inputType="phone" textSize="16sp" marginTop="4" padding="8" bg="#ffffff" h="50"/>' +

                '<horizontal marginTop="10">' +
                '<input id="resetCodeInput" hint="请输入验证码" inputType="number" textSize="16sp" padding="8" bg="#ffffff" h="50" layout_weight="1"/>' +
                '<button id="getResetCodeBtn" text="获取验证码" textSize="14sp" style="Widget.AppCompat.Button.Colored" marginLeft="10"/>' +
                '</horizontal>' +

                '<text text="新密码" textSize="16sp" textColor="#666666" marginTop="20"/>' +
                '<input id="newPasswordInput" hint="请输入新密码" inputType="textPassword" textSize="16sp" marginTop="4" padding="8" bg="#ffffff" h="50"/>' +

                '<button id="resetSubmitBtn" text="立即重置" textSize="16sp" color="#ffffff" bg="#CD853F" marginTop="30" h="50"/>' +

                '<button id="backToLoginBtn" text="返回登录" textSize="14sp" style="Widget.AppCompat.Button.Borderless" marginTop="20"/>' +
                '</vertical>' +
                '</frame>';

            // 设置UI布局
            ui.layout(resetPasswordLayoutXml);

            // 绑定重置密码页面事件
            this.bindResetPasswordEvents();

            // 更新当前页面状态
            this.setCurrentPage("resetPassword");
            global.LogModule.log("重置密码界面创建成功");
        } catch (e) {
            global.LogModule.log("创建重置密码界面失败: " + e.message, "ERROR");
            console.error("创建重置密码界面失败: " + e.message);
            console.error(e.stack);
        }
    },

    /**
     * 绑定重置密码页面事件
     */
    bindResetPasswordEvents: function () {
        var that = this;

        // 获取验证码按钮
        ui.getResetCodeBtn.on("click", function () {
            try {
                var phone = ui.resetPhoneInput.text();

                // 验证手机号格式
                if (!that.isValidPhoneNumber(phone)) {
                    that.showToast("请输入正确的手机号");
                    return;
                }

                // 禁用按钮并开始倒计时
                that.startCodeButtonCountdown(ui.getResetCodeBtn);

                // 调用发送重置密码验证码接口 - 按照原版API
                global.NetworkModule.sendResetPasswordCode(phone, function (error, result) {
                    if (error) {
                        that.showToast("获取验证码失败: " + error.message);
                        global.LogModule.log("获取重置密码验证码失败: " + error.message, "ERROR");
                    } else {
                        if (result && result.code === 200) {
                            that.showToast("验证码已发送，请注意查收");
                            global.LogModule.log("重置密码验证码已发送: " + phone);
                        } else {
                            var errorMsg = result && result.message ? result.message : "获取验证码失败";
                            that.showToast("获取验证码失败: " + errorMsg);
                            global.LogModule.log("获取重置密码验证码失败: " + errorMsg, "ERROR");
                        }
                    }
                });

                global.LogModule.log("发送重置密码验证码到: " + phone);
            } catch (e) {
                console.error("获取验证码失败: " + e.message);
                that.showToast("获取验证码失败: " + e.message);
            }
        });

        // 重置密码按钮
        ui.resetSubmitBtn.on("click", function () {
            try {
                var phone = ui.resetPhoneInput.text();
                var code = ui.resetCodeInput.text();
                var newPassword = ui.newPasswordInput.text();

                // 验证输入
                if (!that.isValidPhoneNumber(phone)) {
                    that.showToast("请输入正确的手机号");
                    return;
                }

                if (!code || code.length < 4) {
                    that.showToast("请输入正确的验证码");
                    return;
                }

                if (!that.isValidPassword(newPassword)) {
                    that.showToast("新密码长度不能少于6位");
                    return;
                }

                that.showToast("正在重置密码...");

                // 调用重置密码接口 - 按照原版逻辑
                global.NetworkModule.resetPassword(phone, code, newPassword, function (error, result) {
                    if (error) {
                        that.showToast("重置密码失败: " + error.message);
                        global.LogModule.log("重置密码失败: " + error.message, "ERROR");
                    } else {
                        if (result && result.code === 200) {
                            that.showToast("密码重置成功，请重新登录");
                            global.LogModule.log("密码重置成功: " + phone);

                            // 返回登录页面 - 按照原版延迟
                            setTimeout(function () {
                                that.createLoginUI();
                            }, 1500);
                        } else {
                            var errorMsg = result && result.message ? result.message : "重置密码失败";
                            that.showToast("重置密码失败: " + errorMsg);
                            global.LogModule.log("重置密码失败: " + errorMsg, "ERROR");
                        }
                    }
                });
            } catch (e) {
                console.error("重置密码失败: " + e.message);
                that.showToast("重置密码失败: " + e.message);
            }
        });

        // 返回登录
        ui.backToLoginBtn.on("click", function () {
            try {
                global.LogModule.log("从重置密码页面返回登录");
                that.createLoginUI();
            } catch (e) {
                console.error("返回登录失败: " + e.message);
                that.showToast("页面跳转失败: " + e.message);
            }
        });
    },

    /**
     * 验证码按钮倒计时
     */
    startCodeButtonCountdown: function (button) {
        var countdown = 60;
        button.setEnabled(false);

        var timer = setInterval(function () {
            countdown--;
            button.setText("重新发送(" + countdown + ")");

            if (countdown <= 0) {
                clearInterval(timer);
                button.setText("获取验证码");
                button.setEnabled(true);
            }
        }, 1000);
    },

    /**
     * 创建注册界面 - 完全按照原始版本
     */
    createRegisterUI: function () {
        try {
            // 创建注册UI布局 - 精确还原原始版本
            var registerLayoutXml =
                '<frame bg="#e8f5e9">' +
                '<vertical padding="16" gravity="center_horizontal">' +
                '<text id="registerTitle" text="注册" textSize="24sp" textColor="#333333" gravity="center" margin="0 20 0 40"/>' +

                '<text text="手机号" textSize="16sp" textColor="#666666" marginTop="20"/>' +
                '<input id="registerPhoneInput" hint="请输入手机号" inputType="phone" textSize="16sp" marginTop="4" padding="8" bg="#ffffff" h="50"/>' +

                '<horizontal marginTop="10">' +
                '<input id="registerCodeInput" hint="请输入验证码" inputType="number" textSize="16sp" padding="8" bg="#ffffff" h="50" layout_weight="1"/>' +
                '<button id="getRegisterCodeBtn" text="获取验证码" textSize="14sp" style="Widget.AppCompat.Button.Colored" marginLeft="10"/>' +
                '</horizontal>' +

                '<text text="密码" textSize="16sp" textColor="#666666" marginTop="20"/>' +
                '<input id="registerPasswordInput" hint="请输入密码" inputType="textPassword" textSize="16sp" marginTop="4" padding="8" bg="#ffffff" h="50"/>' +

                '<text text="推荐人ID" textSize="16sp" textColor="#666666" marginTop="20"/>' +
                '<input id="referrerIdInput" hint="请输入推荐人ID" textSize="16sp" marginTop="4" padding="8" bg="#ffffff" h="50"/>' +

                '<button id="registerSubmitBtn" text="注册" textSize="16sp" color="#ffffff" bg="#CD853F" marginTop="30" h="50"/>' +

                '<button id="backToLoginFromRegisterBtn" text="返回登录" textSize="14sp" style="Widget.AppCompat.Button.Borderless" marginTop="20"/>' +
                '</vertical>' +
                '</frame>';

            // 设置UI布局
            ui.layout(registerLayoutXml);

            // 绑定注册页面事件
            this.bindRegisterEvents();

            // 更新当前页面状态
            this.setCurrentPage("register");
            global.LogModule.log("注册界面创建成功");
        } catch (e) {
            global.LogModule.log("创建注册界面失败: " + e.message, "ERROR");
            console.error("创建注册界面失败: " + e.message);
            console.error(e.stack);
        }
    },

    /**
     * 绑定注册页面事件
     */
    bindRegisterEvents: function () {
        var that = this;

        // 获取验证码按钮
        ui.getRegisterCodeBtn.on("click", function () {
            try {
                var phone = ui.registerPhoneInput.text();

                // 验证手机号格式
                if (!that.isValidPhoneNumber(phone)) {
                    that.showToast("请输入正确的手机号");
                    return;
                }

                // 禁用按钮并开始倒计时
                that.startCodeButtonCountdown(ui.getRegisterCodeBtn);

                // 调用发送注册验证码接口 - 按照原版API
                global.NetworkModule.sendRegisterCode(phone, function (error, result) {
                    if (error) {
                        that.showToast("获取验证码失败: " + error.message);
                        global.LogModule.log("获取注册验证码失败: " + error.message, "ERROR");
                    } else {
                        if (result && result.code === 200) {
                            that.showToast("验证码已发送，请注意查收");
                            global.LogModule.log("注册验证码已发送: " + phone);
                        } else {
                            var errorMsg = result && result.message ? result.message : "获取验证码失败";
                            that.showToast("获取验证码失败: " + errorMsg);
                            global.LogModule.log("获取注册验证码失败: " + errorMsg, "ERROR");
                        }
                    }
                });

                global.LogModule.log("发送注册验证码到: " + phone);
            } catch (e) {
                console.error("获取验证码失败: " + e.message);
                that.showToast("获取验证码失败: " + e.message);
            }
        });

        // 注册按钮
        ui.registerSubmitBtn.on("click", function () {
            try {
                console.log("注册按钮被点击");
                global.LogModule.log("用户点击注册按钮", "INFO");

                var phone = ui.registerPhoneInput.text();
                var code = ui.registerCodeInput.text();
                var password = ui.registerPasswordInput.text();
                var referrerId = ui.referrerIdInput.text();

                console.log("获取注册信息 - 手机号: " + phone + ", 验证码: " + code + ", 推荐人: " + referrerId);

                // 验证手机号格式
                if (!that.isValidPhoneNumber(phone)) {
                    that.showToast("请输入正确的手机号");
                    return;
                }

                // 验证验证码
                if (!code || code.length < 4) {
                    that.showToast("请输入正确的验证码");
                    return;
                }

                // 验证密码
                if (!that.isValidPassword(password)) {
                    that.showToast("密码长度不能少于6位");
                    return;
                }

                // 验证推荐人ID - 按照原版逻辑
                if (!referrerId) {
                    that.showToast("请输入推荐人ID");
                    return;
                }

                // 验证推荐人ID是否存在 - 完全按照原版逻辑
                global.NetworkModule.checkReferrerId(referrerId, function (error, result) {
                    if (error) {
                        that.showToast("验证推荐人ID失败: " + error.message);
                        global.LogModule.log("验证推荐人ID失败: " + error.message, "ERROR");
                    } else if (result && result.code === 200 && result.data === true) {
                        // 显示加载提示
                        that.showToast("正在注册...");

                        // 获取设备ID
                        var deviceId = device.getAndroidId();

                        // 调用注册接口
                        global.NetworkModule.register(phone, code, password, referrerId, function (error, result) {
                            if (error) {
                                that.showToast("注册失败: " + error.message);
                                global.LogModule.log("注册失败: " + error.message, "ERROR");
                            } else {
                                // 检查响应状态码
                                if (result && result.code === 200) {
                                    that.showToast("注册成功，请登录");
                                    global.LogModule.log("注册成功: " + phone);

                                    // 返回登录页面
                                    setTimeout(function () {
                                        that.createLoginUI();
                                    }, 1500);
                                } else {
                                    // 处理业务逻辑错误
                                    var errorMsg = result && result.message ? result.message : "注册失败";
                                    that.showToast("注册失败: " + errorMsg);
                                    global.LogModule.log("注册失败: " + errorMsg, "ERROR");
                                }
                            }
                        });
                    } else {
                        var errorMsg = result && result.message ? result.message : "推荐人ID不存在";
                        that.showToast("推荐人ID不存在，请重新输入");
                        global.LogModule.log("推荐人ID不存在: " + referrerId + ", " + errorMsg, "ERROR");
                    }
                });

            } catch (e) {
                console.error("注册按钮事件处理失败: " + e.message);
                console.error(e.stack);
                that.showToast("注册失败: " + e.message);
                global.LogModule.log("注册按钮事件处理失败: " + e.message, "ERROR");
            }
        });

        // 返回登录
        ui.backToLoginFromRegisterBtn.on("click", function () {
            try {
                global.LogModule.log("从注册页面返回登录");
                that.createLoginUI();
            } catch (e) {
                console.error("返回登录失败: " + e.message);
                that.showToast("页面跳转失败: " + e.message);
            }
        });
    },

    /**
     * 创建脚本中心界面 - 完全按照原始main.js还原
     */
    createScriptCenterUI: function (showAnnouncement) {
        try {
            // 创建脚本中心UI布局 - 完全按照原版
            var scriptCenterLayoutXml =
                '<frame>' +
                '<vertical bg="#F5F5F5" padding="8">' + // 浅灰色背景，增加内边距
                '<frame bg="#F5F5F5" h="50">' +
                '<horizontal gravity="center_vertical" h="*" w="*">' +
                '<frame w="60dp" visibility="invisible"/>' + // 左侧占位符
                '<text text="脚本助手" textSize="18sp" textColor="#333333" gravity="center" layout_weight="1" textStyle="bold"/>' +
                '<frame w="60dp" visibility="invisible"/>' + // 右侧占位符
                '</horizontal>' +
                '</frame>' +

                '<frame id="scriptTabContent" layout_weight="1">' +
                '<scroll>' +
                '<vertical>' +
                // VIP脚本专区标题
                '<card cardCornerRadius="8dp" cardElevation="2dp" margin="5 8 5 5">' +
                '<text text="VIP脚本专区" textSize="16sp" textColor="#FFFFFF" gravity="center" bg="#009688" h="40" w="*"/>' +
                '</card>' +

                // VIP脚本专区 - 第1排
                '<horizontal margin="0 5" gravity="center" w="*">' +
                '<card w="*" h="50" cardCornerRadius="8dp" cardElevation="2dp" layout_weight="1" margin="5 0">' +
                '<vertical gravity="center" bg="#009688" h="*" w="*" clickable="true" id="moon_box">' +
                '<text id="moon_box_text" text="月光宝盒" textSize="16sp" textColor="#FFFFFF" gravity="center"/>' +
                '</vertical>' +
                '</card>' +
                '<card w="*" h="50" cardCornerRadius="8dp" cardElevation="2dp" layout_weight="1" margin="5 0">' +
                '<vertical gravity="center" bg="#009688" h="*" w="*" clickable="true" id="vip_wx">' +
                '<text id="vip_wx_text" text="微信" textSize="16sp" textColor="#FFFFFF" gravity="center"/>' +
                '</vertical>' +
                '</card>' +
                '</horizontal>' +

                // VIP脚本专区 - 第2排
                '<horizontal margin="0 5" gravity="center" w="*">' +
                '<card w="*" h="50" cardCornerRadius="8dp" cardElevation="2dp" layout_weight="1" margin="5 0">' +
                '<vertical gravity="center" bg="#009688" h="*" w="*" clickable="true" id="vip_qq">' +
                '<text id="vip_qq_text" text="QQ" textSize="16sp" textColor="#FFFFFF" gravity="center"/>' +
                '</vertical>' +
                '</card>' +
                '<card w="*" h="50" cardCornerRadius="8dp" cardElevation="2dp" layout_weight="1" margin="5 0">' +
                '<vertical gravity="center" bg="#009688" h="*" w="*" clickable="true" id="vip_wb">' +
                '<text id="vip_wb_text" text="微博" textSize="16sp" textColor="#FFFFFF" gravity="center"/>' +
                '</vertical>' +
                '</card>' +
                '</horizontal>' +

                // VIP脚本专区 - 第3排
                '<horizontal margin="0 5" gravity="center" w="*">' +
                '<card w="*" h="50" cardCornerRadius="8dp" cardElevation="2dp" layout_weight="1" margin="5 0">' +
                '<vertical gravity="center" bg="#009688" h="*" w="*" clickable="true" id="vip_script1">' +
                '<text id="vip_script1_text" text="VIP脚本1" textSize="16sp" textColor="#FFFFFF" gravity="center"/>' +
                '</vertical>' +
                '</card>' +
                '<card w="*" h="50" cardCornerRadius="8dp" cardElevation="2dp" layout_weight="1" margin="5 0">' +
                '<vertical gravity="center" bg="#009688" h="*" w="*" clickable="true" id="vip_script2">' +
                '<text id="vip_script2_text" text="VIP脚本2" textSize="16sp" textColor="#FFFFFF" gravity="center"/>' +
                '</vertical>' +
                '</card>' +
                '</horizontal>' +

                // VIP脚本专区 - 第4排
                '<horizontal margin="0 5" gravity="center" w="*">' +
                '<card w="*" h="50" cardCornerRadius="8dp" cardElevation="2dp" layout_weight="1" margin="5 0">' +
                '<vertical gravity="center" bg="#009688" h="*" w="*" clickable="true" id="vip_script3">' +
                '<text id="vip_script3_text" text="VIP脚本3" textSize="16sp" textColor="#FFFFFF" gravity="center"/>' +
                '</vertical>' +
                '</card>' +
                '<card w="*" h="50" cardCornerRadius="8dp" cardElevation="2dp" layout_weight="1" margin="5 0">' +
                '<vertical gravity="center" bg="#009688" h="*" w="*" clickable="true" id="vip_script4">' +
                '<text id="vip_script4_text" text="VIP脚本4" textSize="16sp" textColor="#FFFFFF" gravity="center"/>' +
                '</vertical>' +
                '</card>' +
                '</horizontal>' +

                // 免费脚本专区标题
                '<card cardCornerRadius="8dp" cardElevation="2dp" margin="5 12 5 5">' +
                '<text text="免费脚本" textSize="16sp" textColor="#FFFFFF" gravity="center" bg="#009688" h="40" w="*"/>' +
                '</card>' +

                // 免费脚本专区 - 第1排
                '<horizontal margin="0 5" gravity="center" w="*">' +
                '<card w="*" h="50" cardCornerRadius="8dp" cardElevation="2dp" layout_weight="1" margin="5 0">' +
                '<vertical gravity="center" bg="#009688" h="*" w="*" clickable="true" id="free_wx_read">' +
                '<text id="free_wx_read" text="微信阅读" textSize="16sp" textColor="#FFFFFF" gravity="center"/>' +
                '</vertical>' +
                '</card>' +
                '<card w="*" h="50" cardCornerRadius="8dp" cardElevation="2dp" layout_weight="1" margin="5 0">' +
                '<vertical gravity="center" bg="#009688" h="*" w="*" clickable="true" id="free_wx">' +
                '<text id="free_wx_text" text="微信" textSize="16sp" textColor="#FFFFFF" gravity="center"/>' +
                '</vertical>' +
                '</card>' +
                '</horizontal>' +

                // 免费脚本专区 - 第2排
                '<horizontal margin="0 5" gravity="center" w="*">' +
                '<card w="*" h="50" cardCornerRadius="8dp" cardElevation="2dp" layout_weight="1" margin="5 0">' +
                '<vertical gravity="center" bg="#009688" h="*" w="*" clickable="true" id="free_qq">' +
                '<text id="free_qq_text" text="QQ" textSize="16sp" textColor="#FFFFFF" gravity="center"/>' +
                '</vertical>' +
                '</card>' +
                '<card w="*" h="50" cardCornerRadius="8dp" cardElevation="2dp" layout_weight="1" margin="5 0">' +
                '<vertical gravity="center" bg="#009688" h="*" w="*" clickable="true" id="free_wb">' +
                '<text id="free_wb_text" text="微博" textSize="16sp" textColor="#FFFFFF" gravity="center"/>' +
                '</vertical>' +
                '</card>' +
                '</horizontal>' +

                // 免费脚本专区 - 第3排
                '<horizontal margin="0 5" gravity="center" w="*">' +
                '<card w="*" h="50" cardCornerRadius="8dp" cardElevation="2dp" layout_weight="1" margin="5 0">' +
                '<vertical gravity="center" bg="#009688" h="*" w="*" clickable="true" id="free_dy">' +
                '<text id="free_dy_text" text="抖音" textSize="16sp" textColor="#FFFFFF" gravity="center"/>' +
                '</vertical>' +
                '</card>' +
                '<card w="*" h="50" cardCornerRadius="8dp" cardElevation="2dp" layout_weight="1" margin="5 0">' +
                '<vertical gravity="center" bg="#009688" h="*" w="*" clickable="true" id="free_ks">' +
                '<text id="free_ks_text" text="快手" textSize="16sp" textColor="#FFFFFF" gravity="center"/>' +
                '</vertical>' +
                '</card>' +
                '</horizontal>' +

                // 免费脚本专区 - 第4排
                '<horizontal margin="0 5" gravity="center" w="*">' +
                '<card w="*" h="50" cardCornerRadius="8dp" cardElevation="2dp" layout_weight="1" margin="5 0">' +
                '<vertical gravity="center" bg="#009688" h="*" w="*" clickable="true" id="free_tb">' +
                '<text id="free_tb_text" text="淘宝" textSize="16sp" textColor="#FFFFFF" gravity="center"/>' +
                '</vertical>' +
                '</card>' +
                '<card w="*" h="50" cardCornerRadius="8dp" cardElevation="2dp" layout_weight="1" margin="5 0">' +
                '<vertical gravity="center" bg="#009688" h="*" w="*" clickable="true" id="free_jd">' +
                '<text id="free_jd_text" text="京东" textSize="16sp" textColor="#FFFFFF" gravity="center"/>' +
                '</vertical>' +
                '</card>' +
                '</horizontal>' +

                // 免费脚本专区 - 第5排
                '<horizontal margin="0 5" gravity="center" w="*">' +
                '<card w="*" h="50" cardCornerRadius="8dp" cardElevation="2dp" layout_weight="1" margin="5 0">' +
                '<vertical gravity="center" bg="#009688" h="*" w="*" clickable="true" id="free_blbl">' +
                '<text id="free_blbl_text" text="哔哩哔哩" textSize="16sp" textColor="#FFFFFF" gravity="center"/>' +
                '</vertical>' +
                '</card>' +
                '<card w="*" h="50" cardCornerRadius="8dp" cardElevation="2dp" layout_weight="1" margin="5 0">' +
                '<vertical gravity="center" bg="#009688" h="*" w="*" clickable="true" id="free_toutiao">' +
                '<text id="free_toutiao_text" text="今日头条" textSize="16sp" textColor="#FFFFFF" gravity="center"/>' +
                '</vertical>' +
                '</card>' +
                '</horizontal>' +

                // 底部留白
                '<text text="" margin="0 10"/>' +
                '</vertical>' +
                '</scroll>' +
                '</frame>' +

                '<frame id="myTabContent" layout_weight="1" visibility="gone">' +
                '<scroll>' +
                '<vertical padding="10" bg="#f5ffe0">' +
                // 个人中心卡片
                '<card cardCornerRadius="10dp" cardElevation="2dp" margin="5 5 5 10">' +
                '<vertical padding="15">' +
                '<horizontal>' +
                '<img src="@android:drawable/ic_menu_myplaces" w="60" h="60" margin="0 0 10 0"/>' +
                '<vertical layout_weight="1">' +
                '<text id="accountInfoText" text="我的账户: xxxx" textSize="14sp" textColor="#333333"/>' +
                '<text id="referrerInfoText" text="上级: xxxx" textSize="14sp" textColor="#333333" marginTop="3"/>' +
                '<text id="deviceIdText" text="设备码:" textSize="14sp" textColor="#333333" marginTop="3"/>' +
                '<text id="invitationCodeText" text="邀请码:" textSize="14sp" textColor="#333333" marginTop="3"/>' +
                '</vertical>' +
                '</horizontal>' +
                '<horizontal gravity="right" marginTop="5">' +
                '<text text="卡密有效期: " textSize="13sp" textColor="#666666"/>' +
                '<text id="expiryDateText" text="2025.7.1" textSize="13sp" textColor="#FF5722" marginLeft="5"/>' +
                '</horizontal>' +
                '</vertical>' +
                '</card>' +

                // 卡密区域
                '<card cardCornerRadius="10dp" cardElevation="2dp" margin="5 10">' +
                '<vertical padding="15">' +
                '<horizontal gravity="center_vertical">' +
                '<text text="卡密" textSize="16sp" textColor="#333333" layout_weight="1"/>' +
                '<input id="cardKeyInput" hint="兑换卡密" textSize="14sp" padding="8dp" bg="#f5f5f5" layout_weight="3"/>' +
                '<button id="exchangeBtn" text="兑换" textSize="14sp" w="60dp" h="40dp" style="Widget.AppCompat.Button.Colored" margin="5 0 0 0"/>' +
                '</horizontal>' +
                '<horizontal gravity="center" marginTop="10">' +
                '<button id="buyCardBtn" text="购买卡密" textSize="14sp" w="80dp" h="40dp" margin="5"/>' +
                '<button id="bindDeviceBtn" text="绑定设备" textSize="14sp" w="80dp" h="40dp" margin="5"/>' +
                '<button id="renewCardBtn" text="解绑设备" textSize="14sp" w="80dp" h="40dp" margin="5"/>' +
                '</horizontal>' +
                '<text text="注: 解绑设备需扣除3600分钟" textSize="13sp" textColor="#FF5722" marginTop="5"/>' +
                '</vertical>' +
                '</card>' +

                // 功能菜单列表 - 修改每日任务菜单项，添加id="dailyTaskBtn"
                '<card cardCornerRadius="10dp" cardElevation="2dp" margin="5 5" id="dailyTaskCard">' +
                '<horizontal gravity="center_vertical" padding="15" bg="#ffffff" clickable="true" id="dailyTaskBtn">' +
                '<img src="@android:drawable/ic_menu_today" w="24" h="24" tint="#333333" marginRight="10"/>' +
                '<text text="每日任务" textSize="16sp" textColor="#333333" layout_weight="1"/>' +
                '<img src="@android:drawable/ic_media_play" w="20" h="20" tint="#999999"/>' +
                '</horizontal>' +
                '</card>' +

                '<card cardCornerRadius="10dp" cardElevation="2dp" margin="5 5" id="agentSalesCard">' +
                '<horizontal gravity="center_vertical" padding="15" bg="#ffffff" clickable="true" id="agentSalesBtn">' +
                '<img src="@android:drawable/ic_menu_compass" w="24" h="24" tint="#333333" marginRight="10"/>' +
                '<text text="代理分销" textSize="16sp" textColor="#333333" layout_weight="1"/>' +
                '<img src="@android:drawable/ic_media_play" w="20" h="20" tint="#999999"/>' +
                '</horizontal>' +
                '</card>' +

                '<card cardCornerRadius="10dp" cardElevation="2dp" margin="5 5" id="myPointsCard">' +
                '<horizontal gravity="center_vertical" padding="15" bg="#ffffff" clickable="true" id="myPointsBtn">' +
                '<img src="@android:drawable/btn_star_big_on" w="24" h="24" tint="#333333" marginRight="10"/>' +
                '<text text="我的积分" textSize="16sp" textColor="#333333" layout_weight="1"/>' +
                '<img src="@android:drawable/ic_media_play" w="20" h="20" tint="#999999"/>' +
                '</horizontal>' +
                '</card>' +

                '<card cardCornerRadius="10dp" cardElevation="2dp" margin="5 5" id="pointsExchangeCard">' +
                '<horizontal gravity="center_vertical" padding="15" bg="#ffffff" clickable="true" id="pointsExchangeBtn">' +
                '<img src="@android:drawable/ic_menu_rotate" w="24" h="24" tint="#333333" marginRight="10"/>' +
                '<text text="积分兑换" textSize="16sp" textColor="#333333" layout_weight="1"/>' +
                '<img src="@android:drawable/ic_media_play" w="20" h="20" tint="#999999"/>' +
                '</horizontal>' +
                '</card>' +

                '<card cardCornerRadius="10dp" cardElevation="2dp" margin="5 5" id="accountWithdrawCard">' +
                '<horizontal gravity="center_vertical" padding="15" bg="#ffffff" clickable="true" id="accountWithdrawBtn">' +
                '<img src="@android:drawable/ic_menu_agenda" w="24" h="24" tint="#333333" marginRight="10"/>' +
                '<text text="账户提现" textSize="16sp" textColor="#333333" layout_weight="1"/>' +
                '<img src="@android:drawable/ic_media_play" w="20" h="20" tint="#999999"/>' +
                '</horizontal>' +
                '</card>' +

                '<card cardCornerRadius="10dp" cardElevation="2dp" margin="5 5" id="promotionCard">' +
                '<horizontal gravity="center_vertical" padding="15" bg="#ffffff" clickable="true" id="promotionRankBtn">' +
                '<img src="@android:drawable/ic_menu_share" w="24" h="24" tint="#333333" marginRight="10"/>' +
                '<text text="推广排名" textSize="16sp" textColor="#333333" layout_weight="1"/>' +
                '<img src="@android:drawable/ic_media_play" w="20" h="20" tint="#999999"/>' +
                '</horizontal>' +
                '</card>' +

                '<card cardCornerRadius="10dp" cardElevation="2dp" margin="5 5" id="aboutUsCard">' +
                '<horizontal gravity="center_vertical" padding="15" bg="#ffffff" clickable="true" id="aboutUsBtn">' +
                '<img src="@android:drawable/ic_menu_info_details" w="24" h="24" tint="#333333" marginRight="10"/>' +
                '<text text="关于我们" textSize="16sp" textColor="#333333" layout_weight="1"/>' +
                '<img src="@android:drawable/ic_media_play" w="20" h="20" tint="#999999"/>' +
                '</horizontal>' +
                '</card>' +

                '<card cardCornerRadius="10dp" cardElevation="2dp" margin="5 5" id="moreProjectsCard">' +
                '<horizontal gravity="center_vertical" padding="15" bg="#ffffff" clickable="true" id="moreProjectsBtn">' +
                '<img src="@android:drawable/ic_menu_more" w="24" h="24" tint="#333333" marginRight="10"/>' +
                '<text text="更多项目" textSize="16sp" textColor="#333333" layout_weight="1"/>' +
                '<img src="@android:drawable/ic_media_play" w="20" h="20" tint="#999999"/>' +
                '</horizontal>' +
                '</card>' +

                '<card cardCornerRadius="10dp" cardElevation="2dp" margin="5 5" id="wealthExchangeCard">' +
                '<horizontal gravity="center_vertical" padding="15" bg="#ffffff" clickable="true" id="wealthExchangeBtn">' +
                '<img src="@android:drawable/ic_menu_send" w="24" h="24" tint="#333333" marginRight="10"/>' +
                '<text text="发财交流群" textSize="16sp" textColor="#333333" layout_weight="1"/>' +
                '<img src="@android:drawable/ic_media_play" w="20" h="20" tint="#999999"/>' +
                '</horizontal>' +
                '</card>' +

                '<card cardCornerRadius="10dp" cardElevation="2dp" margin="5 5" id="sleepNotificationCard">' +
                '<horizontal gravity="center_vertical" padding="15" bg="#ffffff" clickable="true" id="sleepNotificationBtn">' +
                '<img src="@android:drawable/ic_popup_reminder" w="24" h="24" tint="#333333" marginRight="10"/>' +
                '<text text="脚本通知群" textSize="16sp" textColor="#333333" layout_weight="1"/>' +
                '<img src="@android:drawable/ic_media_play" w="20" h="20" tint="#999999"/>' +
                '</horizontal>' +
                '</card>' +

                '<card cardCornerRadius="10dp" cardElevation="2dp" margin="5 5 5 20" id="userManualCard">' +
                '<horizontal gravity="center_vertical" padding="15" bg="#ffffff" clickable="true" id="userManualBtn">' +
                '<img src="@android:drawable/ic_menu_help" w="24" h="24" tint="#333333" marginRight="10"/>' +
                '<text text="使用教程" textSize="16sp" textColor="#333333" layout_weight="1"/>' +
                '<img src="@android:drawable/ic_media_play" w="20" h="20" tint="#999999"/>' +
                '</horizontal>' +
                '</card>' +
                '</vertical>' +
                '</scroll>' +
                '</frame>' +

                '<card cardCornerRadius="0dp" cardElevation="5dp">' +
                '<horizontal bg="#FFFFFF" h="50" gravity="center_vertical">' +
                '<vertical layout_weight="1" gravity="center" id="scriptTabBtn" clickable="true">' +
                '<text id="scriptTabText" text="脚本" textSize="16sp" textColor="#009688" gravity="center"/>' +
                '</vertical>' +
                '<vertical layout_weight="1" gravity="center" id="myTabBtn" clickable="true">' +
                '<text id="myTabText" text="我的" textSize="16sp" textColor="#9E9E9E" gravity="center"/>' +
                '</vertical>' +
                '</horizontal>' +
                '</card>' +
                '</vertical>' +
                '</frame>';

            // 设置UI布局
            ui.layout(scriptCenterLayoutXml);

            // 绑定脚本中心页面事件
            this.bindScriptCenterEvents();

            // 绑定我的页面事件
            this.bindMyPageEvents();

            // 更新当前页面状态
            this.setCurrentPage("scriptCenter");
            global.LogModule.log("脚本中心界面创建成功");

            // 获取最新公告并显示（仅在showAnnouncement为true时显示）
            if (showAnnouncement === true) {
                this.fetchAndShowAnnouncement();
                // 设置标志，表示已经显示过公告
                if (global.ConfigModule) {
                    global.ConfigModule.set("hasShownAnnouncement", "true");
                }
            }
        } catch (e) {
            global.LogModule.log("创建脚本中心界面失败: " + e.message, "ERROR");
            console.error("创建脚本中心界面失败: " + e.message);
            console.error(e.stack);
        }
    },

    /**
     * 绑定脚本中心页面事件 - 完全按照原版还原
     */
    bindScriptCenterEvents: function () {
        try {
            // 设置标签切换逻辑
            var that = this;

            // 脚本标签按钮点击事件
            ui.scriptTabBtn.on("click", function () {
                try {
                    ui.scriptTabContent.attr("visibility", "visible");
                    ui.myTabContent.attr("visibility", "gone");

                    // 更新标签样式 - 使用直接ID访问
                    if (ui.scriptTabText) {
                        ui.scriptTabText.attr("textColor", "#009688");
                    }
                    if (ui.myTabText) {
                        ui.myTabText.attr("textColor", "#9E9E9E");
                    }

                    return true; // 返回true阻止事件继续传播
                } catch (e) {
                    global.LogModule.log("处理脚本标签点击事件失败: " + e.message, "ERROR");
                    return true;
                }
            });

            // 我的标签按钮点击事件
            ui.myTabBtn.on("click", function () {
                try {
                    ui.scriptTabContent.attr("visibility", "gone");
                    ui.myTabContent.attr("visibility", "visible");

                    // 更新标签样式 - 使用直接ID访问
                    if (ui.scriptTabText) {
                        ui.scriptTabText.attr("textColor", "#9E9E9E");
                    }
                    if (ui.myTabText) {
                        ui.myTabText.attr("textColor", "#009688");
                    }

                    return true; // 返回true阻止事件继续传播
                } catch (e) {
                    global.LogModule.log("处理我的标签点击事件失败: " + e.message, "ERROR");
                    return true;
                }
            });

            // 绑定脚本项点击事件
            this.bindScriptItemEvents();
        } catch (e) {
            global.LogModule.log("绑定脚本中心事件失败: " + e.message, "ERROR");
        }
    },

    /**
     * 绑定脚本项点击事件 - 完全按照原版还原
     */
    bindScriptItemEvents: function () {
        try {
            var that = this;

            // VIP脚本项点击事件
            var vipScriptItems = ["moon_box", "vip_wx", "vip_qq", "vip_wb", "vip_script1", "vip_script2", "vip_script3", "vip_script4"];
            vipScriptItems.forEach(function (id) {
                if (ui[id]) {
                    ui[id].on("click", function () {
                        try {
                            // 获取脚本名称 - 使用直接ID访问
                            var textId = id + "_text";
                            var scriptName = id.replace("vip_", "");

                            // 如果文本元素存在，则使用其文本内容
                            if (ui[textId]) {
                                try {
                                    scriptName = ui[textId].text();
                                } catch (e) {
                                    global.LogModule.log("无法获取文本内容: " + e.message);
                                }
                            }

                            var expiryDateText = ui.findById("expiryDateText");
                            // 根据脚本ID执行相应逻辑
                            // 验证卡密状态（异步验证）
                            that.validateCardKey(function (isValid, message) {
                                if (!isValid) {
                                    that.showToast(message || "没有卡密信息或卡密已过期，请续费后使用");
                                    return;
                                }
                                // 卡密验证通过，启动月光宝盒脚本
                                if (id === "moon_box") {
                                    if (typeof global !== 'undefined' && global.MoonBoxModule) {
                                        global.MoonBoxModule.createMoonBoxUI();
                                    } else {
                                        that.showToast("月光宝盒模块未加载");
                                    }
                                    return true; // 返回true阻止事件继续传播
                                } else {
                                    that.showToast("更多脚本正在开发中...");
                                }
                            });

                        } catch (e) {
                            global.LogModule.log("处理VIP脚本点击事件失败: " + e.message, "ERROR");
                            return true;
                        }
                    });
                }
            });

            // 免费脚本项点击事件
            var freeScriptItems = ["free_wx_read", "free_wx", "free_qq", "free_wb", "free_dy", "free_ks", "free_tb", "free_jd", "free_blbl", "free_toutiao"];
            freeScriptItems.forEach(function (id) {
                if (ui[id]) {
                    ui[id].on("click", function () {
                        try {
                            // 获取脚本名称 - 使用直接ID访问
                            var textId = id + "_text";
                            var scriptName = id.replace("free_", "");

                            // 如果文本元素存在，则使用其文本内容
                            switch (id) {
                                case "free_wx_read":
                                    that.createWxReadConfigPage();
                                    break;
                                default:
                                    that.showToast("更多免费脚本正在开发中。。");
                                    break;
                            }

                            global.LogModule.log("用户点击了免费脚本: " + scriptName);

                            return true; // 返回true阻止事件继续传播
                        } catch (e) {
                            global.LogModule.log("处理免费脚本点击事件失败: " + e.message, "ERROR");
                            return true;
                        }
                    });
                }
            });
        } catch (e) {
            global.LogModule.log("绑定脚本项点击事件失败: " + e.message, "ERROR");
        }
    },

    /**
     * 验证卡密是否过期 - 完全按照原版还原
     * @param {function} callback - 回调函数，参数为 (isValid, message)
     */
    validateCardKey: function (callback) {
        try {
            // 优先从全局变量获取用户手机号
            var userPhone = null;
            if (currentUser && currentUser.phone) {
                userPhone = currentUser.phone;
            } else if (global.ConfigModule) {
                userPhone = global.ConfigModule.get("userPhone");
            }

            if (!userPhone) {
                global.LogModule.log("用户未登录，无法验证卡密");
                if (callback) callback(false, "用户未登录，无法验证卡密");
                return;
            }

            // 调试Token状态
            console.log("========== 卡密验证前Token调试 ==========");
            var token = global.NetworkModule.debugTokenStatus();

            // 验证Authorization头格式
            if (token) {
                global.NetworkModule.verifyAuthorizationHeader(token);
            }

            // 调用API查询卡密信息
            global.NetworkModule.queryCardKeyInfo(userPhone, function (error, result) {
                try {
                    if (error) {
                        console.error("查询卡密信息失败: " + error.message);
                        global.LogModule.log("查询卡密信息失败: " + error.message, "ERROR");
                        if (callback) callback(false, "查询卡密信息失败，请检查网络连接");
                        return;
                    }

                    // 检查返回结果
                    if (!result || result.code !== 200) {
                        global.LogModule.log("卡密查询返回错误: " + (result ? result.message : "未知错误"), "ERROR");
                        if (callback) callback(false, result && result.message ? result.message : "查询卡密信息失败");
                        return;
                    }

                    // 检查卡密数据是否存在
                    if (!result.data || result.data === null || result.data === undefined) {
                        global.LogModule.log("没有卡密信息", "INFO");
                        if (callback) callback(false, "您还没有购买卡密，请先购买卡密后使用");
                        return;
                    }

                    // 将后端时间字符串转换为Date对象
                    // 处理 "2025-09-06 06:30:36" 格式的日期字符串
                    var backendDate;
                    try {
                        // 方法1: 直接使用Date构造函数
                        backendDate = new Date(result.data);

                        // 如果解析失败，尝试替换空格为T（ISO格式）
                        if (isNaN(backendDate.getTime())) {
                            var isoDateString = result.data.replace(' ', 'T');
                            backendDate = new Date(isoDateString);
                        }

                        // 如果还是失败，手动解析
                        if (isNaN(backendDate.getTime())) {
                            var dateTimeParts = result.data.split(' ');
                            if (dateTimeParts.length === 2) {
                                var datePart = dateTimeParts[0]; // "2025-09-06"
                                var timePart = dateTimeParts[1]; // "06:30:36"

                                var dateComponents = datePart.split('-');
                                var timeComponents = timePart.split(':');

                                if (dateComponents.length === 3 && timeComponents.length === 3) {
                                    // 注意：月份需要减1，因为JavaScript的月份是0-11
                                    backendDate = new Date(
                                        parseInt(dateComponents[0]), // 年
                                        parseInt(dateComponents[1]) - 1, // 月（0-11）
                                        parseInt(dateComponents[2]), // 日
                                        parseInt(timeComponents[0]), // 小时
                                        parseInt(timeComponents[1]), // 分钟
                                        parseInt(timeComponents[2])  // 秒
                                    );
                                }
                            }
                        }
                    } catch (parseError) {
                        console.error("日期解析异常: " + parseError.message);
                    }

                    var currentDate = new Date();

                    // 检查日期是否有效
                    if (!backendDate || isNaN(backendDate.getTime())) {
                        if (callback) callback(false, "卡密信息格式错误，请联系客服");
                        return;
                    }

                    // 计算时间差（毫秒）
                    var timeDiff = backendDate - currentDate;

                    // 如果小于当前时间则是过期
                    if (timeDiff < 0) {
                        if (callback) callback(false, "您的卡密已过期，请续费后使用");
                        return;
                    }

                    // 卡密有效
                    global.LogModule.log("卡密验证通过", "INFO");
                    if (callback) callback(true, "卡密验证通过");

                } catch (parseError) {
                    global.LogModule.log("解析卡密验证结果失败: " + parseError.message, "ERROR");
                    if (callback) callback(false, "验证卡密时出现错误，请重试");
                }
            });
        } catch (e) {
            global.LogModule.log("验证卡密是否过期失败: " + e.message, "ERROR");
            if (callback) callback(false, "验证卡密时出现错误，请重试");
        }
    },

    /**
     * 绑定我的页面事件 - 完全按照原版还原
     */
    bindMyPageEvents: function () {
        try {
            var that = this;

            // 获取当前用户信息 - 优先从全局变量获取
            var userPhone = null;
            var userId = null;
            var referrerId = null;
            var invitationCode = null;

            if (currentUser && currentUser.phone) {
                userPhone = currentUser.phone;
                userId = currentUser.id;
                referrerId = currentUser.referrerId || currentUser.referrer;
                invitationCode = currentUser.invitationCode || currentUser.id;
            } else if (global.ConfigModule) {
                userPhone = global.ConfigModule.get("userPhone");
                userId = global.ConfigModule.get("userId");
                referrerId = global.ConfigModule.get("referrerId");
                invitationCode = global.ConfigModule.get("invitationCode");
            }

            var deviceId = device.getAndroidId();

            // 设置用户信息
            if (ui.cardKeyInput) {
                // 设置用户信息
                var accountInfoText = ui.findById("accountInfoText");
                if (accountInfoText) {
                    accountInfoText.setText("我的账户: " + this.maskPhone(userPhone));
                }

                var referrerInfoText = ui.findById("referrerInfoText");
                if (referrerInfoText) {
                    referrerInfoText.setText("上级: " + (referrerId || "无"));
                }

                var deviceIdText = ui.findById("deviceIdText");
                if (deviceIdText) {
                    deviceIdText.setText("设备码: " + deviceId);
                }

                var invitationCodeText = ui.findById("invitationCodeText");
                if (invitationCodeText) {
                    invitationCodeText.setText("邀请码: " + (invitationCode || "未设置"));
                }

                // 查询卡密信息
                this.queryCardKeyInfo(userPhone);

                // 兑换卡密按钮点击事件
                ui.exchangeBtn.on("click", function () {
                    var cardKey = ui.cardKeyInput.text();
                    if (!cardKey) {
                        that.showToast("请输入卡密");
                        return;
                    }

                    // 调用兑换卡密接口
                    that.redeemCardKey(cardKey, userPhone);
                });

                // 购买卡密按钮点击事件
                ui.buyCardBtn.on("click", function () {
                    try {
                        // 跳转到购买卡密网页
                        app.openUrl("https://www.baidu.com");
                        global.LogModule.log("跳转到购买卡密网页");
                    } catch (e) {
                        global.LogModule.log("跳转到购买卡密网页失败: " + e.message, "ERROR");
                        that.showToast("跳转到购买卡密网页失败");
                    }
                });

                // 解绑设备按钮点击事件
                ui.renewCardBtn.on("click", function () {
                    // 弹出确认对话框
                    dialogs.confirm("解绑设备提示",
                        "解绑设备将扣除3600分钟的卡密时长，确定要解绑吗？",
                        function (confirmed) {
                            if (confirmed) {
                                // 调用解绑设备接口 - 使用全局UIModule引用
                                console.log("准备调用解绑设备功能，用户手机号: " + userPhone + ", 设备ID: " + deviceId);
                                global.UIModule.unbindDevice(userPhone, deviceId);
                            }
                        }
                    );
                });

                // 绑定设备按钮点击事件
                ui.bindDeviceBtn.on("click", function () {
                    // 弹出确认对话框
                    dialogs.confirm("绑定设备提示",
                        "确定要将当前设备与账号绑定吗？绑定后其他设备将无法使用该账号。",
                        function (confirmed) {
                            if (confirmed) {
                                // 调用绑定设备接口
                                that.bindDevice(userPhone, deviceId);
                            }
                        }
                    );
                });
            }

            // 处理推广排名卡片点击事件
            if (ui.promotionCard) {
                ui.promotionCard.on("click", function () {
                    try {
                        global.LogModule.log("推广排名卡片被点击", "INFO");

                        // 调用推广排名模块
                        if (typeof global !== 'undefined' && global.PromotionRankModule && global.PromotionRankModule.createPromotionRankUI) {
                            global.PromotionRankModule.createPromotionRankUI();
                        } else {
                            that.showToast("推广排名模块未加载");
                        }
                        return true; // 阻止事件继续传播
                    } catch (e) {
                        global.LogModule.log("处理推广排名卡片点击事件失败: " + e.message, "ERROR");
                        that.showToast("加载推广排名页面失败: " + e.message);
                        return true;
                    }
                });
            }

            // 处理推广排名按钮点击事件
            if (ui.promotionRankBtn) {
                ui.promotionRankBtn.on("click", function () {
                    try {
                        // 调用推广排名模块
                        if (typeof global !== 'undefined' && global.PromotionRankModule && global.PromotionRankModule.createPromotionRankUI) {
                            global.PromotionRankModule.createPromotionRankUI();
                        } else {
                            that.showToast("推广排名模块未加载");
                        }
                        return true; // 阻止事件继续传播
                    } catch (e) {
                        global.LogModule.log("处理推广排名按钮点击事件失败: " + e.message, "ERROR");
                        return true;
                    }
                });
            }

            // 处理关于我们卡片点击事件
            if (ui.aboutUsCard) {
                ui.aboutUsCard.on("click", function () {
                    try {
                        // 调用关于我们模块
                        if (typeof global !== 'undefined' && global.AboutUsModule && global.AboutUsModule.createAboutUsUI) {
                            global.AboutUsModule.createAboutUsUI();
                        } else {
                            that.showToast("关于我们模块未加载");
                        }
                        return true; // 阻止事件继续传播
                    } catch (e) {
                        global.LogModule.log("处理关于我们卡片点击事件失败: " + e.message, "ERROR");
                        return true;
                    }
                });
            }

            // 处理关于我们按钮点击事件
            if (ui.aboutUsBtn) {
                ui.aboutUsBtn.on("click", function () {
                    try {
                        // 调用关于我们模块
                        if (typeof global !== 'undefined' && global.AboutUsModule && global.AboutUsModule.createAboutUsUI) {
                            global.AboutUsModule.createAboutUsUI();
                        } else {
                            that.showToast("关于我们模块未加载");
                        }
                        return true; // 阻止事件继续传播
                    } catch (e) {
                        return true;
                    }
                });
            }

            // 绑定其他菜单项点击事件
            this.bindMyMenuEvents();

        } catch (e) {
            global.LogModule.log("绑定我的页面事件失败: " + e.message, "ERROR");
        }
    },

    /**
     * 绑定我的页面菜单事件 - 完全按照原版还原
     */
    bindMyMenuEvents: function () {
        var that = this;

        // 单独处理账户提现卡片点击事件
        if (ui.accountWithdrawCard) {
            ui.accountWithdrawCard.on("click", function () {
                try {
                    global.LogModule.log("账户提现卡片被点击", "INFO");

                    // 调用账户提现模块
                    if (typeof global !== 'undefined' && global.showWithdrawalPage) {
                        // 使用延迟执行，避免UI线程阻塞
                        setTimeout(function () {
                            try {
                                global.showWithdrawalPage();
                            } catch (delayedError) {
                                that.showToast("加载提现页面失败: " + delayedError.message);
                            }
                        }, 100);
                    } else {
                        console.error("showWithdrawalPage未定义，无法加载提现页面");
                        global.LogModule.log("showWithdrawalPage未定义，无法加载提现页面", "ERROR");
                        that.showToast("加载提现页面失败: showWithdrawalPage未定义");
                    }

                    return true; // 阻止事件继续传播
                } catch (e) {
                    global.LogModule.log("处理账户提现卡片点击事件失败: " + e.message, "ERROR");
                    that.showToast("加载提现页面失败: " + e.message);
                    return true;
                }
            });
        }

        // 单独处理账户提现按钮点击事件
        if (ui.accountWithdrawBtn) {
            ui.accountWithdrawBtn.on("click", function () {
                try {
                    global.LogModule.log("账户提现按钮被点击", "INFO");

                    // 调用账户提现模块
                    if (typeof global !== 'undefined' && global.WithdrawalModule && global.WithdrawalModule.createWithdrawalPage) {
                        // 跳转到提现页面
                        global.LogModule.log("准备调用WithdrawalModule.createWithdrawalPage()", "INFO");

                        // 使用延迟执行，避免UI线程阻塞
                        setTimeout(function () {
                            try {
                                global.WithdrawalModule.createWithdrawalPage();
                            } catch (delayedError) {
                                global.LogModule.log("延迟执行createWithdrawalPage失败: " + delayedError.message, "ERROR");
                                that.showToast("加载提现页面失败: " + delayedError.message);
                            }
                        }, 100);
                    } else if (typeof global !== 'undefined' && global.showWithdrawalPage) {
                        // 降级方案：使用原来的方法
                        global.LogModule.log("WithdrawalModule不可用，使用降级方案showWithdrawalPage()", "INFO");
                        setTimeout(function () {
                            try {
                                global.showWithdrawalPage();
                            } catch (delayedError) {
                                global.LogModule.log("延迟执行showWithdrawalPage失败: " + delayedError.message, "ERROR");
                                that.showToast("加载提现页面失败: " + delayedError.message);
                            }
                        }, 100);
                    } else {
                        global.LogModule.log("提现模块不可用，无法加载提现页面", "ERROR");
                        that.showToast("提现功能暂时不可用");
                    }

                    return true; // 阻止事件继续传播
                } catch (e) {
                    global.LogModule.log("处理账户提现按钮点击事件失败: " + e.message, "ERROR");
                    that.showToast("加载提现页面失败: " + e.message);
                    return true;
                }
            });
        }

        // 单独处理我的积分卡片点击事件
        if (ui.myPointsCard) {
            ui.myPointsCard.on("click", function () {
                try {
                    global.LogModule.log("我的积分卡片被点击", "INFO");
                    // 调用我的积分模块
                    if (typeof global !== 'undefined' && global.MyPointsModule && global.MyPointsModule.createMyPointsUI) {
                        global.MyPointsModule.createMyPointsUI();
                    } else {
                        that.showToast("积分模块未加载");
                    }
                    return true; // 阻止事件继续传播
                } catch (e) {
                    global.LogModule.log("处理积分卡片点击事件失败: " + e.message, "ERROR");
                    that.showToast("加载积分页面失败: " + e.message);
                    return true;
                }
            });
        }

        // 添加对myPointsBtn的点击事件处理
        if (ui.myPointsBtn) {
            ui.myPointsBtn.on("click", function () {
                try {
                    global.LogModule.log("我的积分按钮被点击", "INFO");
                    // 调用我的积分模块
                    if (typeof global !== 'undefined' && global.MyPointsModule && global.MyPointsModule.createMyPointsUI) {
                        global.MyPointsModule.createMyPointsUI();
                    } else {
                        that.showToast("积分模块未加载");
                    }
                    return true; // 阻止事件继续传播
                } catch (e) {
                    console.error("处理积分按钮点击事件失败: " + e.message);
                    global.LogModule.log("处理积分按钮点击事件失败: " + e.message, "ERROR");
                    that.showToast("加载积分页面失败: " + e.message);
                    return true;
                }
            });
        }

        // 处理每日任务按钮点击事件
        if (ui.dailyTaskBtn) {
            ui.dailyTaskBtn.on("click", function () {
                try {
                    global.LogModule.log("每日任务按钮被点击", "INFO");
                    // 调用每日任务模块
                    if (typeof global !== 'undefined' && global.DailyTaskModule && global.DailyTaskModule.createDailyTaskUI) {
                        global.DailyTaskModule.createDailyTaskUI();
                    } else {
                        // 降级处理：显示开发中提示
                        that.showToast("每日任务功能开发中...");
                        global.LogModule.log("每日任务模块未加载，显示开发中提示", "INFO");
                    }
                    return true;
                } catch (e) {
                    global.LogModule.log("处理每日任务按钮点击事件失败: " + e.message, "ERROR");
                    that.showToast("每日任务功能开发中...");
                    return true;
                }
            });
        }

        // 处理代理分销按钮点击事件
        if (ui.agentSalesBtn) {
            ui.agentSalesBtn.on("click", function () {
                try {
                    global.LogModule.log("代理分销按钮被点击", "INFO");
                    // 调用代理分销模块
                    if (typeof global !== 'undefined' && global.AgentSalesModule && global.AgentSalesModule.createAgentSalesUI) {
                        global.AgentSalesModule.createAgentSalesUI();
                    } else {
                        // 降级处理：显示开发中提示
                        that.showToast("代理分销功能开发中...");
                        global.LogModule.log("代理分销模块未加载，显示开发中提示", "INFO");
                    }
                    return true;
                } catch (e) {
                    global.LogModule.log("处理代理分销按钮点击事件失败: " + e.message, "ERROR");
                    that.showToast("代理分销功能开发中...");
                    return true;
                }
            });
        }

        // 处理每日任务卡片点击事件（整个卡片）
        if (ui.dailyTaskCard) {
            ui.dailyTaskCard.on("click", function () {
                try {
                    global.LogModule.log("每日任务卡片被点击", "INFO");
                    // 调用每日任务模块
                    if (typeof global !== 'undefined' && global.DailyTaskModule && global.DailyTaskModule.createDailyTaskUI) {
                        global.DailyTaskModule.createDailyTaskUI();
                    } else {
                        that.showToast("每日任务模块未加载");
                    }
                    return true; // 阻止事件继续传播
                } catch (e) {
                    global.LogModule.log("处理每日任务卡片点击事件失败: " + e.message, "ERROR");
                    that.showToast("加载每日任务页面失败: " + e.message);
                    return true;
                }
            });
        }

        // 处理积分兑换按钮点击事件
        if (ui.pointsExchangeBtn) {
            ui.pointsExchangeBtn.on("click", function () {
                try {
                    global.LogModule.log("积分兑换按钮被点击", "INFO");
                    // 调用积分兑换模块
                    if (typeof global !== 'undefined' && global.PointsExchangeModule && global.PointsExchangeModule.showPointsExchangePage) {
                        global.PointsExchangeModule.showPointsExchangePage();
                    } else {
                        // 降级处理：显示开发中提示
                        that.showToast("积分兑换功能开发中...");
                        global.LogModule.log("积分兑换模块未加载，显示开发中提示", "INFO");
                    }
                    return true;
                } catch (e) {
                    global.LogModule.log("处理积分兑换按钮点击事件失败: " + e.message, "ERROR");
                    that.showToast("积分兑换功能开发中...");
                    return true;
                }
            });
        }

        // 处理更多项目按钮点击事件
        if (ui.moreProjectsBtn) {
            ui.moreProjectsBtn.on("click", function () {
                try {
                    global.LogModule.log("更多项目按钮被点击", "INFO");
                    // 调用关于我们模块的更多项目功能
                    if (typeof global !== 'undefined' && global.AboutUsModule && global.AboutUsModule.createMoreProjectsUI) {
                        global.AboutUsModule.createMoreProjectsUI();
                    } else {
                        that.showToast("更多项目功能开发中...");
                        global.LogModule.log("更多项目模块未加载，显示开发中提示", "INFO");
                    }
                    return true;
                } catch (e) {
                    global.LogModule.log("处理更多项目按钮点击事件失败: " + e.message, "ERROR");
                    that.showToast("更多项目功能开发中...");
                    return true;
                }
            });
        }

        // 处理发财交流群按钮点击事件
        if (ui.wealthExchangeBtn) {
            ui.wealthExchangeBtn.on("click", function () {
                try {
                    global.LogModule.log("发财交流群按钮被点击", "INFO");
                    // 调用关于我们模块的发财交流群功能
                    if (typeof global !== 'undefined' && global.AboutUsModule && global.AboutUsModule.jumpWealthExchange) {
                        global.AboutUsModule.jumpWealthExchange();
                    } else {
                        that.showToast("发财交流群功能开发中...");
                        global.LogModule.log("发财交流群模块未加载，显示开发中提示", "INFO");
                    }
                    return true;
                } catch (e) {
                    global.LogModule.log("处理发财交流群按钮点击事件失败: " + e.message, "ERROR");
                    that.showToast("发财交流群功能开发中...");
                    return true;
                }
            });
        }

        // 处理脚本通知群按钮点击事件
        if (ui.sleepNotificationBtn) {
            ui.sleepNotificationBtn.on("click", function () {
                try {
                    global.LogModule.log("脚本通知群按钮被点击", "INFO");
                    // 调用关于我们模块的脚本通知群功能
                    if (typeof global !== 'undefined' && global.AboutUsModule && global.AboutUsModule.jumpScriptNotificationGroup) {
                        global.AboutUsModule.jumpScriptNotificationGroup();
                    } else {
                        that.showToast("脚本通知群功能开发中...");
                        global.LogModule.log("脚本通知群模块未加载，显示开发中提示", "INFO");
                    }
                    return true;
                } catch (e) {
                    global.LogModule.log("处理脚本通知群按钮点击事件失败: " + e.message, "ERROR");
                    that.showToast("脚本通知群功能开发中...");
                    return true;
                }
            });
        }

        // 处理使用教程按钮点击事件
        if (ui.userManualBtn) {
            ui.userManualBtn.on("click", function () {
                try {
                    global.LogModule.log("使用教程按钮被点击", "INFO");
                    // 调用使用教程模块
                    if (typeof global !== 'undefined' && global.TutorialModule && global.TutorialModule.createTutorialUI) {
                        global.TutorialModule.createTutorialUI();
                    } else {
                        that.showToast("使用教程功能开发中...");
                        global.LogModule.log("使用教程模块未加载，显示开发中提示", "INFO");
                    }
                    return true;
                } catch (e) {
                    global.LogModule.log("处理使用教程按钮点击事件失败: " + e.message, "ERROR");
                    that.showToast("使用教程功能开发中...");
                    return true;
                }
            });
        }
    },

    /**
     * 查询卡密信息 - 完全按照原版还原
     */
    queryCardKeyInfo: function (userPhone) {
        try {
            var that = this;
            if (!userPhone) {
                global.LogModule.log("用户手机号为空，无法查询卡密信息", "ERROR");
                return;
            }

            global.NetworkModule.queryCardKeyInfo(userPhone, function (error, result) {
                try {
                    if (error) {
                        global.LogModule.log("查询卡密信息失败: " + error.message, "ERROR");
                        return;
                    }

                    if (result && result.code === 200 && result.data) {
                        // 更新卡密有效期显示
                        var expiryDateText = ui.findById("expiryDateText");
                        if (expiryDateText) {
                            // 格式化日期显示
                            try {
                                var expiryDate = new Date(result.data);

                                // 检查日期是否有效
                                if (isNaN(expiryDate.getTime())) {
                                    // 尝试手动解析日期字符串 (格式: "2025-09-26 06:30:36")
                                    var parts = result.data.split(' ');
                                    if (parts.length === 2) {
                                        var dateParts = parts[0].split('-');
                                        var timeParts = parts[1].split(':');

                                        if (dateParts.length === 3 && timeParts.length === 3) {
                                            expiryDate = new Date(
                                                parseInt(dateParts[0]), // 年
                                                parseInt(dateParts[1]) - 1, // 月 (0-11)
                                                parseInt(dateParts[2]), // 日
                                                parseInt(timeParts[0]), // 时
                                                parseInt(timeParts[1]), // 分
                                                parseInt(timeParts[2])  // 秒
                                            );
                                        }
                                    }
                                }

                                // 如果日期仍然无效，显示原始字符串
                                if (isNaN(expiryDate.getTime())) {
                                    expiryDateText.setText(result.data);
                                } else {
                                    // 格式化为完整的日期时间格式：YYYY.MM.DD HH:mm:ss
                                    var formattedDate = expiryDate.getFullYear() + "-" +
                                        String(expiryDate.getMonth() + 1).padStart(2, '0') + "-" +
                                        String(expiryDate.getDate()).padStart(2, '0') + " " +
                                        String(expiryDate.getHours()).padStart(2, '0') + ":" +
                                        String(expiryDate.getMinutes()).padStart(2, '0') + ":" +
                                        String(expiryDate.getSeconds()).padStart(2, '0');
                                    expiryDateText.setText(formattedDate);
                                }
                            } catch (dateError) {
                                console.error("日期格式化失败: " + dateError.message);
                                // 显示原始字符串作为降级方案
                                expiryDateText.setText(result.data);
                            }
                        }
                        global.LogModule.log("卡密信息查询成功，有效期: " + result.data);
                    } else {
                        global.LogModule.log("没有卡密信息或查询失败");
                        var expiryDateText = ui.findById("expiryDateText");
                        if (expiryDateText) {
                            expiryDateText.setText("未购买");
                        }
                    }
                } catch (parseError) {
                    global.LogModule.log("解析卡密信息失败: " + parseError.message, "ERROR");
                }
            });
        } catch (e) {
            global.LogModule.log("查询卡密信息异常: " + e.message, "ERROR");
        }
    },

    /**
     * 兑换卡密 - 完全按照原版还原
     */
    redeemCardKey: function (cardKey, phone) {
        try {
            var that = this;
            var deviceId = device.getAndroidId();

            // 显示加载提示
            this.showToast("正在兑换卡密...");

            // 调用API兑换卡密
            global.NetworkModule.redeemCardKey(cardKey, phone, deviceId, function (error, result) {
                if (error) {
                    that.showToast("兑换卡密失败: " + error.message);
                    global.LogModule.log("兑换卡密失败: " + error.message, "ERROR");
                    return;
                }

                if (result && result.code === 200) {
                    that.showToast("卡密兑换成功");
                    global.LogModule.log("卡密兑换成功");

                    // 如果返回了新的到期时间，直接更新UI
                    if (result.data) {
                        ui.run(function () {
                            try {
                                var expiryDateText = ui.findById("expiryDateText");
                                if (expiryDateText) {
                                    // 改进日期解析方式
                                    var expiryDate;
                                    try {
                                        // 尝试直接解析
                                        expiryDate = new Date(result.data);

                                        // 检查日期是否有效
                                        if (isNaN(expiryDate.getTime())) {
                                            // 手动解析日期字符串 (格式: "2025-07-08 19:30:05")
                                            var parts = result.data.split(' ');
                                            var dateParts = parts[0].split('-');
                                            var timeParts = parts[1].split(':');

                                            expiryDate = new Date(
                                                parseInt(dateParts[0]), // 年
                                                parseInt(dateParts[1]) - 1, // 月 (0-11)
                                                parseInt(dateParts[2]), // 日
                                                parseInt(timeParts[0]), // 时
                                                parseInt(timeParts[1]), // 分
                                                parseInt(timeParts[2])  // 秒
                                            );
                                        }
                                    } catch (parseError) {
                                        console.error("解析日期出错: " + parseError.message);
                                        global.LogModule.log("解析日期出错: " + parseError.message, "ERROR");
                                        // 尝试最简单的方式显示原始字符串
                                        expiryDateText.setText(result.data);
                                        return;
                                    }

                                    var currentDate = new Date();

                                    // 比较日期
                                    if (expiryDate > currentDate) {
                                        // 卡密有效
                                        expiryDateText.setText(result.data);
                                    } else {
                                        // 卡密已过期
                                        expiryDateText.setText("已过期");
                                    }
                                }
                            } catch (e) {
                                global.LogModule.log("更新兑换后卡密UI失败: " + e.message, "ERROR");
                                console.error("更新兑换后卡密UI失败: " + e.message);
                            }
                        });
                    } else {
                        // 如果没有返回新的到期时间，刷新卡密信息
                        that.queryCardKeyInfo(phone);
                    }

                    // 清空输入框
                    ui.cardKeyInput.setText("");
                } else {
                    var errorMsg = result && result.message ? result.message : "兑换卡密失败";
                    that.showToast(errorMsg);
                    global.LogModule.log("兑换卡密响应错误: " + errorMsg, "ERROR");
                }
            });
        } catch (e) {
            global.LogModule.log("执行兑换卡密操作失败: " + e.message, "ERROR");
            this.showToast("兑换卡密出错");
        }
    },

    /**
     * 解绑设备 - 完全按照原版还原
     * @param {string} phone - 用户手机号
     * @param {string} deviceId - 设备ID
     */
    unbindDevice: function (phone, deviceId) {
        try {
            var that = this;

            // 显示加载提示
            this.showToast("正在解绑设备...");

            // 调用API解绑设备
            global.NetworkModule.unbindDevice(phone, deviceId, function (error, result) {
                if (error) {
                    that.showToast("解绑设备失败: " + error.message);
                    global.LogModule.log("解绑设备失败: " + error.message, "ERROR");
                    return;
                }

                if (result && result.code === 200) {
                    that.showToast("设备解绑成功");

                    // 如果返回了新的到期时间，直接更新UI
                    if (result.data) {
                        ui.run(function () {
                            try {
                                var expiryDateText = ui.findById("expiryDateText");
                                if (expiryDateText) {
                                    // 改进日期解析方式
                                    var expiryDate;
                                    try {
                                        // 尝试直接解析
                                        expiryDate = new Date(result.data);

                                        // 检查日期是否有效
                                        if (isNaN(expiryDate.getTime())) {
                                            // 手动解析日期字符串 (格式: "2025-07-08 19:30:05")
                                            var parts = result.data.split(' ');
                                            var dateParts = parts[0].split('-');
                                            var timeParts = parts[1].split(':');

                                            expiryDate = new Date(
                                                parseInt(dateParts[0]), // 年
                                                parseInt(dateParts[1]) - 1, // 月 (0-11)
                                                parseInt(dateParts[2]), // 日
                                                parseInt(timeParts[0]), // 时
                                                parseInt(timeParts[1]), // 分
                                                parseInt(timeParts[2])  // 秒
                                            );
                                        }
                                    } catch (parseError) {
                                        console.error("解析日期出错: " + parseError.message);
                                        global.LogModule.log("解析日期出错: " + parseError.message, "ERROR");
                                        // 尝试最简单的方式显示原始字符串
                                        expiryDateText.setText(result.data);
                                        return;
                                    }

                                    var currentDate = new Date();

                                    // 比较日期
                                    if (expiryDate > currentDate) {
                                        // 卡密有效
                                        expiryDateText.setText(result.data);
                                    } else {
                                        // 卡密已过期
                                        expiryDateText.setText("已过期");
                                    }
                                }
                            } catch (e) {
                                global.LogModule.log("更新解绑后卡密UI失败: " + e.message, "ERROR");
                                console.error("更新解绑后卡密UI失败: " + e.message);
                            }
                        });
                    } else {
                        // 如果没有返回新的到期时间，刷新卡密信息
                        that.queryCardKeyInfo(phone);
                    }

                    global.LogModule.log("设备解绑成功");
                } else {
                    var errorMsg = result && result.message ? result.message : "解绑设备失败";
                    that.showToast(errorMsg);
                    global.LogModule.log("解绑设备响应错误: " + errorMsg, "ERROR");
                }
            });
        } catch (e) {
            global.LogModule.log("执行解绑设备操作失败: " + e.message, "ERROR");
            this.showToast("解绑设备出错");
        }
    },

    /**
     * 绑定设备 - 完全按照原版还原
     * @param {string} phone - 用户手机号
     * @param {string} deviceId - 设备ID
     */
    bindDevice: function (phone, deviceId) {
        try {
            var that = this;

            // 显示加载提示
            this.showToast("正在绑定设备...");

            // 调试Token状态
            console.log("========== 设备绑定前Token调试 ==========");
            global.NetworkModule.debugTokenStatus();

            // 调用API绑定设备
            global.NetworkModule.bindDevice(phone, deviceId, function (error, result) {
                if (error) {
                    that.showToast("绑定设备失败: " + error.message);
                    global.LogModule.log("绑定设备失败: " + error.message, "ERROR");
                    return;
                }

                if (result && result.code === 200) {
                    if (result.data) {
                        that.showToast("设备绑定成功");
                    } else {
                        that.showToast("设备已绑定，无需重复绑定");
                        global.LogModule.log("设备已绑定，无需重复绑定", "INFO");
                    }
                } else {
                    var errorMsg = result && result.message ? result.message : "绑定设备失败";
                    that.showToast(errorMsg);
                    global.LogModule.log("绑定设备响应错误: " + errorMsg, "ERROR");
                }
            });
        } catch (e) {
            global.LogModule.log("执行绑定设备操作失败: " + e.message, "ERROR");
            this.showToast("绑定设备出错");
        }
    },

    /**
     * 手机号脱敏显示 - 完全按照原版还原
     */
    maskPhone: function (phone) {
        if (!phone || phone.length < 11) {
            return phone || "未知";
        }
        return phone.substring(0, 3) + "****" + phone.substring(7);
    },

    /**
     * 获取并显示公告弹框
     */
    fetchAndShowAnnouncement: function () {
        try {
            var that = this;
            console.log("开始获取公告配置信息");

            // 先尝试从/announcement/enabled获取完整配置
            if (global.NetworkModule && global.NetworkModule.getAnnouncementConfig) {
                global.NetworkModule.getAnnouncementConfig(function (error, result) {
                    if (error) {
                        global.LogModule.log("获取公告配置失败: " + error.message, "ERROR");
                        // 降级到/announcement/latest接口
                        that.fallbackToLatestAnnouncement();
                    } else {
                        if (result && result.code === 200 && result.data) {
                            console.log("获取到公告配置: " + JSON.stringify(result.data));

                            // 保存QQ群信息供后续使用
                            if (result.data.qqGroup) {
                                that.currentQQGroup = result.data.qqGroup;
                                console.log("保存QQ群号: " + that.currentQQGroup);
                            }

                            // 显示公告内容
                            var content = result.data.content || "欢迎使用脚本助手！\n\n感谢您的使用，如有问题请联系客服。";
                            that.showAnnouncementDialog(content);
                            global.LogModule.log("显示公告弹框: " + content);
                        } else {
                            console.log("公告配置数据格式异常，降级到latest接口");
                            that.fallbackToLatestAnnouncement();
                        }
                    }
                });
            } else {
                console.log("AnnouncementConfig接口不可用，降级到latest接口");
                that.fallbackToLatestAnnouncement();
            }
        } catch (e) {
            global.LogModule.log("获取公告异常: " + e.message, "ERROR");
            console.error("获取公告异常: " + e.message);
            this.fallbackToLatestAnnouncement();
        }
    },

    /**
     * 降级到最新公告接口
     */
    fallbackToLatestAnnouncement: function() {
        try {
            var that = this;
            console.log("降级使用/announcement/latest接口");

            if (global.NetworkModule && global.NetworkModule.getLatestAnnouncement) {
                global.NetworkModule.getLatestAnnouncement(function (error, result) {
                    if (error) {
                        global.LogModule.log("获取最新公告失败: " + error.message, "ERROR");
                        that.showAnnouncementDialog("欢迎使用脚本助手！\n\n感谢您的使用，如有问题请联系客服。");
                    } else {
                        if (result && result.code === 200 && result.data && result.data.content) {
                            console.log("从latest接口获取到公告内容: " + result.data.content);
                            that.showAnnouncementDialog(result.data.content);
                        } else {
                            that.showAnnouncementDialog("欢迎使用脚本助手！\n\n感谢您的使用，如有问题请联系客服。");
                        }
                    }
                });
            } else {
                console.log("所有公告接口都不可用，显示默认公告");
                that.showAnnouncementDialog("欢迎使用脚本助手！\n\n感谢您的使用，如有问题请联系客服。");
            }
        } catch (e) {
            console.error("降级获取公告失败: " + e.message);
            this.showAnnouncementDialog("欢迎使用脚本助手！\n\n感谢您的使用，如有问题请联系客服。");
        }
    },

    /**
     * 显示公告弹框
     */
    showAnnouncementDialog: function(content) {
        try {
            var that = this;
            console.log("显示公告弹框");

            // 创建公告弹框布局
            var announcementDialogXml =
                '<frame>' +
                '<vertical gravity="center" bg="#80000000">' + // 半透明背景
                '<card cardCornerRadius="12dp" cardElevation="8dp" margin="20">' +
                '<vertical bg="#ffffff" padding="0">' +

                // 标题栏
                '<horizontal bg="#009688" padding="16 12" gravity="center_vertical">' +
                '<img src="@android:drawable/ic_dialog_info" w="24" h="24" tint="#ffffff" marginRight="8"/>' +
                '<text text="系统公告" textSize="18sp" textColor="#ffffff" textStyle="bold" layout_weight="1"/>' +
                '</horizontal>' +

                // 内容区域
                '<scroll h="200dp" margin="16 16 16 8">' +
                '<text id="announcementContent" text="' + content + '" textSize="15sp" textColor="#333333" lineSpacingExtra="4dp"/>' +
                '</scroll>' +

                // 按钮区域
                '<horizontal padding="16 8 16 16" gravity="right">' +
                '<button id="joinQQGroupBtn" text="加入QQ群" textSize="14sp" style="Widget.AppCompat.Button.Borderless" textColor="#009688" marginRight="8"/>' +
                '<button id="closeAnnouncementBtn" text="知道了" textSize="14sp" style="Widget.AppCompat.Button.Borderless" textColor="#009688"/>' +
                '</horizontal>' +

                '</vertical>' +
                '</card>' +
                '</vertical>' +
                '</frame>';

            // 在UI线程中创建弹框
            ui.run(function() {
                try {
                    // 创建弹框UI
                    ui.layout(announcementDialogXml);

                    // 绑定关闭按钮事件
                    if (ui.closeAnnouncementBtn) {
                        ui.closeAnnouncementBtn.on("click", function() {
                            console.log("用户点击关闭公告弹框");
                            that.closeAnnouncementDialog();
                        });
                    }

                    // 绑定QQ群按钮事件
                    if (ui.joinQQGroupBtn) {
                        ui.joinQQGroupBtn.on("click", function() {
                            console.log("用户点击加入QQ群");
                            that.handleJoinQQGroup();
                        });
                    }

                    console.log("公告弹框创建成功");

                } catch (uiError) {
                    console.error("创建公告弹框UI失败: " + uiError.message);
                    // 降级到简单toast
                    that.showToast("欢迎使用脚本助手！");
                }
            });

        } catch (e) {
            console.error("显示公告弹框失败: " + e.message);
            global.LogModule.log("显示公告弹框失败: " + e.message, "ERROR");
            // 降级到简单toast
            this.showToast("欢迎使用脚本助手！");
        }
    },

    /**
     * 关闭公告弹框
     */
    closeAnnouncementDialog: function() {
        try {
            console.log("关闭公告弹框");
            // 返回到脚本中心界面
            this.createScriptCenterUI(false);
        } catch (e) {
            console.error("关闭公告弹框失败: " + e.message);
        }
    },

    /**
     * 处理加入QQ群
     */
    handleJoinQQGroup: function() {
        try {
            console.log("处理加入QQ群请求");
            var that = this;

            // 优先使用已保存的QQ群号（从/announcement/enabled获取）
            if (this.currentQQGroup) {
                console.log("使用已保存的QQ群号: " + this.currentQQGroup);
                this.jumpToQQGroup(this.currentQQGroup);
                return;
            }

            // 如果没有保存的QQ群号，重新获取公告配置
            if (global.NetworkModule && global.NetworkModule.getAnnouncementConfig) {
                global.NetworkModule.getAnnouncementConfig(function(error, result) {
                    if (error || !result || result.code !== 200 || !result.data) {
                        console.log("获取公告配置失败，使用默认群号");
                        that.jumpToQQGroup("123456789"); // 默认群号
                    } else {
                        console.log("从公告配置获取到QQ群信息: " + JSON.stringify(result.data));
                        var qqGroupNumber = result.data.qqGroup || "123456789";
                        console.log("使用公告配置中的QQ群号: " + qqGroupNumber);
                        // 保存QQ群号供下次使用
                        that.currentQQGroup = qqGroupNumber;
                        that.jumpToQQGroup(qqGroupNumber);
                    }
                });
            } else {
                // 如果没有API，使用默认群号
                console.log("NetworkModule不可用，使用默认QQ群号");
                this.jumpToQQGroup("123456789");
            }

        } catch (e) {
            console.error("处理QQ群加入失败: " + e.message);
            this.jumpToQQGroup("123456789");
        }
    },

    /**
     * 跳转到QQ群
     */
    jumpToQQGroup: function(qqGroupNumber) {
        try {
            console.log("尝试跳转到QQ群: " + qqGroupNumber);

            // 先关闭公告弹框
            this.closeAnnouncementDialog();

            // 尝试跳转到QQ群
            try {
                // 方法1：尝试通过QQ协议直接跳转
                var qqIntent = app.intent({
                    action: "android.intent.action.VIEW",
                    data: "mqqopensdkapi://bizAgent/qm/qr?url=http%3A%2F%2Fqm.qq.com%2Fcgi-bin%2Fqm%2Fqr%3Ffrom%3Dapp%26p%3Dandroid%26k%3D" + qqGroupNumber
                });
                app.startActivity(qqIntent);
                console.log("通过QQ协议跳转群聊成功");
                this.showToast("正在跳转到QQ群...");

            } catch (intentError) {
                console.log("QQ协议跳转失败，尝试其他方式: " + intentError.message);

                // 方法2：尝试通过浏览器打开QQ群链接
                try {
                    var browserIntent = app.intent({
                        action: "android.intent.action.VIEW",
                        data: "https://qm.qq.com/cgi-bin/qm/qr?k=" + qqGroupNumber + "&jump_from=webapi"
                    });
                    app.startActivity(browserIntent);
                    console.log("通过浏览器跳转QQ群成功");
                    this.showToast("正在通过浏览器跳转到QQ群...");

                } catch (browserError) {
                    console.log("浏览器跳转也失败，复制群号: " + browserError.message);

                    // 方法3：复制群号到剪贴板作为最后的降级方案
                    if (typeof setClip !== 'undefined') {
                        setClip(qqGroupNumber);
                        this.showToast("QQ群号已复制: " + qqGroupNumber + "，请手动搜索加入");
                        console.log("QQ群号已复制: " + qqGroupNumber);
                    } else {
                        this.showToast("QQ群号: " + qqGroupNumber + "，请手动搜索加入");
                    }
                }
            }

        } catch (e) {
            console.error("跳转QQ群失败: " + e.message);
            this.showToast("QQ群号: " + qqGroupNumber + "，请手动搜索加入");
        }
    },

    /**
     * 创建微信阅读脚本配置页面 - 完全按照原版还原
     */
    createWxReadConfigPage: function () {
        try {
            global.LogModule.log("创建微信阅读配置页面", "INFO");
            var that = this;

            // 微信阅读配置页面布局
            var wxReadConfigLayoutXml =
                '<frame>' +
                '<vertical h="*" w="*" bg="#f5ffe0">' +
                '<horizontal gravity="center_vertical" padding="16" bg="#009688">' +
                '<img src="@android:drawable/ic_menu_revert" w="24" h="24" tint="#ffffff" marginRight="16" id="wxReadBackBtn"/>' +
                '<text text="微信阅读" textColor="#ffffff" textSize="18sp" layout_weight="1"/>' +
                '</horizontal>' +

                '<scroll layout_weight="1">' +
                '<vertical padding="16">' +

                // 文章滑动配置
                '<card cardCornerRadius="8dp" cardElevation="2dp" margin="0 0 0 16" bg="#ffff8a">' +
                '<text text="文章滑动配置" textSize="16sp" textColor="#333333" padding="12 8" textStyle="bold"/>' +
                '</card>' +

                '<vertical margin="0 0 16 0">' +
                '<horizontal gravity="center_vertical" padding="12">' +
                '<text text="每篇文章滑动" textSize="14sp" textColor="#333333" layout_weight="1"/>' +
                '<input id="wxReadScrollCount" text="8" inputType="number" textSize="14sp" w="80dp" gravity="center" bg="#ffffff" padding="8"/>' +
                '<text text="次" textSize="14sp" textColor="#333333" marginLeft="8"/>' +
                '</horizontal>' +

                '<horizontal gravity="center_vertical" padding="12">' +
                '<text text="每次滑动后停留" textSize="14sp" textColor="#333333" layout_weight="1"/>' +
                '<input id="wxReadScrollDelay" text="3" inputType="number" textSize="14sp" w="80dp" gravity="center" bg="#ffffff" padding="8"/>' +
                '<text text="秒" textSize="14sp" textColor="#333333" marginLeft="8"/>' +
                '</horizontal>' +

                '<horizontal gravity="center_vertical" padding="12">' +
                '<text text="点赞概率" textSize="14sp" textColor="#333333" layout_weight="1"/>' +
                '<input id="wxReadLikeProbability" text="20" inputType="number" textSize="14sp" w="80dp" gravity="center" bg="#ffffff" padding="8"/>' +
                '<text text="%" textSize="14sp" textColor="#333333" marginLeft="8"/>' +
                '</horizontal>' +

                '<horizontal gravity="center_vertical" padding="12">' +
                '<text text="喜欢概率" textSize="14sp" textColor="#333333" layout_weight="1"/>' +
                '<input id="wxReadFavoriteProbability" text="20" inputType="number" textSize="14sp" w="80dp" gravity="center" bg="#ffffff" padding="8"/>' +
                '<text text="%" textSize="14sp" textColor="#333333" marginLeft="8"/>' +
                '</horizontal>' +
                '</vertical>' +

                // 启动选项
                '<card cardCornerRadius="8dp" cardElevation="2dp" margin="0 0 0 16" bg="#ffff8a">' +
                '<text text="启动选项" textSize="16sp" textColor="#333333" padding="12 8" textStyle="bold"/>' +
                '</card>' +

                '<vertical margin="0 0 16 0">' +
                '<horizontal gravity="center_vertical" padding="12">' +
                '<checkbox id="wxReadSimulateHuman" text="是否模拟人工进行滑动" textSize="14sp" textColor="#333333" checked="true"/>' +
                '</horizontal>' +
                '</vertical>' +

                // 启动按钮
                '<button id="wxReadStartBtn" text="启动脚本" textSize="16sp" textColor="#ffffff" bg="#009688" margin="16" padding="16" textStyle="bold"/>' +

                // 启动提示文字
                '<text text="点击启动后会自动打开微信，请手动进入到第一篇文章页面，然后点击开始。" textSize="12sp" textColor="#666666" margin="16 0 16 16" gravity="center"/>' +

                '</vertical>' +
                '</scroll>' +
                '</vertical>' +
                '</frame>';

            // 创建并显示页面
            ui.layout(wxReadConfigLayoutXml);

            // 为输入框添加数字验证
            this.setupNumberInputValidation();

            // 绑定返回按钮事件
            ui.wxReadBackBtn.on("click", function () {
                // 检查是否有正在运行的微信阅读脚本
                if (that.wxReadControlWindow || that.wxReadScriptRunning) {
                    console.log("检测到微信阅读脚本正在运行，执行清理操作");
                    that.stopWxReadScriptAndCleanup();
                }
                // 返回脚本中心页面
                that.createScriptCenterUI(false);
            });

            // 绑定启动按钮事件
            ui.wxReadStartBtn.on("click", function () {
                that.startWxReadScript();
            });

            this.setCurrentPage("wxReadConfig");

            // 添加页面退出监听器
            this.setupWxReadConfigPageExitListener();

        } catch (e) {
            console.error("创建微信阅读配置页面失败: " + e.message);
            global.LogModule.log("创建微信阅读配置页面失败: " + e.message, "ERROR");
            this.showToast("页面创建失败，请重试");
        }
    },

    /**
     * 设置数字输入验证 - 完全按照原版还原
     */
    setupNumberInputValidation: function () {
        // 为数字输入框添加验证
        var numberInputs = ["wxReadScrollCount", "wxReadScrollDelay", "wxReadLikeProbability", "wxReadFavoriteProbability"];
        numberInputs.forEach(function (inputId) {
            if (ui[inputId]) {
                ui[inputId].on("text_changed", function (text) {
                    // 限制只能输入数字
                    var numericValue = text.replace(/[^0-9]/g, '');
                    if (numericValue !== text) {
                        ui[inputId].setText(numericValue);
                    }
                });
            }
        });
    },

    /**
     * 启动微信阅读脚本 - 完全按照原版还原
     */
    startWxReadScript: function () {
        try {
            var that = this;

            // 检查脚本是否已经启动（控制按钮是否已创建）
            if (that.wxReadControlWindow) {
                that.showToast("微信阅读脚本已启动，请使用悬浮控制按钮进行操作");
                return;
            }

            // 获取配置参数
            var scrollCount = parseInt(ui.wxReadScrollCount.text()) || 8;
            var scrollDelay = parseInt(ui.wxReadScrollDelay.text()) || 3;
            var likeProbability = parseInt(ui.wxReadLikeProbability.text()) || 20;
            var favoriteProbability = parseInt(ui.wxReadFavoriteProbability.text()) || 20;
            var simulateHuman = ui.wxReadSimulateHuman.checked;

            // 参数验证
            if (scrollCount < 1 || scrollCount > 50) {
                that.showToast("每篇文章滑动次数应在1-50之间");
                return;
            }

            if (scrollDelay < 1 || scrollDelay > 30) {
                that.showToast("滑动停留时间应在1-30秒之间");
                return;
            }

            if (likeProbability < 0 || likeProbability > 100) {
                that.showToast("点赞概率应在0-100%之间");
                return;
            }

            if (favoriteProbability < 0 || favoriteProbability > 100) {
                that.showToast("喜欢概率应在0-100%之间");
                return;
            }

            // 保存配置参数到全局变量
            that.wxReadConfig = {
                scrollCount: scrollCount,
                scrollDelay: scrollDelay,
                likeProbability: likeProbability,
                favoriteProbability: favoriteProbability,
                simulateHuman: simulateHuman
            };

            // 显示配置信息
            var configInfo = "配置信息：\n" +
                "每篇文章滑动：" + scrollCount + "次\n" +
                "滑动停留时间：" + scrollDelay + "秒\n" +
                "点赞概率：" + likeProbability + "%\n" +
                "喜欢概率：" + favoriteProbability + "%\n" +
                "模拟人工滑动：" + (simulateHuman ? "是" : "否");

            console.log("微信阅读脚本配置：", configInfo);
            global.LogModule.log("微信阅读脚本启动，配置：" + configInfo, "INFO");

            // 显示确认对话框
            dialogs.build({
                title: "确认启动",
                content: configInfo + "\n\n确定要启动微信阅读脚本吗？",
                positive: "启动",
                negative: "取消"
            }).on("positive", function () {
                that.showToast("脚本启动中...");

                // 尝试打开微信
                try {
                    var wechatOpened = app.launchPackage("com.tencent.mm");
                    if (wechatOpened) {
                        console.log("成功打开微信");
                        that.showToast("微信已打开，请手动进入阅读页面");
                    } else {
                        console.log("无法自动打开微信");
                        that.showToast("请手动打开微信并进入阅读页面");
                    }
                } catch (e) {
                    console.error("打开微信失败: " + e.message);
                    that.showToast("请手动打开微信并进入阅读页面");
                }

                // 创建控制按钮界面
                that.createWxReadControlButtons();
            }).show();

        } catch (e) {
            console.error("启动微信阅读脚本失败: " + e.message);
            global.LogModule.log("启动微信阅读脚本失败: " + e.message, "ERROR");
            this.showToast("启动失败，请重试");
        }
    },

    /**
     * 创建微信阅读脚本控制按钮 - 完全按照原版还原
     */
    createWxReadControlButtons: function () {
        try {
            var that = this;

            // 初始化脚本状态
            that.wxReadScriptRunning = false;
            that.wxReadScriptPaused = false;
            that.wxReadConsoleVisible = false;
            that.isClick = false;

            // 创建悬浮控制按钮
            that.wxReadControlWindow = floaty.window(
                '<vertical>' +
                '<button id="wxReadStartStopBtn" text="开始" textSize="12sp" textColor="#ffffff" bg="#4CAF50" w="60dp" h="35dp" margin="2dp"/>' +
                '<button id="wxReadLogBtn" text="日志" textSize="12sp" textColor="#ffffff" bg="#2196F3" w="60dp" h="35dp" margin="2dp"/>' +
                '<button id="wxReadStopBtn" text="结束" textSize="12sp" textColor="#ffffff" bg="#F44336" w="60dp" h="35dp" margin="2dp"/>' +
                '</vertical>'
            );

            // 设置悬浮窗位置（左侧位置）
            that.wxReadControlWindow.setPosition(50, device.height / 2 - 100);

            // 绑定开始/停止按钮事件
            that.wxReadControlWindow.wxReadStartStopBtn.on("click", function () {
                if (!that.wxReadScriptRunning) {
                    // 开始脚本
                    that.wxReadScriptRunning = true;
                    that.wxReadControlWindow.wxReadStartStopBtn.setText("停止");
                    that.wxReadControlWindow.wxReadStartStopBtn.attr("bg", "#F44336");
                    toast("微信阅读脚本已开始");
                    // 启动脚本执行
                    that.startWxReadExecution();
                } else {
                    // 停止脚本
                    that.wxReadScriptRunning = false;
                    that.wxReadControlWindow.wxReadStartStopBtn.setText("开始");
                    that.wxReadControlWindow.wxReadStartStopBtn.attr("bg", "#4CAF50");
                    toast("微信阅读脚本已停止");
                }
            });

            // 绑定日志按钮事件
            that.wxReadControlWindow.wxReadLogBtn.on("click", function () {
                if (!that.wxReadConsoleVisible) {
                    // 在新线程中显示控制台，避免阻塞主线程
                    threads.start(function () {
                        try {
                            // 初次点击日志按钮时清空控制台
                            if (!that.isClick) {
                                console.clear();
                                that.isClick = true; // 标记已经点击过
                            }
                            console.show();
                            // 在主线程中更新UI
                            ui.run(function () {
                                that.wxReadControlWindow.wxReadLogBtn.setText("隐藏");
                                that.wxReadConsoleVisible = true;
                            });
                        } catch (e) {
                            console.error("显示控制台失败: " + e.message);
                        }
                    });
                } else {
                    // 隐藏控制台
                    console.hide();
                    that.wxReadControlWindow.wxReadLogBtn.setText("日志");
                    that.wxReadConsoleVisible = false;
                }
            });

            // 绑定结束按钮事件
            that.wxReadControlWindow.wxReadStopBtn.on("click", function () {
                that.stopWxReadScriptAndCleanup();
            });

            global.LogModule.log("微信阅读控制按钮创建成功");

        } catch (e) {
            console.error("创建微信阅读控制按钮失败: " + e.message);
            global.LogModule.log("创建微信阅读控制按钮失败: " + e.message, "ERROR");
        }
    },

    /**
     * 启动微信阅读脚本执行 - 完全按照原版还原
     */
    startWxReadExecution: function () {
        try {
            var that = this;
            console.log("========== 开始执行微信阅读脚本 ==========");
            global.LogModule.log("微信阅读脚本开始执行", "INFO");

            // 在新线程中执行脚本逻辑
            threads.start(function () {
                that.wxReadExecutionLoop();
            });

        } catch (e) {
            console.error("启动微信阅读脚本执行失败: " + e.message);
            global.LogModule.log("启动微信阅读脚本执行失败: " + e.message, "ERROR");
        }
    },

    /**
     * 微信阅读脚本执行循环 - 完全按照原版还原
     */
    wxReadExecutionLoop: function () {
        var that = this;

        try {
            var articleCount = 0; // 已处理的文章数量
            that.currentSwipeIndex = 0; // 当前滑动进度
            that.currentArticlePhase = 'swipe'; // 当前文章处理阶段：swipe, like, favorite, next

            while (that.wxReadScriptRunning) {
                // 检测是否还在微信应用中
                var currentPkg = currentPackage();
                if (currentPkg !== "com.tencent.mm") {
                    console.log("用户已退出微信应用，结束脚本");
                    that.stopWxReadScriptAndCleanup();
                    break;
                }

                console.log("检测是否在文章页面...");
                var isInArticlePage = that.isInWxReadArticlePage();
                if (!isInArticlePage) {
                    var checkPage = false;
                    for (var i = 0; i <= 3; i++) {
                        sleep(5000);
                        checkPage = that.isInWxReadArticlePage();
                        if (!checkPage) {
                            console.log("未检测到文章，请检查");
                        } else {
                            checkPage = true;
                            break;
                        }
                    }
                    if (checkPage == false) {
                        break;
                    }
                }

                // 点赞按钮
                var likeButton;
                // 喜欢按钮
                var favoriteButton;
                try {
                    // 寻找点赞按钮
                    likeButton = className("android.widget.Button").textContains("赞").findOne(6000);
                    // 喜欢按钮
                    favoriteButton = className("android.widget.Button").textContains("在看").findOne(6000);
                } catch (e) {
                    console.log("未识别到点赞或者喜欢按钮");
                }

                // 检查暂停状态
                while (that.wxReadScriptPaused && that.wxReadScriptRunning) {
                    console.log("脚本已暂停，等待恢复...");
                    sleep(1000);
                }

                // 检查是否仍在运行
                if (!that.wxReadScriptRunning) {
                    break;
                }

                // 如果是新文章，重置进度
                if (that.currentSwipeIndex === 0 && that.currentArticlePhase === 'swipe') {
                    articleCount++;
                    console.log("========== 开始阅读第 " + articleCount + " 篇文章 ==========");
                }

                // 执行滑动操作阶段
                if (that.currentArticlePhase === 'swipe') {
                    for (var i = that.currentSwipeIndex; i < that.wxReadConfig.scrollCount; i++) {
                        // 在每次滑动前检查暂停状态
                        if (!that.wxReadScriptRunning) {
                            return; // 脚本停止，直接退出
                        }

                        if (that.wxReadScriptPaused) {
                            that.currentSwipeIndex = i; // 记住当前进度
                            break; // 跳出滑动循环，等待恢复
                        }

                        if (that.wxReadConfig.simulateHuman) {
                            // 模拟人工滑动：从下往上的曲线滑动
                            console.log("准备执行人工模拟滑动 " + (i + 1) + "/" + that.wxReadConfig.scrollCount);
                            that.performHumanLikeSwipe();
                        } else {
                            // 普通滑动：直线滑动
                            try {
                                // 检查设备尺寸
                                var screenWidth = device.width;
                                var screenHeight = device.height;

                                var startY = screenHeight * 0.7;
                                var endY = screenHeight * 0.3;
                                var centerX = screenWidth / 2;
                                var duration = 500;

                                console.log("普通滑动参数: (" + centerX + "," + startY + ") -> (" + centerX + "," + endY + "), 持续时间: " + duration + "ms");
                                swipe(centerX, startY, centerX, endY, duration);
                                console.log("普通滑动执行成功");
                            } catch (swipeError) {
                                console.error("普通滑动执行失败: " + swipeError.message);
                            }
                        }

                        // 滑动后停留，期间也要检查暂停状态
                        var sleepTime = that.wxReadConfig.scrollDelay * 1000;
                        var sleepInterval = 500; // 每500ms检查一次暂停状态
                        for (var sleepCount = 0; sleepCount < sleepTime; sleepCount += sleepInterval) {
                            if (!that.wxReadScriptRunning || that.wxReadScriptPaused) {
                                that.currentSwipeIndex = i + 1; // 这次滑动已完成，下次从下一次开始
                                break;
                            }
                            sleep(Math.min(sleepInterval, sleepTime - sleepCount));
                        }

                        // 如果在停留期间被暂停，跳出循环
                        if (that.wxReadScriptPaused) {
                            break;
                        }
                    }

                    // 如果滑动阶段完成且未被暂停，进入下一阶段
                    if (!that.wxReadScriptPaused && that.currentSwipeIndex >= that.wxReadConfig.scrollCount) {
                        that.currentArticlePhase = 'like';
                        that.currentSwipeIndex = 0; // 重置滑动进度
                    }
                }

                // 模拟点赞操作（根据概率）
                if (that.currentArticlePhase === 'like' || that.currentArticlePhase === 'swipe') {
                    if (Math.random() * 100 < that.wxReadConfig.likeProbability) {
                        if (likeButton) {
                            try {
                                likeButton.click();
                                console.log("执行点赞操作");
                            } catch (e) {
                                console.log("点赞操作失败: " + e.message);
                            }
                        }
                        sleep(2000);
                    }
                }

                // 模拟收藏操作（根据概率）
                if (that.currentArticlePhase === 'like' || that.currentArticlePhase === 'swipe') {
                    if (Math.random() * 100 < that.wxReadConfig.favoriteProbability) {
                        if (favoriteButton) {
                            try {
                                favoriteButton.click();
                                console.log("执行收藏操作");
                            } catch (e) {
                                console.log("收藏操作失败: " + e.message);
                            }
                        }
                        sleep(2000);
                    }
                }

                // 进入下一篇文章
                back();

                // 重置文章处理状态
                that.currentSwipeIndex = 0;
                that.currentArticlePhase = 'swipe';

                // 文章间隔时间
                sleep(3000);
            }

        } catch (e) {
            console.error("微信阅读脚本执行循环异常: " + e.message);
            global.LogModule.log("微信阅读脚本执行循环异常: " + e.message, "ERROR");
        }
    },

    /**
     * 检测是否在微信阅读文章页面 - 完全按照原版还原
     */
    isInWxReadArticlePage: function () {
            try {
                // 检查当前应用包名
                var currentPkg = currentPackage();
                if (currentPkg !== "com.tencent.mm") {
                    return false;
                }

                // 检查是否在微信文章页面
                // 方法1: 检查是否有文章时间标识
                var timePattern = /\d{4}年\d{2}月\d{2}日\s+\d{2}:\d{2}/;
                var dateText = textMatches(timePattern).findOnce();
                if (dateText != null) {
                    return true;
                }

                // 方法2: 检查是否有点赞和在看按钮
                var likeButton = className("android.widget.Button").textContains("赞").findOnce(3000);
                var favoriteButton = className("android.widget.Button").textContains("在看").findOnce(3000);
                if (likeButton != null && favoriteButton != null) {
                    return true;
                }

                // 方法3: 检查是否有微信公众号文章的特征元素
                var articleTitle = textContains("微信公众平台").findOnce(2000);
                if (articleTitle != null) {
                    return true;
                }

                // 方法4: 检查是否有年月日相关的文本
                var yearText = textContains("年").findOnce(2000);
                var monthText = textContains("月").findOnce(2000);
                var dayText = textContains("日").findOnce(2000);
                if (yearText != null && monthText != null && dayText != null) {
                    return true;
                }

                return false;

            } catch (e) {
                console.error("检测微信阅读配置页面失败: " + e.message);
                return false;
            }
        },

    /**
     * 执行人工模拟滑动 - 完全按照原版还原
     */
    performHumanLikeSwipe: function () {
        try {
            // 检查设备尺寸
            var screenWidth = device.width;
            var screenHeight = device.height;


            // 模拟人工滑动：添加随机性和曲线轨迹
            var startX = screenWidth / 2 + (Math.random() - 0.5) * 100;
            var startY = screenHeight * 0.7 + (Math.random() - 0.5) * 50;
            var endX = screenWidth / 2 + (Math.random() - 0.5) * 100;
            var endY = screenHeight * 0.3 + (Math.random() - 0.5) * 50;
            var duration = 500 + Math.random() * 300; // 随机持续时间

            console.log("执行人工模拟滑动: (" + startX.toFixed(0) + "," + startY.toFixed(0) + ") -> (" + endX.toFixed(0) + "," + endY.toFixed(0) + ")");

            // 方法1：尝试使用gesture函数（单个手势）
            try {
                gesture(duration, [startX, startY], [endX, endY]);
                return;
            } catch (gestureError) {
                console.log("gesture函数失败: " + gestureError.message);
            }

            // 方法2：尝试使用swipe函数（更兼容）
            try {
                swipe(startX, startY, endX, endY, duration);
                console.log("使用swipe函数执行滑动成功");
                return;
            } catch (swipeError) {
                console.log("swipe函数失败: " + swipeError.message);
            }

            // 方法3：降级到基础滑动
            throw new Error("所有滑动方法都失败，使用降级方案");

        } catch (e) {
            console.log("人工模拟滑动失败: " + e.message);
            // 降级到普通滑动
            try {
                var startY = device.height * 0.7;
                var endY = device.height * 0.3;
                var centerX = device.width / 2;
                swipe(centerX, startY, centerX, endY, 500);
                console.log("降级滑动执行成功");
            } catch (fallbackError) {
                console.error("降级滑动也失败: " + fallbackError.message);
            }
        }
    },

    /**
     * 进入下一篇文章 - 完全按照原版还原
     */
    goToNextArticle: function () {
        try {
            console.log("尝试进入下一篇文章...");

            // 方法1：尝试点击下一篇按钮
            var nextButton = textContains("下一篇").findOne(2000);
            if (nextButton) {
                nextButton.click();
                sleep(3000);
                return;
            }

            // 方法2：尝试滑动到下一篇
            var startY = device.height * 0.8;
            var endY = device.height * 0.2;
            swipe(device.width / 2, startY, device.width / 2, endY, 800);
            sleep(2000);

            // 方法3：尝试点击屏幕右侧进入下一篇
            click(device.width * 0.8, device.height / 2);
            sleep(2000);

        } catch (e) {
            console.log("进入下一篇文章失败: " + e.message);
        }
    },

    /**
     * 停止微信阅读脚本并清理 - 完全按照原版还原
     */
    stopWxReadScriptAndCleanup: function () {
        try {
            global.LogModule.log("停止微信阅读脚本并清理");

            // 停止脚本运行
            this.wxReadScriptRunning = false;
            this.wxReadScriptPaused = false;

            // 清理悬浮控制窗口
            if (this.wxReadControlWindow) {
                try {
                    this.wxReadControlWindow.close();
                    console.log("悬浮控制窗口已关闭");
                } catch (e) {
                    console.error("关闭悬浮控制窗口失败: " + e.message);
                }
                this.wxReadControlWindow = null;
            }

            // 隐藏控制台
            if (this.wxReadConsoleVisible) {
                try {
                    console.hide();
                    this.wxReadConsoleVisible = false;
                } catch (e) {
                    console.error("隐藏控制台失败: " + e.message);
                }
            }

            // 清理脚本状态变量
            this.currentSwipeIndex = 0;
            this.currentArticlePhase = 'swipe';
            this.isClick = false;
            this.wxReadConfig = null;

            this.showToast("微信阅读脚本已停止");
            global.LogModule.log("微信阅读脚本清理完成");

        } catch (e) {
            console.error("停止微信阅读脚本失败: " + e.message);
            global.LogModule.log("停止微信阅读脚本失败: " + e.message, "ERROR");
        }
    },

    /**
     * 设置微信阅读配置页面退出监听器 - 完全按照原版还原
     */
    setupWxReadConfigPageExitListener: function () {
        try {
            var that = this;

            // 监听应用退出事件
            events.on("exit", function () {
                console.log("检测到应用退出事件");
                if (that.getCurrentPage() === "wxReadConfig") {
                    console.log("当前在微信阅读配置页面，执行清理");
                    that.stopWxReadScriptAndCleanup();
                }
            });

            // 监听按键事件（返回键）
            events.on("key_down", function (keyCode, event) {
                if (keyCode === keys.back && that.getCurrentPage() === "wxReadConfig") {
                    console.log("检测到返回键，清理微信阅读脚本");
                    that.stopWxReadScriptAndCleanup();
                    // 清理完成后返回到脚本中心页面
                    setTimeout(function() {
                        if (typeof global !== 'undefined' && global.UIModule && global.UIModule.createScriptCenterUI) {
                            console.log("返回到脚本中心页面");
                            global.UIModule.createScriptCenterUI(false);
                        }
                    }, 500);
                }
            });

            // 物理返回键事件处理
            ui.emitter.on("back_pressed", function(e) {
                if (that.getCurrentPage() === "wxReadConfig") {
                    console.log("微信阅读页面检测到物理返回键");
                    e.consumed = true; // 阻止默认返回行为
                    that.stopWxReadScriptAndCleanup();
                    // 清理完成后返回到脚本中心页面
                    setTimeout(function() {
                        if (typeof global !== 'undefined' && global.UIModule && global.UIModule.createScriptCenterUI) {
                            console.log("返回到脚本中心页面");
                            global.UIModule.createScriptCenterUI(false);
                        }
                    }, 500);
                }
            });

            // 设置页面可见性监听
            ui.emitter.on("pause", function () {
                console.log("应用进入后台");
                if (that.getCurrentPage() === "wxReadConfig" && that.wxReadControlWindow) {
                    // 应用进入后台时不清理，保持脚本运行
                    console.log("应用后台运行，微信阅读脚本继续执行");
                }
            });

            ui.emitter.on("resume", function () {
                console.log("应用恢复前台");
            });

        } catch (e) {
            console.error("设置微信阅读配置页面退出监听器失败: " + e.message);
            global.LogModule.log("设置微信阅读配置页面退出监听器失败: " + e.message, "ERROR");
        }
    },

    /**
     * 更新用户信息显示 - 完全按照原版还原
     */
    updateUserInfo: function () {
        try {
            // 更新我的页面中的用户信息
            if (ui.accountInfoText && currentUser) {
                var accountText = "我的账户: " + (currentUser.phone || "未知");
                ui.accountInfoText.setText(accountText);
            }

            if (ui.referrerInfoText) {
                var referrerText = "上级: " + (currentUser && (currentUser.referrerId || currentUser.referrer) ? (currentUser.referrerId || currentUser.referrer) : "无");
                ui.referrerInfoText.setText(referrerText);
            }

            if (ui.deviceIdText) {
                var deviceId = device.getAndroidId();
                ui.deviceIdText.setText("设备码: " + deviceId);
            }

            if (ui.invitationCodeText && currentUser) {
                var invitationCode = currentUser.invitationCode || currentUser.id || "未知";
                ui.invitationCodeText.setText("邀请码: " + invitationCode);
            }

            if (ui.expiryDateText) {
                var expiryDate = currentUser && currentUser.expiryDate ? currentUser.expiryDate : "2025.7.1";
                ui.expiryDateText.setText(expiryDate);
            }

        } catch (e) {
            global.LogModule.log("更新用户信息失败: " + e.message, "ERROR");
            console.error("更新用户信息失败: " + e.message);
        }
    },

    // 权限管理函数 - 完全按照原始版本
    checkAccessibilityPermission: function () {
        try {
            return auto.service != null;
        } catch (e) {
            return false;
        }
    },

    isAccessibilityServiceEnabled: function () {
        return this.checkAccessibilityPermission();
    },

    requestAccessibilityPermission: function () {
        try {
            auto();
            this.showToast("请在无障碍设置中开启脚本助手");
        } catch (e) {
            this.showAccessibilitySettingsDialog();
        }
    },

    showAccessibilitySettingsDialog: function () {
        try {
            dialogs.confirm("需要无障碍权限",
                "脚本助手需要无障碍服务权限才能正常工作。\n\n" +
                "无障碍服务用于:\n" +
                "• 自动点击和滑动\n" +
                "• 读取屏幕内容\n" +
                "• 执行自动化任务\n\n" +
                "是否前往设置开启？"
            ).then(confirmed => {
                if (confirmed) {
                    try {
                        auto();
                        this.showToast("请在无障碍设置中开启脚本助手");
                    } catch (e) {
                        this.showToast("请手动前往设置开启无障碍服务");
                    }
                }
            });
        } catch (e) {
            this.showToast("请在设置中开启无障碍服务");
        }
    },

    checkFloatingWindowPermission: function () {
        try {
            return floaty.checkPermission();
        } catch (e) {
            return false;
        }
    },

    isFloatingWindowPermissionGranted: function () {
        return this.checkFloatingWindowPermission();
    },

    requestFloatingWindowPermission: function () {
        try {
            floaty.requestPermission();
            this.showToast("请在设置中开启悬浮窗权限");
        } catch (e) {
            this.showToast("请手动开启悬浮窗权限");
        }
    },

    checkAllRequiredPermissions: function () {
        return this.checkAccessibilityPermission() && this.checkFloatingWindowPermission();
    },

    // 验证函数
    isValidPhoneNumber: function (phone) {
        return /^1[3-9]\d{9}$/.test(phone);
    },

    isValidPassword: function (password) {
        return password && password.length >= 6;
    },

    // UI状态更新
    updateUIState: function () {
        try {
            // 更新权限开关状态
            if (ui.accessibilitySwitch) {
                ui.accessibilitySwitch.setChecked(this.checkAccessibilityPermission());
            }
            if (ui.floatingWindowSwitch) {
                ui.floatingWindowSwitch.setChecked(this.checkFloatingWindowPermission());
            }

            // 更新版本信息
            if (ui.appTitle && global.APP_INFO) {
                // 保持原始标题
            }
        } catch (e) {
            console.error("更新UI状态失败: " + e.message);
        }
    },

    /**
     * 创建提现页面 - 完全按照原版还原
     */
    createWithdrawalPage: function () {
        try {
            global.LogModule.log("创建提现页面");

            // 提现页面布局 - 简化版本
            var withdrawalPageLayoutXml =
                '<frame>' +
                '<vertical h="*" w="*">' +
                '<horizontal gravity="center_vertical" padding="16">' +
                '<img src="@android:drawable/ic_menu_revert" w="24" h="24" tint="#009688" marginRight="16" id="backBtn"/>' +
                '<text text="账户提现" textColor="#212121" textSize="18sp" layout_weight="1"/>' +
                '</horizontal>' +

                '<scroll id="mainScroll" layout_weight="1">' +
                '<vertical padding="16">' +
                '<card cardCornerRadius="8dp" cardElevation="2dp" margin="0 0 0 16">' +
                '<vertical padding="16">' +
                '<text text="可提现金额" textColor="#757575" textSize="14sp"/>' +
                '<text id="availableAmountText" text="0.00元" textColor="#212121" textSize="24sp" textStyle="bold" margin="0 4"/>' +
                '</vertical>' +
                '</card>' +

                '<text text="提现申请" textColor="#212121" textSize="16sp" margin="0 16 0 8"/>' +

                '<card cardCornerRadius="8dp" cardElevation="2dp" margin="0 8">' +
                '<vertical padding="16">' +
                '<horizontal gravity="center_vertical" margin="0 8">' +
                '<text text="提现金额" textColor="#212121" textSize="16sp" layout_weight="1"/>' +
                '</horizontal>' +

                '<vertical bg="#f5f5f5" padding="8" margin="0 8" radius="4dp">' +
                '<text text="已选择金额" textColor="#757575" textSize="12sp"/>' +
                '<horizontal gravity="center_vertical">' +
                '<text text="¥" textColor="#212121" textSize="16sp"/>' +
                '<text id="selectedAmountText" text="10.00" textColor="#212121" textSize="20sp" layout_weight="1"/>' +
                '</horizontal>' +
                '</vertical>' +

                '<horizontal gravity="center_vertical" margin="0 16 0 8">' +
                '<text text="支付宝账号" textColor="#212121" textSize="16sp" layout_weight="1"/>' +
                '</horizontal>' +
                '<input id="alipayAccountInput" hint="请输入支付宝账号" textSize="14sp" padding="8dp" margin="0 4" bg="#f5f5f5" h="45dp"/>' +

                '<horizontal gravity="center_vertical" margin="0 16 0 8">' +
                '<text text="支付宝实名" textColor="#212121" textSize="16sp" layout_weight="1"/>' +
                '</horizontal>' +
                '<input id="alipayNameInput" hint="请输入支付宝实名" textSize="14sp" padding="8dp" margin="0 4" bg="#f5f5f5" h="45dp"/>' +

                '<button id="applyWithdrawalBtn" text="立即提现" textSize="16sp" textColor="#ffffff" bg="#FF5722" margin="0 16 0 8" padding="8dp" h="48dp"/>' +
                '</vertical>' +
                '</card>' +

                '<text text="提现说明" textColor="#212121" textSize="16sp" margin="0 16 0 8"/>' +

                '<card cardCornerRadius="8dp" cardElevation="2dp" margin="0 8">' +
                '<vertical padding="16">' +
                '<text text="1. 提现金额最低10元，最高1000元" textColor="#666666" textSize="14sp" margin="0 4"/>' +
                '<text text="2. 提现申请提交后1-3个工作日到账" textColor="#666666" textSize="14sp" margin="0 4"/>' +
                '<text text="3. 请确保支付宝账号和实名信息正确" textColor="#666666" textSize="14sp" margin="0 4"/>' +
                '</vertical>' +
                '</card>' +

                '</vertical>' +
                '</scroll>' +
                '</vertical>' +
                '</frame>';

            // 创建并显示页面
            ui.layout(withdrawalPageLayoutXml);

            var that = this;

            // 绑定返回按钮事件
            ui.backBtn.on("click", function () {
                // 返回到脚本中心的"我的"页面
                that.createScriptCenterUI(false);
                // 切换到"我的"标签
                setTimeout(function () {
                    if (ui.myTabBtn) {
                        ui.myTabBtn.click();
                    }
                }, 100);
            });

            // 绑定提现按钮事件
            ui.applyWithdrawalBtn.on("click", function () {
                var alipayAccount = ui.alipayAccountInput.text();
                var alipayName = ui.alipayNameInput.text();

                if (!alipayAccount) {
                    that.showToast("请输入支付宝账号");
                    return;
                }

                if (!alipayName) {
                    that.showToast("请输入支付宝实名");
                    return;
                }

                that.showToast("提现功能开发中...");
            });

            global.LogModule.log("提现页面创建成功");

        } catch (e) {
            console.error("创建提现页面失败: " + e.message);
            global.LogModule.log("创建提现页面失败: " + e.message, "ERROR");
            this.showToast("页面创建失败，请重试");
        }
    },

    /**
     * 返回脚本中心并切换到"我的"标签
     */
    returnToScriptCenterMyTab: function () {
        try {
            // 返回到脚本中心页面
            this.createScriptCenterUI(false);

            // 切换到"我的"标签
            setTimeout(function () {
                if (ui.myTabBtn) {
                    ui.myTabBtn.click();
                }
            }, 100);
        } catch (e) {
            console.error("返回脚本中心失败: " + e.message);
            this.showToast("返回失败，请重试");
        }
    },

    /**
     * 直接显示"我的"页面（不经过脚本中心）
     */
    showMyPageDirectly: function () {
        try {
            console.log("直接显示我的页面");

            // 创建脚本中心UI但直接显示"我的"页面内容
            this.createScriptCenterUI(false);

            // 立即切换到"我的"标签，不需要延迟
            if (ui.myTabBtn) {
                // 直接触发"我的"标签的显示逻辑
                ui.scriptTabContent.attr("visibility", "gone");
                ui.myTabContent.attr("visibility", "visible");

                // 更新标签样式
                ui.scriptTabText.attr("textColor", "#9E9E9E");
                ui.myTabText.attr("textColor", "#009688");

                // 设置当前页面
                this.setCurrentPage("my");

                console.log("成功直接显示我的页面");
            } else {
                console.error("myTabBtn 不存在，使用备用方案");
                // 备用方案：使用原来的方法
                this.returnToScriptCenterMyTab();
            }
        } catch (e) {
            console.error("直接显示我的页面失败: " + e.message);
            // 降级到原来的方法
            this.returnToScriptCenterMyTab();
        }
    },

    // 工具函数
    showToast: function (message) {
        try {
            toast(message);
        } catch (e) {
            console.log("Toast: " + message);
        }
    }
};

// 设置UIModule为全局变量，以便在回调函数中访问
global.UIModule = UIModule;

// 检查登录状态
function checkLoginStatus() {
    try {
        // if (global.StorageModule) {
        //     var savedLoginStatus = global.StorageModule.get("isLoggedIn", false);
        //     var savedUser = global.StorageModule.get("currentUser", null);

        //     if (savedLoginStatus === true && savedUser) {
        //         isLoggedIn = true;
        //         currentUser = savedUser;
        //         global.LogModule.log("自动恢复登录状态: " + savedUser.phone, "INFO");

        //         // 跳转到脚本中心页面
        //         setTimeout(function() {
        //             UIModule.createScriptCenterUI(false);
        //         }, 500);
        //         return;
        //     }
        // }

        // 未登录，显示主界面
        UIModule.createMainUI();
    } catch (e) {
        console.error("检查登录状态失败: " + e.message);
        UIModule.createMainUI();
    }
}

// 主函数 - 应用启动入口
function main() {
    try {
        console.log("脚本助手主函数启动...");

        // 1. 初始化应用
        console.log("开始初始化应用...");

        // 2. 加载所有模块
        loadAllModules();

        // 3. 检查登录状态并显示相应界面
        console.log("检查登录状态...");
        checkLoginStatus();

        console.log("✓ 应用初始化完成");
        global.LogModule.log("脚本助手启动成功");

    } catch (e) {
        console.error("应用启动失败: " + e.message);
        console.error(e.stack);

        // 启动失败时显示基础界面
        try {
            UIModule.createMainUI();
        } catch (uiError) {
            console.error("创建基础界面也失败: " + uiError.message);
            toast("应用启动失败，请重试");
        }
    }
}

// 全局函数 - 显示提现页面
function showWithdrawalPage() {
    try {
        console.log("全局函数showWithdrawalPage被调用");
        global.LogModule.log("全局函数showWithdrawalPage被调用", "INFO");

        // 确保UIModule已定义
        if (typeof UIModule === 'undefined') {
            console.error("UIModule未定义");
            global.LogModule.log("UIModule未定义", "ERROR");
            toast("加载提现页面失败: UIModule未定义");
            return;
        }

        // 确保在UI线程中执行
        ui.run(function () {
            try {
                console.log("准备在UI线程中创建提现页面");
                global.LogModule.log("准备在UI线程中创建提现页面", "INFO");

                // 尝试创建提现页面
                UIModule.createWithdrawalPage();
                console.log("UIModule.createWithdrawalPage()调用完成 - 全局函数");
                global.LogModule.log("提现页面创建完成", "INFO");
            } catch (e) {
                console.error("UI线程中执行createWithdrawalPage失败: " + e.message);
                console.error(e.stack);
                global.LogModule.log("UI线程中执行createWithdrawalPage失败: " + e.message, "ERROR");
                global.LogModule.log(e.stack, "ERROR");
                toast("加载提现页面失败: " + e.message);
            }
        });
    } catch (e) {
        console.error("全局函数showWithdrawalPage执行失败: " + e.message);
        console.error(e.stack);
        global.LogModule.log("全局函数showWithdrawalPage执行失败: " + e.message, "ERROR");
        global.LogModule.log(e.stack, "ERROR");
        toast("加载提现页面失败: " + e.message);
    }
}

// 启动应用
console.log("=== 脚本助手启动完成 ===");
main();
