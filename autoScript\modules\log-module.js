/**
 * 日志模块
 * 负责处理应用的日志记录功能
 */

// 引入配置
if (typeof LOG_LEVEL === 'undefined') {
    // 如果常量未加载，使用默认值
    var LOG_LEVEL = {
        DEBUG: 0,
        INFO: 1,
        WARN: 2,
        ERROR: 3
    };
}

const LogModule = (function () {
    // 当前日志级别
    let currentLevel = LOG_LEVEL.DEBUG;
    
    // 日志文件路径
    const LOG_FILE_PATH = "/sdcard/脚本助手/logs/app.log";
    
    // 最大日志文件大小（字节）
    const MAX_LOG_FILE_SIZE = 5 * 1024 * 1024; // 5MB

    return {
        /**
         * 初始化日志模块
         */
        init: function () {
            try {
                // 确保日志目录存在
                let logDir = "/sdcard/脚本助手/logs";
                if (!files.exists(logDir)) {
                    files.createWithDirs(logDir + "/temp.txt");
                    files.remove(logDir + "/temp.txt");
                }
                
                console.log("日志模块初始化完成");
            } catch (e) {
                console.error("日志模块初始化失败: " + e.message);
            }
        },

        /**
         * 记录日志
         * @param {string} message - 日志消息
         * @param {string} level - 日志级别，默认为INFO
         */
        log: function (message, level) {
            level = level || "INFO";
            let logLevel = LOG_LEVEL[level] || LOG_LEVEL.INFO;

            // 当前日志级别低于配置的级别则不记录
            if (logLevel < currentLevel) {
                return;
            }

            // 获取当前时间
            let now = new Date();
            let timeString = now.toLocaleTimeString();

            // 格式化日志消息
            let logMessage = "[" + timeString + " GMT+08:00]" + message;

            // 打印到控制台
            switch (logLevel) {
                case LOG_LEVEL.ERROR:
                    console.error(logMessage);
                    break;
                case LOG_LEVEL.WARN:
                    console.warn(logMessage);
                    break;
                default:
                    console.log(logMessage);
            }

            // 写入日志文件
            this.writeToFile(logMessage, level);
        },

        /**
         * 写入日志到文件
         * @param {string} message - 日志消息
         * @param {string} level - 日志级别
         */
        writeToFile: function (message, level) {
            try {
                // 检查文件大小，如果过大则轮转
                if (files.exists(LOG_FILE_PATH)) {
                    let fileContent = files.read(LOG_FILE_PATH);
                    if (fileContent && fileContent.length > MAX_LOG_FILE_SIZE) {
                        this.rotateLogFile();
                    }
                }

                // 格式化完整的日志条目
                let timestamp = new Date().toISOString();
                let logEntry = timestamp + " [" + level + "] " + message + "\n";

                // 追加到日志文件
                files.append(LOG_FILE_PATH, logEntry);
            } catch (e) {
                // 日志写入失败时不影响主程序运行
                console.error("写入日志文件失败: " + e.message);
            }
        },

        /**
         * 轮转日志文件
         */
        rotateLogFile: function () {
            try {
                let backupPath = LOG_FILE_PATH + ".old";
                
                // 删除旧的备份文件
                if (files.exists(backupPath)) {
                    files.remove(backupPath);
                }
                
                // 将当前日志文件重命名为备份文件
                if (files.exists(LOG_FILE_PATH)) {
                    files.rename(LOG_FILE_PATH, backupPath);
                }
                
                console.log("日志文件已轮转");
            } catch (e) {
                console.error("日志文件轮转失败: " + e.message);
            }
        },

        /**
         * 设置日志级别
         * @param {string} level - 日志级别
         */
        setLevel: function (level) {
            if (LOG_LEVEL.hasOwnProperty(level)) {
                currentLevel = LOG_LEVEL[level];
                this.log("日志级别已设置为: " + level, "INFO");
            } else {
                this.log("无效的日志级别: " + level, "WARN");
            }
        },

        /**
         * 获取当前日志级别
         */
        getLevel: function () {
            for (let levelName in LOG_LEVEL) {
                if (LOG_LEVEL[levelName] === currentLevel) {
                    return levelName;
                }
            }
            return "INFO";
        },

        /**
         * 清理日志文件
         */
        clearLogs: function () {
            try {
                if (files.exists(LOG_FILE_PATH)) {
                    files.remove(LOG_FILE_PATH);
                }
                
                let backupPath = LOG_FILE_PATH + ".old";
                if (files.exists(backupPath)) {
                    files.remove(backupPath);
                }
                
                this.log("日志文件已清理", "INFO");
            } catch (e) {
                console.error("清理日志文件失败: " + e.message);
            }
        },

        /**
         * 获取日志文件内容
         * @param {number} lines - 获取最后几行，默认100行
         */
        getLogContent: function (lines) {
            try {
                if (!files.exists(LOG_FILE_PATH)) {
                    return "日志文件不存在";
                }
                
                let content = files.read(LOG_FILE_PATH);
                if (!content) {
                    return "日志文件为空";
                }
                
                if (lines && lines > 0) {
                    let logLines = content.split('\n');
                    let startIndex = Math.max(0, logLines.length - lines);
                    return logLines.slice(startIndex).join('\n');
                }
                
                return content;
            } catch (e) {
                return "读取日志文件失败: " + e.message;
            }
        }
    };
})();

// 导出模块（AutoX.js兼容方式）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LogModule;
} else {
    // AutoX.js环境 - 多种方式确保模块可用
    try {
        if (typeof global !== 'undefined') {
            global.LogModule = LogModule;
        }
        if (typeof window !== 'undefined') {
            window.LogModule = LogModule;
        }
        // 直接赋值到全局作用域
        eval('LogModule = LogModule');
    } catch (e) {
        console.warn("LogModule导出失败: " + e.message);
    }
}

// 立即验证导出是否成功
try {
    if (typeof LogModule !== 'undefined') {
        console.log("LogModule导出成功");
    } else {
        console.warn("LogModule导出失败");
    }
} catch (e) {
    console.warn("LogModule验证失败: " + e.message);
}
