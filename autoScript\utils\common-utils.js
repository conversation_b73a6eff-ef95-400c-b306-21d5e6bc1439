/**
 * 通用工具函数
 * 包含项目中使用的通用工具方法
 */

/**
 * 检测是否为系统文本（包括聊天气泡时间）
 */
function isSystemText(text) {
    if (!text) return true;

    // 系统文本特征
    var systemPatterns = [
        /^\d{1,2}:\d{2}$/,                    // 基础时间格式 12:34
        /^上午\d{1,2}:\d{2}$/,                // 上午时间 上午9:30
        /^下午\d{1,2}:\d{2}$/,                // 下午时间 下午2:15
        /^晚上\d{1,2}:\d{2}$/,                // 晚上时间 晚上6:49
        /^凌晨\d{1,2}:\d{2}$/,                // 凌晨时间 凌晨1:23
        /^\d{1,2}月\d{1,2}日$/,               // 日期格式 12月25日
        /^昨天|今天|星期|刚才|分钟前|小时前/,      // 时间相关
        /^[\d\s\-:：]+$/,                     // 纯数字符号
        /^[\.。，,！!？?]+$/,                  // 纯标点
        /撤回|重新编辑|查看详情|小程序/,          // 系统功能
        /微信|返回|语音通话|视频通话|拍摄|相册|表情/, // 界面元素
        /^[a-zA-Z]$/,                        // 单个字母
        /^[\u4e00-\u9fa5]$/,                 // 单个汉字
        /^\d{4}年\d{1,2}月\d{1,2}日$/,        // 完整日期 2024年1月1日
        /^周[一二三四五六日]$/,                // 星期 周一
        /^星期[一二三四五六日]$/               // 星期 星期一
    ];

    for (var i = 0; i < systemPatterns.length; i++) {
        if (systemPatterns[i].test(text)) {
            return true;
        }
    }

    return false;
}

/**
 * 检测是否在头像区域
 */
function isAvatarArea(bounds, chatRegion) {
    if (!bounds) return false;

    var relativeX = bounds.left;
    var width = bounds.right - bounds.left;
    var height = bounds.bottom - bounds.top;

    // 头像通常是正方形，位于消息两侧
    var isSquareish = Math.abs(width - height) < Math.min(width, height) * 0.3;
    var isSmall = width < 80 && height < 80;
    var isAtSide = relativeX < chatRegion.width * 0.15 || relativeX > chatRegion.width * 0.85;

    // 如果是类似正方形且在两侧，可能是头像
    if (isSquareish && isSmall && isAtSide) {
        return true;
    }

    return false;
}

/**
 * 检测是否为聊天气泡上方的时间显示
 */
function isChatBubbleTime(text, bounds, chatRegion) {
    if (!text || !bounds) return false;

    // 1. 首先检查文本是否符合时间格式
    var timePatterns = [
        /^\d{1,2}:\d{2}$/,                    // 12:34
        /^上午\d{1,2}:\d{2}$/,                // 上午9:30
        /^下午\d{1,2}:\d{2}$/,                // 下午2:15
        /^晚上\d{1,2}:\d{2}$/,                // 晚上6:49
        /^凌晨\d{1,2}:\d{2}$/,                // 凌晨1:23
        /^\d{1,2}月\d{1,2}日$/,               // 12月25日
        /^昨天|今天$/,                        // 昨天/今天
        /^周[一二三四五六日]$/,                // 周一
        /^星期[一二三四五六日]$/               // 星期一
    ];

    var isTimeFormat = false;
    for (var i = 0; i < timePatterns.length; i++) {
        if (timePatterns[i].test(text)) {
            isTimeFormat = true;
            break;
        }
    }

    if (!isTimeFormat) return false;

    // 2. 检查位置特征
    var relativeX = bounds.left;
    var centerX = relativeX + (bounds.right - bounds.left) / 2;
    var isNearCenter = Math.abs(centerX - chatRegion.width / 2) < chatRegion.width * 0.3;

    return isNearCenter;
}

/**
 * 清理文本数组，移除系统文本和重复内容
 */
function cleanTextArray(texts) {
    if (!texts || !Array.isArray(texts)) return [];

    var cleanTexts = [];
    var seenTexts = new Set();

    for (var i = 0; i < texts.length; i++) {
        var textObj = texts[i];
        if (!textObj || !textObj.text) continue;

        var text = textObj.text.trim();
        
        // 跳过空文本
        if (!text) continue;
        
        // 跳过系统文本
        if (isSystemText(text)) continue;
        
        // 跳过重复文本
        if (seenTexts.has(text)) continue;
        
        seenTexts.add(text);
        cleanTexts.push(textObj);
    }

    return cleanTexts;
}

/**
 * 格式化时间戳
 */
function formatTimestamp(timestamp) {
    if (!timestamp) timestamp = Date.now();
    
    var date = new Date(timestamp);
    var year = date.getFullYear();
    var month = String(date.getMonth() + 1).padStart(2, '0');
    var day = String(date.getDate()).padStart(2, '0');
    var hours = String(date.getHours()).padStart(2, '0');
    var minutes = String(date.getMinutes()).padStart(2, '0');
    var seconds = String(date.getSeconds()).padStart(2, '0');
    
    return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;
}

/**
 * 生成随机字符串
 */
function generateRandomString(length) {
    if (!length) length = 8;
    
    var chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    var result = '';
    
    for (var i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    
    return result;
}

/**
 * 延迟执行
 */
function delay(ms) {
    return new Promise(function(resolve) {
        setTimeout(resolve, ms);
    });
}

/**
 * 安全的JSON解析
 */
function safeJsonParse(jsonString, defaultValue) {
    try {
        return JSON.parse(jsonString);
    } catch (e) {
        return defaultValue || null;
    }
}

/**
 * 安全的JSON字符串化
 */
function safeJsonStringify(obj, defaultValue) {
    try {
        return JSON.stringify(obj);
    } catch (e) {
        return defaultValue || '{}';
    }
}

/**
 * 检查字符串是否为空
 */
function isEmpty(str) {
    return !str || str.trim().length === 0;
}

/**
 * 截断字符串
 */
function truncateString(str, maxLength) {
    if (!str) return '';
    if (str.length <= maxLength) return str;
    return str.substring(0, maxLength) + '...';
}

// 导出工具函数（AutoX.js兼容方式）
if (typeof module !== 'undefined' && module.exports) {
    // Node.js环境
    module.exports = {
        isSystemText,
        isAvatarArea,
        isChatBubbleTime,
        cleanTextArray,
        formatTimestamp,
        generateRandomString,
        delay,
        safeJsonParse,
        safeJsonStringify,
        isEmpty,
        truncateString
    };
} else {
    // AutoX.js环境 - 将函数添加到全局作用域
    global.isSystemText = isSystemText;
    global.isAvatarArea = isAvatarArea;
    global.isChatBubbleTime = isChatBubbleTime;
    global.cleanTextArray = cleanTextArray;
    global.formatTimestamp = formatTimestamp;
    global.generateRandomString = generateRandomString;
    global.delay = delay;
    global.safeJsonParse = safeJsonParse;
    global.safeJsonStringify = safeJsonStringify;
    global.isEmpty = isEmpty;
    global.truncateString = truncateString;
}
