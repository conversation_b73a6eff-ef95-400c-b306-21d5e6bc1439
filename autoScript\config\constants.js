/**
 * 应用配置常量
 * 包含所有应用级别的配置信息
 */

// API配置
const API_CONFIG = {
    BASE_URL: "http://192.168.1.19:8527/api",
    TIMEOUT: 10000,
    RETRY_COUNT: 3
};

// 应用版本信息
const APP_INFO = {
    VERSION: "1.0.0",
    VERSION_CODE: 100,
    APP_NAME: "脚本助手"
};

// 更新状态常量
const UPDATE_STATUS = {
    CHECKING: 0,        // 检查中
    DOWNLOADING: 1,     // 下载中
    INSTALLING: 2,      // 安装中
    SUCCESS: 3,         // 成功
    FAILED: 4,          // 失败
    NO_UPDATE: 5        // 无更新
};

// 日志级别
const LOG_LEVEL = {
    DEBUG: 0,
    INFO: 1,
    WARN: 2,
    ERROR: 3
};

// 存储路径配置
const STORAGE_CONFIG = {
    ROOT_DIR: "/sdcard/脚本助手",
    STORAGE_DIR: "/sdcard/脚本助手/storage",
    UPDATES_DIR: "/sdcard/脚本助手/updates",
    LOGS_DIR: "/sdcard/脚本助手/logs"
};

// 月光宝盒配置
const MOONBOX_CONFIG = {
    DEFAULT_FUNCTION: "read",
    DEFAULT_QUANTITY: 0,
    DEFAULT_SWIPE_COUNT: 5,
    DEFAULT_INTERVAL: 3000,
    DEFAULT_DURATION: 30
};

// UI配置
const UI_CONFIG = {
    TOAST_DURATION: 2000,
    DIALOG_TIMEOUT: 30000,
    ANIMATION_DURATION: 300
};

// 网络请求配置
const NETWORK_CONFIG = {
    MAX_RETRIES: 3,
    RETRY_DELAY: 1000,
    TIMEOUT: 10000
};

// 权限配置
const PERMISSION_CONFIG = {
    REQUIRED_PERMISSIONS: [
        "android.permission.WRITE_EXTERNAL_STORAGE",
        "android.permission.READ_EXTERNAL_STORAGE",
        "android.permission.INTERNET",
        "android.permission.ACCESS_NETWORK_STATE"
    ]
};

// 导出配置（AutoX.js兼容方式）
if (typeof module !== 'undefined' && module.exports) {
    // Node.js环境
    module.exports = {
        API_CONFIG,
        APP_INFO,
        UPDATE_STATUS,
        LOG_LEVEL,
        STORAGE_CONFIG,
        MOONBOX_CONFIG,
        UI_CONFIG,
        NETWORK_CONFIG,
        PERMISSION_CONFIG
    };
} else {
    // AutoX.js环境 - 直接赋值到全局
    try {
        // 尝试多种方式确保全局可用
        if (typeof global !== 'undefined') {
            global.API_CONFIG = API_CONFIG;
            global.APP_INFO = APP_INFO;
            global.UPDATE_STATUS = UPDATE_STATUS;
            global.LOG_LEVEL = LOG_LEVEL;
            global.STORAGE_CONFIG = STORAGE_CONFIG;
            global.MOONBOX_CONFIG = MOONBOX_CONFIG;
            global.UI_CONFIG = UI_CONFIG;
            global.NETWORK_CONFIG = NETWORK_CONFIG;
            global.PERMISSION_CONFIG = PERMISSION_CONFIG;
        }

        // 直接赋值（最兼容的方式）
        eval('API_CONFIG = API_CONFIG');
        eval('APP_INFO = APP_INFO');
        eval('UPDATE_STATUS = UPDATE_STATUS');
        eval('LOG_LEVEL = LOG_LEVEL');
        eval('STORAGE_CONFIG = STORAGE_CONFIG');
        eval('MOONBOX_CONFIG = MOONBOX_CONFIG');
        eval('UI_CONFIG = UI_CONFIG');
        eval('NETWORK_CONFIG = NETWORK_CONFIG');
        eval('PERMISSION_CONFIG = PERMISSION_CONFIG');

    } catch (e) {
        console.warn("配置导出失败: " + e.message);
    }
}
