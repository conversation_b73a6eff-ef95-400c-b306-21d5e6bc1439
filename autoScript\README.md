# 脚本助手 - 模块化版本

基于AutoX.js开发的安卓脚本软件，采用模块化架构设计。

## 项目结构

```
autoScript/
├── main.js                 # 主入口文件
├── config/                 # 配置文件目录
│   └── constants.js        # 应用常量配置
├── utils/                  # 通用工具函数目录
│   └── common-utils.js     # 通用工具函数
├── modules/                # 功能模块目录
│   ├── log-module.js       # 日志模块
│   ├── storage-module.js   # 存储模块
│   ├── network-module.js   # 网络模块
│   ├── app-update-module.js # 应用更新模块
│   ├── ui-module.js        # UI界面模块
│   ├── main-module.js      # 主控制模块
│   ├── moonbox-module.js   # 月光宝盒模块
│   ├── daily-task-module.js # 每日任务模块
│   ├── agent-sales-module.js # 代理分销模块
│   ├── my-points-module.js # 我的积分模块
│   ├── points-exchange-module.js # 积分兑换模块
│   ├── promotion-rank-module.js # 推广排名模块
│   ├── about-us-module.js  # 关于我们模块
│   ├── tutorial-module.js  # 使用教程模块
│   └── withdrawal-module.js # 账户提现模块
└── README.md               # 项目说明文档
```

## 模块说明

### 配置模块 (config/)

#### constants.js
- 包含所有应用级别的配置常量
- API配置、版本信息、更新状态等
- 支持AutoX.js和Node.js环境

### 工具模块 (utils/)

#### common-utils.js
- 通用工具函数集合
- 文本处理、时间格式化、JSON操作等
- 系统文本检测、头像区域判断等专用函数

### 功能模块 (modules/)

#### log-module.js
- 日志记录和管理
- 支持多级别日志（DEBUG、INFO、WARN、ERROR）
- 自动日志文件轮转
- 控制台和文件双重输出

#### storage-module.js
- 数据持久化存储
- 支持键值对存储和文件存储
- 自动JSON序列化/反序列化
- 存储统计和管理功能

#### network-module.js
- 网络请求处理
- 支持GET、POST、PUT、DELETE请求
- 自动重试机制
- 文件下载功能

#### app-update-module.js
- 应用版本更新管理
- 自动检查更新
- 下载和安装更新
- 更新状态报告

#### ui-module.js
- 用户界面管理
- 页面导航和事件处理
- 对话框和Toast显示
- 界面状态管理

#### main-module.js
- 主控制模块
- 统一管理所有模块的初始化
- 应用生命周期管理
- 返回键处理和退出逻辑

#### moonbox-module.js
- 月光宝盒功能模块
- 支持阅读、点赞、评论、关注等任务
- 任务配置和进度管理
- 可扩展的任务执行框架

#### daily-task-module.js
- 每日任务功能模块
- 任务列表展示和管理
- 任务执行和状态跟踪
- 完全按照原版实现

#### agent-sales-module.js
- 代理分销功能模块
- 会员列表和推广数据展示
- 推广链接生成和收益查看
- 完全按照原版实现

#### my-points-module.js
- 我的积分功能模块
- 积分数据展示和历史记录
- 积分明细查询和统计
- 完全按照原版实现

#### points-exchange-module.js
- 积分兑换功能模块
- 兑换配置和兑换流程
- 卡密兑换和兑换记录
- 完全按照原版实现

#### promotion-rank-module.js
- 推广排名功能模块
- 排名数据展示和网络请求
- 排行榜查看和个人排名
- 完全按照原版实现

#### about-us-module.js
- 关于我们功能模块
- 对话框展示和后端数据获取
- 集成更多项目、发财交流群、脚本通知群功能
- 完全按照原版实现

#### tutorial-module.js
- 使用教程功能模块
- WebView展示和教程链接加载
- 支持在线教程和离线说明
- 完全按照原版实现

#### withdrawal-module.js
- 账户提现功能模块
- 完整的提现流程和表单验证
- 金额选择、支付宝信息填写、提现记录查看
- 提现说明和状态管理，完全按照原版实现

## 使用方法

### 1. 环境要求
- AutoX.js 4.0+
- Android 6.0+
- 存储权限
- 网络权限

### 2. 安装运行
1. 将整个 `autoScript` 目录复制到设备存储
2. 在AutoX.js中打开 `main.js` 文件
3. 点击运行按钮启动应用

### 3. 配置修改
编辑 `config/constants.js` 文件修改应用配置：
```javascript
// 修改API服务器地址
const API_CONFIG = {
    BASE_URL: "http://your-server:port/api",
    TIMEOUT: 10000
};
```

## 功能特性

### ✅ 已实现功能
- 模块化架构设计
- 日志记录和管理
- 数据持久化存储
- 网络请求处理
- 应用自动更新
- 基础UI框架
- 月光宝盒任务框架

### ✅ 新增完成功能 (模块化重构)
- 每日任务功能 (`modules/daily-task-module.js`)
- 代理分销功能 (`modules/agent-sales-module.js`)
- 我的积分功能 (`modules/my-points-module.js`)
- 积分兑换功能 (`modules/points-exchange-module.js`)
- 推广排名功能 (`modules/promotion-rank-module.js`)
- 关于我们功能 (`modules/about-us-module.js`)
- 使用教程功能 (`modules/tutorial-module.js`)
- 账户提现功能 (`modules/withdrawal-module.js`)

### 🚧 待完善功能
- 月光宝盒具体任务实现

## 开发指南

### 添加新模块
1. 在 `modules/` 目录创建新的模块文件
2. 使用标准的模块模式：
```javascript
const YourModule = (function () {
    return {
        init: function () {
            // 初始化逻辑
        },
        // 其他公共方法
    };
})();

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = YourModule;
} else {
    this.YourModule = YourModule;
}
```
3. 在 `main.js` 中添加模块加载
4. 在 `main-module.js` 中添加初始化调用

### 模块间通信
- 通过全局对象访问其他模块
- 使用回调函数进行异步通信
- 通过StorageModule共享数据

### 错误处理
- 每个模块都应该有完善的错误处理
- 使用LogModule记录错误信息
- 提供用户友好的错误提示

## 注意事项

1. **兼容性**: 代码设计为兼容AutoX.js环境，避免使用不支持的API
2. **权限**: 确保应用有必要的存储和网络权限
3. **路径**: 所有文件路径使用相对路径，便于部署
4. **错误处理**: 每个模块都有独立的错误处理，避免单点故障
5. **日志**: 重要操作都有日志记录，便于调试和维护

## 更新日志

### v2.0.0 (功能模块完整重构版本) - 2025年8月4日
- ✅ 完成所有主要功能模块的1:1重构
- ✅ 实现每日任务、代理分销、积分系统等8个核心模块
- ✅ 完全按照原版功能实现，保持UI和交互逻辑一致
- ✅ 所有模块支持独立运行和错误降级处理
- ✅ 更新所有事件处理为调用新模块
- ✅ 完善的错误处理和用户提示

### v1.0.0 (模块化重构版本)
- 完成模块化架构重构
- 实现基础功能模块
- 添加完整的错误处理
- 支持自动更新功能
- 优化代码结构和可维护性

## 技术支持

如有问题或建议，请通过以下方式联系：
- 查看日志文件：`/sdcard/脚本助手/logs/app.log`
- 检查模块加载状态
- 确认权限配置正确

## 许可证

本项目仅供学习和研究使用，请遵守相关法律法规。
