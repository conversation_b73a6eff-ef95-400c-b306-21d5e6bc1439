/**
 * 我的积分模块
 * 负责处理我的积分相关功能
 */

var MyPointsModule = (function () {
    return {
        /**
         * 创建我的积分界面 - 完全按照原始main.js实现
         */
        createMyPointsUI: function () {
            try {
                // 创建我的积分UI布局 - 完全按照原版
                var pointsPageLayoutXml =
                    '<frame bg="#f5ffe0">' +
                    '<vertical padding="8" h="*">' +
                    '<horizontal gravity="center_vertical" h="50">' +
                    '<button id="backFromPointsBtn" text="返回" textSize="14sp" style="Widget.AppCompat.Button.Borderless" w="60dp" h="40dp"/>' +
                    '<text text="我的积分" textSize="18sp" textColor="#333333" gravity="center" layout_weight="1" textStyle="bold"/>' +
                    '<frame w="60dp" visibility="invisible"/>' +
                    '</horizontal>' +

                    '<scroll layout_weight="1">' +
                    '<vertical padding="10">' +
                    '<card cardCornerRadius="10dp" cardElevation="2dp" margin="5 10">' +
                    '<vertical padding="15">' +
                    '<text text="积分概览" textSize="16sp" textColor="#333333" textStyle="bold"/>' +
                    '<horizontal margin="0 10 0 5">' +
                    '<vertical layout_weight="1" gravity="center">' +
                    '<text id="currentPointsText" text="0" textSize="20sp" textColor="#FF5722" textStyle="bold"/>' +
                    '<text text="当前积分" textSize="14sp" textColor="#666666" marginTop="5"/>' +
                    '</vertical>' +
                    '<vertical layout_weight="1" gravity="center">' +
                    '<text id="totalEarnedPointsText" text="0" textSize="20sp" textColor="#4CAF50" textStyle="bold"/>' +
                    '<text text="累计获得" textSize="14sp" textColor="#666666" marginTop="5"/>' +
                    '</vertical>' +
                    '<vertical layout_weight="1" gravity="center">' +
                    '<text id="usedPointsText" text="0" textSize="20sp" textColor="#2196F3" textStyle="bold"/>' +
                    '<text text="已使用" textSize="14sp" textColor="#666666" marginTop="5"/>' +
                    '</vertical>' +
                    '</horizontal>' +
                    '</vertical>' +
                    '</card>' +

                    '<card cardCornerRadius="10dp" cardElevation="2dp" margin="5 10">' +
                    '<vertical padding="15">' +
                    '<text text="积分记录" textSize="16sp" textColor="#333333" textStyle="bold"/>' +
                    '<vertical id="pointsRecordContainer" margin="0 10">' +
                    '<text text="加载中..." textSize="14sp" textColor="#999999" gravity="center" margin="0 20"/>' +
                    '</vertical>' +
                    '</vertical>' +
                    '</card>' +
                    '</vertical>' +
                    '</scroll>' +
                    '</vertical>' +
                    '</frame>';

                // 使用安全的UI创建方法
                this.safeCreateUI(pointsPageLayoutXml, function () {
                    try {
                        // 设置当前页面状态
                        if (typeof global !== 'undefined' && global.UIModule && global.UIModule.setCurrentPage) {
                            global.UIModule.setCurrentPage("points");
                        }

                        // 绑定事件
                        MyPointsModule.bindEvents();

                        // 加载积分数据
                        MyPointsModule.loadPointsData();

                    } catch (e) {
                        console.error("绑定我的积分界面事件失败: " + e.message);
                        console.error(e.stack);
                    }
                });
            } catch (e) {
                console.error("创建我的积分界面失败: " + e.message);
                if (typeof toast !== 'undefined') {
                    toast("创建我的积分界面失败: " + e.message);
                }
            }
        },

        /**
         * 安全创建UI的方法
         */
        safeCreateUI: function (layoutXml, callback) {
            try {
                if (typeof ui !== 'undefined' && ui.layout) {
                    ui.layout(layoutXml);
                    if (callback && typeof callback === 'function') {
                        // 延迟执行回调，确保UI已完全加载
                        setTimeout(callback, 100);
                    }
                } else {
                    console.error("UI对象不可用");
                    if (typeof toast !== 'undefined') {
                        toast("UI对象不可用");
                    }
                }
            } catch (e) {
                console.error("安全创建UI失败: " + e.message);
                if (typeof toast !== 'undefined') {
                    toast("创建界面失败: " + e.message);
                }
            }
        },

        /**
         * 绑定事件处理
         */
        bindEvents: function () {
            try {
                console.log("开始绑定返回按钮事件");
                // 绑定返回按钮事件
                var backBtn = ui.findById("backFromPointsBtn");
                if (backBtn) {
                    backBtn.on("click", function () {
                        console.log("积分页面返回按钮被点击");
                        // 直接显示"我的"页面，不经过脚本中心
                        if (typeof global !== 'undefined' && global.UIModule && global.UIModule.showMyPageDirectly) {
                            global.UIModule.showMyPageDirectly();
                        } else if (typeof global !== 'undefined' && global.UIModule && global.UIModule.returnToScriptCenterMyTab) {
                            // 降级方案：使用原来的方法
                            global.UIModule.returnToScriptCenterMyTab();
                        } else {
                            console.error("UIModule 导航方法不可用");
                            if (typeof toast !== 'undefined') {
                                toast("返回失败，请重试");
                            }
                        }
                        return true;
                    });
                    console.log("积分页面返回按钮事件绑定成功");
                } else {
                    console.error("找不到积分页面返回按钮元素");
                    if (typeof toast !== 'undefined') {
                        toast("找不到返回按钮，界面可能未正确加载");
                    }
                }

                // 添加物理返回键监听器
                MyPointsModule.setupBackKeyHandler();

            } catch (e) {
                console.error("绑定我的积分事件失败: " + e.message);
            }
        },

        /**
         * 获取用户Token - 与其他模块保持一致的获取逻辑
         */
        getUserToken: function() {
            console.log("我的积分模块：开始获取用户Token...");

            // 优先从StorageModule获取Token（登录时保存的位置）
            if (typeof global !== 'undefined' && global.StorageModule) {
                console.log("我的积分模块：尝试从StorageModule获取Token...");
                var token = global.StorageModule.get("userToken");
                if (token) {
                    console.log("我的积分模块：从StorageModule获取到Token: " + token.substring(0, 10) + "...");
                    return token;
                } else {
                    console.log("我的积分模块：StorageModule中没有找到Token");
                }
            } else {
                console.log("我的积分模块：StorageModule不可用");
            }

            // 降级到ConfigModule
            if (typeof global !== 'undefined' && global.ConfigModule) {
                console.log("我的积分模块：尝试从ConfigModule获取Token...");
                var token = global.ConfigModule.get("userToken");
                if (token) {
                    console.log("我的积分模块：从ConfigModule获取到Token: " + token.substring(0, 10) + "...");
                    return token;
                } else {
                    console.log("我的积分模块：ConfigModule中没有找到Token");
                }
            } else {
                console.log("我的积分模块：ConfigModule不可用");
            }

            // 最后尝试从currentUser全局变量获取
            if (typeof currentUser !== 'undefined' && currentUser && currentUser.token) {
                console.log("我的积分模块：从currentUser获取到Token: " + currentUser.token.substring(0, 10) + "...");
                return currentUser.token;
            } else {
                console.log("我的积分模块：currentUser中没有Token");
            }

            console.warn("我的积分模块：未能获取到用户Token");
            return null;
        },

        /**
         * 加载积分数据
         */
        loadPointsData: function () {
            try {
                console.log("开始加载积分数据...");
                var userPhone = null;
                var token = this.getUserToken();

                console.log("我的积分模块：获取到Token: " + (token ? token.substring(0, 10) + "..." : "null"));

                // 获取用户手机号 - 添加详细调试信息
                console.log("我的积分模块：开始获取用户手机号...");

                if (typeof global !== 'undefined' && global.StorageModule) {
                    userPhone = global.StorageModule.get("userPhone");
                }

                if (!userPhone && typeof global !== 'undefined' && global.ConfigModule) {
                    userPhone = global.ConfigModule.get("userPhone");
                }

                if (!userPhone && typeof currentUser !== 'undefined' && currentUser && currentUser.phone) {
                    userPhone = currentUser.phone;
                }


                if (!userPhone || !token) {
                    if (typeof toast !== 'undefined') {
                        toast("用户信息不完整，请先登录");
                    }
                    if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                        global.LogModule.log("加载积分数据失败：用户信息不完整 - userPhone: " + userPhone + ", token: " + (token ? "存在" : "不存在"), "ERROR");
                    }
                    return;
                }

                // 调用API获取积分数据
                if (typeof global !== 'undefined' && global.NetworkModule && global.NetworkModule.post) {
                    global.NetworkModule.post("/points/external/user", { phone: userPhone }, function (error, result) {
                        if (error) {
                            console.error("获取积分数据失败: " + error.message);
                            if (typeof toast !== 'undefined') {
                                toast("获取积分数据失败，请检查网络连接");
                            }
                            if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                                global.LogModule.log("获取积分数据失败: " + error.message, "ERROR");
                            }
                            return;
                        }

                        if (result && result.code === 200 && result.data) {
                            console.log("积分数据获取成功，开始更新UI");
                            MyPointsModule.updatePointsUI(result.data);
                        } else {
                            console.error("获取积分数据失败: " + (result ? result.message : "未知错误"));
                            if (typeof toast !== 'undefined') {
                                toast("获取积分数据失败: " + (result ? result.message : "未知错误"));
                            }
                            if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                                global.LogModule.log("获取积分数据失败: " + (result ? result.message : "未知错误"), "ERROR");
                            }
                        }
                    }, token);
                } else {
                    console.error("NetworkModule 不可用");
                    // 使用默认数据
                    var defaultData = {
                        userPoints: {
                            points: 0,
                            totalEarnedPoints: 0,
                            totalConsumedPoints: 0
                        },
                        pointsRecords: []
                    };
                    MyPointsModule.updatePointsUI(defaultData);
                }

            } catch (e) {
                console.error("加载积分数据失败: " + e.message);
                console.error(e.stack);
                if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                    global.LogModule.log("加载积分数据失败: " + e.message, "ERROR");
                }
                if (typeof toast !== 'undefined') {
                    toast("加载积分数据失败: " + e.message);
                }
            }
        },

        /**
         * 更新积分UI内容
         */
        updatePointsUI: function (pointsData) {
            try {
                // 更新UI - 使用ui.run确保在UI线程中执行
                if (typeof ui !== 'undefined' && ui.run) {
                    ui.run(function () {
                        try {
                            var userPoints = pointsData.userPoints || {};
                            var pointsRecords = pointsData.pointsRecords || [];

                            // 更新积分概览
                            var currentPointsText = ui.findById("currentPointsText");
                            var totalEarnedPointsText = ui.findById("totalEarnedPointsText");
                            var usedPointsText = ui.findById("usedPointsText");

                            if (currentPointsText) {
                                currentPointsText.setText(userPoints.points ? userPoints.points.toString() : "0");
                            }

                            if (totalEarnedPointsText) {
                                totalEarnedPointsText.setText(userPoints.totalEarnedPoints ? userPoints.totalEarnedPoints.toString() : "0");
                            }

                            if (usedPointsText) {
                                usedPointsText.setText(userPoints.totalConsumedPoints ? userPoints.totalConsumedPoints.toString() : "0");
                            }

                            // 更新积分记录
                            var recordContainer = ui.findById("pointsRecordContainer");
                            if (!recordContainer) {
                                console.error("找不到积分记录容器");
                                if (typeof toast !== 'undefined') {
                                    toast("无法加载积分记录，容器不存在");
                                }
                                return;
                            }

                            // 清空容器
                            recordContainer.removeAllViews();
                            console.log("已清空积分记录容器");

                            // 创建并添加记录项
                            if (pointsRecords && pointsRecords.length > 0) {
                                console.log("开始添加" + pointsRecords.length + "条积分记录");

                                // 创建简单的字符串模板
                                var recordTemplate =
                                    '<horizontal padding="10 15" bg="#ffffff" margin="0 5">' +
                                    '<vertical layout_weight="1">' +
                                    '<text text="%description%" textSize="14sp" textColor="#333333"/>' +
                                    '<text text="%time%" textSize="12sp" textColor="#999999" marginTop="3"/>' +
                                    '</vertical>' +
                                    '<text text="%amount%" textSize="16sp" textColor="%color%" textStyle="bold"/>' +
                                    '</horizontal>';

                                for (var i = 0; i < pointsRecords.length; i++) {
                                    try {
                                        var record = pointsRecords[i];
                                        var isPositive = record.pointsChange > 0;
                                        var amountText = (isPositive ? "+" : "") + record.pointsChange;
                                        var color = isPositive ? "#4CAF50" : "#F44336";

                                        // 替换模板中的占位符
                                        var itemXml = recordTemplate
                                            .replace("%description%", record.description || record.operationTypeName || "积分变动")
                                            .replace("%time%", record.createTime || "")
                                            .replace("%amount%", amountText)
                                            .replace("%color%", color);

                                        // 创建记录项视图
                                        var recordView = ui.inflate(itemXml, recordContainer);
                                        recordContainer.addView(recordView);

                                        console.log("已添加第" + (i + 1) + "条积分记录");
                                    } catch (itemError) {
                                        console.error("添加积分记录项失败: " + itemError.message);
                                    }
                                }
                                console.log("积分记录添加完成");
                            } else {
                                // 没有记录时显示提示
                                var emptyView = ui.inflate(
                                    '<text text="暂无积分记录" textSize="14sp" textColor="#999999" gravity="center" margin="0 20"/>',
                                    recordContainer
                                );
                                recordContainer.addView(emptyView);
                                console.log("已显示暂无积分记录提示");
                            }

                            if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                                global.LogModule.log("积分数据加载完成");
                            }
                            console.log("积分数据UI更新完成");
                        } catch (uiError) {
                            console.error("更新积分UI失败: " + uiError.message);
                            console.error(uiError.stack);
                            if (typeof global !== 'undefined' && global.LogModule && global.LogModule.log) {
                                global.LogModule.log("更新积分UI失败: " + uiError.message, "ERROR");
                            }
                            if (typeof toast !== 'undefined') {
                                toast("更新积分UI失败，请重试");
                            }
                        }
                    });
                }
            } catch (e) {
                console.error("更新积分UI失败: " + e.message);
            }
        },

        /**
         * 设置物理返回键处理
         */
        setupBackKeyHandler: function () {
            try {
                // 监听返回键事件
                if (typeof ui !== 'undefined' && ui.emitter) {
                    ui.emitter.on("back_pressed", function (e) {
                        try {
                            // 检查当前是否在我的积分页面
                            if (typeof global !== 'undefined' && global.UIModule && global.UIModule.getCurrentPage) {
                                var currentPage = global.UIModule.getCurrentPage();
                                if (currentPage === "points") {
                                    // 阻止默认返回行为
                                    e.consumed = true;

                                    // 执行返回操作 - 直接显示"我的"页面
                                    if (global.UIModule.showMyPageDirectly) {
                                        global.UIModule.showMyPageDirectly();
                                    } else if (global.UIModule.returnToScriptCenterMyTab) {
                                        // 降级方案
                                        global.UIModule.returnToScriptCenterMyTab();
                                    } else {
                                        console.error("UIModule 导航方法不可用");
                                        if (typeof toast !== 'undefined') {
                                            toast("返回失败，请重试");
                                        }
                                    }
                                }
                            }
                        } catch (error) {
                            console.error("处理返回键事件失败: " + error.message);
                        }
                    });
                }

                // 监听按键事件（备用方案）
                if (typeof events !== 'undefined') {
                    events.on("key_down", function (keyCode, event) {
                        try {
                            if (keyCode === keys.back) {
                                // 检查当前是否在我的积分页面
                                if (typeof global !== 'undefined' && global.UIModule && global.UIModule.getCurrentPage) {
                                    var currentPage = global.UIModule.getCurrentPage();
                                    if (currentPage === "points") {
                                        // 执行返回操作 - 直接显示"我的"页面
                                        if (global.UIModule.showMyPageDirectly) {
                                            global.UIModule.showMyPageDirectly();
                                        } else if (global.UIModule.returnToScriptCenterMyTab) {
                                            global.UIModule.returnToScriptCenterMyTab();
                                        }
                                        return true; // 阻止默认行为
                                    }
                                }
                            }
                        } catch (error) {
                            console.error("处理按键事件失败: " + error.message);
                        }
                        return false;
                    });
                }

                console.log("我的积分页面返回键处理设置完成");
            } catch (e) {
                console.error("设置返回键处理失败: " + e.message);
            }
        }
    };
})();

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MyPointsModule;
} else if (typeof global !== 'undefined') {
    global.MyPointsModule = MyPointsModule;
}
